import java.util.Properties

val localProperties = Properties().apply {
    val file = project.rootProject.file("local.properties")
    if (file.exists()) {
        load(file.inputStream())
    }
}

val libraryVersionProp = "LIBRARY_VERSION"
val libraryVersion = if (project.hasProperty(libraryVersionProp)) {
    project.property(libraryVersionProp) as String
} else {
    localProperties.getProperty(libraryVersionProp) ?: "1.0.0"
}
subprojects {
    version = libraryVersion
}

task("publishAndroidToMavenLocal") {
    dependsOn(getTasksByName("publishKotlinMultiplatformPublicationToMavenLocal", true))
    dependsOn(getTasksByName("publishAndroidReleasePublicationToMavenLocal", true))
    dependsOn(getTasksByName("publishAndroidDebugPublicationToMavenLocal", true))
}
