rootProject.name = "OGAppKitSDK"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    includeBuild("ogappkit/l10n/processor")

    listOf(
        repositories,
        dependencyResolutionManagement.repositories,
        buildscript.repositories,
    ).forEach { repositories ->
        repositories.apply {
            google()
            mavenCentral()
            gradlePluginPortal()
            mavenLocal()
            maven("https://plugins.gradle.org/m2/")
            maven {
                name = "GitHubPackages"
                url =
                    uri("https://maven.pkg.github.com/aacml/og-dx_aac-android-module-otto_group_kit")
                credentials {
                    val props = extensions.extraProperties.properties
                    username =
                        props["gpr.user"] as? String? ?: System.getenv("GH_PACKAGES_USERNAME")
                    password = props["gpr.key"] as? String? ?: System.getenv("GH_PACKAGES_TOKEN")
                }
            }
            maven("https://jitpack.io")
        }
    }
}

include(":ogappkit:base:api")
include(":ogappkit:base:internal")
include(":ogappkit:sdk")
include(":ogappkit:nativeui:api")
include(":ogappkit:nativeui:internal")
include(":ogappkit:tracking:api")
include(":ogappkit:tracking:processor")
include(":ogappkit:deals:api")
include(":ogappkit:deals:internal")
include(":ogappkit:l10n:api")
include(":ogappkit:l10n:internal")
include(":ogappkit:l10n:processor")
include(":ogappkit:kotlinbetterserializable")
include(":ogappkit:coordinator:api")
include(":ogappkit:coordinator:internal")
