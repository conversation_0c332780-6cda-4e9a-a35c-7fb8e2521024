// swift-tools-version:5.8
import PackageDescription

let packageName = "OGAppKitSDK"

let package = Package(
    name: packageName,
    platforms: [
        .iOS(.v13)
    ],
    products: [
        .library(
            name: packageName,
            targets: [packageName]
        ),
    ],
    targets: [
        .binaryTarget(
            name: packageName,
            path: "./ogappkit/sdk/build/XCFrameworks/debug/\(packageName).xcframework"
        )
        ,
    ]
)