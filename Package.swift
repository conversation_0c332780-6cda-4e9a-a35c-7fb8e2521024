// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.
import CompilerPluginSupport
import PackageDescription

let package = Package(
  name: "OGKit",
  platforms: [
    .iOS(.v15),
    .macOS(.v12)
  ],
  products: [
    .library(
      name: "OGKit",
      targets: ["OGKit"]
    ),
    .library(
      name: "OGAppEnvironment",
      targets: ["OGAppEnvironment"]
    ),
    .library(
      name: "OGAdjustReporter",
      targets: ["OGAdjustReporter"]
    ),
    .library(
      name: "OGAirshipReporter",
      targets: ["OGAirshipReporter"]
    ),
    .library(
      name: "OGBackports",
      targets: ["OGBackports"]
    ),
    .library(
      name: "OGBadge",
      targets: ["OGBadge"]
    ),
    .library(
      name: "<PERSON>GCore",
      targets: ["OGCore"]
    ),
    .library(
      name: "OGCopyCodeBanner",
      targets: ["OGCopyCodeBanner"]
    ),
    .library(
      name: "OGDeepLinkHandler",
      targets: ["OGDeepLinkHandler"]
    ),
    .library(
      name: "OGDialogCoordinator",
      targets: ["OGDialogCoordinator"]
    ),
    .library(
      name: "OGDIService",
      targets: ["OGDIService"]
    ),
    .library(
      name: "OGSwiftConcurrencyExtras",
      targets: ["OGSwiftConcurrencyExtras"]
    ),
    .library(
      name: "OGDomainStore",
      targets: ["OGDomainStore"]
    ),
    .library(
      name: "OGAirshipKit",
      targets: ["OGAirshipKit"]
    ),
    .library(
      name: "OGExternalBrowser",
      targets: ["OGExternalBrowser"]
    ),
    .library(
      name: "OGFirebaseKit",
      targets: ["OGFirebaseKit"]
    ),
    .library(
      name: "OGHTTPClient",
      targets: ["OGHTTPClient"]
    ),
    .library(
      name: "OGInAppBrowser",
      targets: ["OGInAppBrowser"]
    ),
    .library(
      name: "OGFeatureKit",
      targets: ["OGFeatureKit"]
    ),
    .library(
      name: "OGBundledFeatureSetFetcher",
      targets: ["OGBundledFeatureSetFetcher"]
    ),
    .library(
      name: "OGFeatureAdapter",
      targets: ["OGFeatureAdapter"]
    ),
    .library(
      name: "OGFeatureConfigView",
      targets: ["OGFeatureConfigView"]
    ),
    .library(
      name: "OGFeatureCore",
      targets: ["OGFeatureCore"]
    ),
    .library(
      name: "OGFeatureManager",
      targets: ["OGFeatureManager"]
    ),
    .library(
      name: "OGRemoteFeatureSetFetcher",
      targets: ["OGRemoteFeatureSetFetcher"]
    ),
    .library(
      name: "OGScreenViewUpdate",
      targets: ["OGScreenViewUpdate"]
    ),
    .library(
      name: "OGSearch",
      targets: ["OGSearch"]
    ),
    .library(
      name: "OGSystemKit",
      targets: ["OGSystemKit"]
    ),
    .library(
      name: "OGSystemKitTestsUtils",
      targets: ["OGSystemKitTestsUtils"]
    ),
    .library(
      name: "OGTenantCore",
      targets: ["OGTenantCore"]
    ),
    .library(
      name: "OGDetectiveComponent",
      targets: ["OGDetectiveComponent"]
    ),
    .library(
      name: "OGTenantKit",
      targets: ["OGTenantKit"]
    ),
    .library(
      name: "OGTenantSwitch",
      targets: ["OGTenantSwitch"]
    ),
    .library(
      name: "OGTestEnvironmentKit",
      targets: ["OGTestEnvironmentKit"]
    ),
    .library(
      name: "OGDetective",
      targets: ["OGDetective"]
    ),
    .library(
      name: "OGNetworkLogger",
      targets: ["OGNetworkLogger"]
    ),
    .library(
      name: "OGL10n",
      targets: ["OGL10n"]
    ),
    .library(
      name: "OGL10nTestsUtils",
      targets: ["OGL10nTestsUtils"]
    ),
    .library(
      name: "OGExternalDependencies",
      targets: ["OGExternalDependencies"]
    ),
    .library(
      name: "OGIdentifier",
      targets: ["OGIdentifier"]
    ),
    .library(
      name: "OGStorage",
      targets: ["OGStorage"]
    ),
    .library(
      name: "OGLogger",
      targets: ["OGLogger"]
    ),
    .library(
      name: "OGSecret",
      targets: ["OGSecret"]
    ),
    .library(
      name: "OGTracker",
      targets: ["OGTracker"]
    ),
    .library(
      name: "OGViewStore",
      targets: ["OGViewStore"]
    ),
    .library(
      name: "OGRouter",
      targets: ["OGRouter"]
    ),
    .library(
      name: "OGUserCore",
      targets: ["OGUserCore"]
    ),
    .library(
      name: "OGURLCredentialStorage",
      targets: ["OGURLCredentialStorage"]
    ),
    .library(
      name: "OGWebBridge",
      targets: ["OGWebBridge"]
    ),
    .library(
      name: "OGWebView",
      targets: ["OGWebView"]
    ),
    .library(
      name: "OGSalutation",
      targets: ["OGSalutation"]
    ),
    .library(
      name: "OGLoginButton",
      targets: ["OGLoginButton"]
    ),
    .library(
      name: "OGAppLifecycle",
      targets: ["OGAppLifecycle"]
    ),
    .library(
      name: "OGMacros",
      targets: ["OGMacros"]
    ),
    .library(
      name: "OGNavigation",
      targets: ["OGNavigation"]
    ),
    .library(
      name: "OGNavigationCore",
      targets: ["OGNavigationCore"]
    ),
    .library(
      name: "OGZipArchiver",
      targets: ["OGZipArchiver"]
    ),
    .library(
      name: "OGAdjustReporterTestsUtils",
      targets: ["OGAdjustReporterTestsUtils"]
    ),
    .library(
      name: "OGAppEnvironmentTestsUtils",
      targets: ["OGAppEnvironmentTestsUtils"]
    ),
    .library(
      name: "OGBadgeTestsUtils",
      targets: ["OGBadgeTestsUtils"]
    ),
    .library(
      name: "OGCoreTestsUtils",
      targets: ["OGCoreTestsUtils"]
    ),
    .library(
      name: "OGCopyCodeBannerTestsUtils",
      targets: ["OGCopyCodeBannerTestsUtils"]
    ),
    .library(
      name: "OGDetectiveTestsUtils",
      targets: ["OGDetectiveTestsUtils"]
    ),
    .library(
      name: "OGDialogCoordinatorTestsUtils",
      targets: ["OGDialogCoordinatorTestsUtils"]
    ),
    .library(
      name: "OGDomainStoreTestsUtils",
      targets: ["OGDomainStoreTestsUtils"]
    ),
    .library(
      name: "OGViewStoreTestsUtils",
      targets: ["OGViewStoreTestsUtils"]
    ),
    .library(
      name: "OGFeatureAdapterTestsUtils",
      targets: ["OGFeatureAdapterTestsUtils"]
    ),
    .library(
      name: "OGFeatureManagerTestsUtils",
      targets: ["OGFeatureManagerTestsUtils"]
    ),
    .library(
      name: "OGAirshipKitTestsUtils",
      targets: ["OGAirshipKitTestsUtils"]
    ),
    .library(
      name: "OGFirebaseKitTestsUtils",
      targets: ["OGFirebaseKitTestsUtils"]
    ),
    .library(
      name: "OGHTTPClientTestsUtils",
      targets: ["OGHTTPClientTestsUtils"]
    ),
    .library(
      name: "OGIdentifierTestsUtils",
      targets: ["OGIdentifierTestsUtils"]
    ),
    .library(
      name: "OGLoggerTestsUtils",
      targets: ["OGLoggerTestsUtils"]
    ),
    .library(
      name: "OGMock",
      targets: ["OGMock"]
    ),
    .library(
      name: "OGNavigationTestsUtils",
      targets: ["OGNavigationTestsUtils"]
    ),
    .library(
      name: "OGNavigationCoreTestsUtils",
      targets: ["OGNavigationCoreTestsUtils"]
    ),
    .library(
      name: "OGNavigationBar",
      targets: ["OGNavigationBar"]
    ),
    .library(
      name: "OGRouterTestsUtils",
      targets: ["OGRouterTestsUtils"]
    ),
    .library(
      name: "OGSecretTestsUtils",
      targets: ["OGSecretTestsUtils"]
    ),
    .library(
      name: "OGStorageTestsUtils",
      targets: ["OGStorageTestsUtils"]
    ),
    .library(
      name: "OGTenantKitTestsUtils",
      targets: ["OGTenantKitTestsUtils"]
    ),
    .library(
      name: "OGTenantSwitchTestsUtils",
      targets: ["OGTenantSwitchTestsUtils"]
    ),
    .library(
      name: "OGTenantCoreTestsUtils",
      targets: ["OGTenantCoreTestsUtils"]
    ),
    .library(
      name: "OGTestEnvironmentKitTestsUtils",
      targets: ["OGTestEnvironmentKitTestsUtils"]
    ),
    .library(
      name: "OGTrackerTestsUtils",
      targets: ["OGTrackerTestsUtils"]
    ),
    .library(
      name: "OGTrackerCoreTestsUtils",
      targets: ["OGTrackerCoreTestsUtils"]
    ),
    .library(
      name: "OGTrackerOptInService",
      targets: ["OGTrackerOptInService"]
    ),
    .library(
      name: "OGTrackerOptInServiceTestsUtils",
      targets: ["OGTrackerOptInServiceTestsUtils"]
    ),
    .library(
      name: "OGUserAgent",
      targets: ["OGUserAgent"]
    ),
    .library(
      name: "OGUserCoreTestsUtils",
      targets: ["OGUserCoreTestsUtils"]
    ),
    .library(
      name: "OGURLCredentialStorageTestsUtils",
      targets: ["OGURLCredentialStorageTestsUtils"]
    ),
    .library(
      name: "OGWebBridgeTestsUtils",
      targets: ["OGWebBridgeTestsUtils"]
    ),
    .library(
      name: "OGWebViewTestsUtils",
      targets: ["OGWebViewTestsUtils"]
    ),
    .library(
      name: "OGSalutationTestsUtils",
      targets: ["OGSalutationTestsUtils"]
    ),
    .library(
      name: "OGWebBridgeTracker",
      targets: ["OGWebBridgeTracker"]
    ),
    .library(
      name: "OGWebBridgeTrackerTestsUtils",
      targets: ["OGWebBridgeTrackerTestsUtils"]
    ),
    .library(
      name: "PluginsBundle",
      targets: ["PluginsBundle"]
    ),
    .plugin(
      name: "SecretServiceBuildPlugin",
      targets: ["SecretServiceBuildPlugin"]
    )
  ],
  dependencies: [
    .package(
      url: "https://github.com/apple/swift-http-types.git",
      exact: "1.0.2"
    ),
    .package(
      url: "https://github.com/apple/swift-syntax.git",
      exact: "509.0.2"
    ),
    .package(
      url: "https://github.com/ZipArchive/ZipArchive.git",
      exact: "2.4.3"
    ),
    .package(
      url: "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git",
      exact: "6.3.0-10"
    ),
    .package(
      url: "https://github.com/snowplow/snowplow-ios-tracker",
      exact: "6.0.7"
    ),
    .package(
      url: "https://github.com/hmlongco/Factory",
      exact: "2.2.0"
    ),
    .package(url: "https://github.com/pointfreeco/swift-concurrency-extras", exact: "1.1.0"),
    .package(
      url: "https://github.com/apple/swift-async-algorithms",
      from: "0.0.4"
    ),
    .package(
      url: "https://github.com/adjust/ios_sdk",
      exact: "5.4.1"
    ),
    .package(
      url: "https://github.com/urbanairship/ios-library",
      exact: "17.7.3"
    ),
    .package(
      url: "https://github.com/firebase/firebase-ios-sdk",
      exact: "11.13.0"
    ),
    .package(
      url: "https://github.com/johnpatrickmorgan/NavigationBackport",
      exact: "0.9.0"
    ),
    .package(
      url: "https://github.com/shaps80/SwiftUIBackports.git",
      exact: "2.8.0"
    )
  ],
  targets: [
    .target(
      name: "OGKit",
      dependencies: [
        "OGAdjustReporter", "OGL10n", "OGL10nTestsUtils", "OGAirshipReporter", "OGAirshipKit", "OGAppEnvironment", "OGAppLifecycle",
        "OGBadge", "OGBackports", "OGBundledFeatureSetFetcher", "OGCore", "OGCopyCodeBanner", "OGDetectiveComponent",
        "OGDetective", "OGDeepLinkHandler", "OGDialogCoordinator", "OGDIService",
        "OGDomainStore", "OGExternalBrowser", "OGFeatureAdapter", "OGFeatureConfigView",
        "OGFeatureCore", "OGFeatureKit", "OGFeatureManager", "OGFirebaseKit",
        "OGHTTPClient", "OGIdentifier", "OGInAppBrowser", "OGExternalDependencies",
        "OGLogger", "OGLoginButton", "OGMacros", "OGNavigation", "OGNavigationBar",
        "OGNavigationCore", "OGNetworkLogger", "OGRemoteFeatureSetFetcher", "OGRouter",
        "OGSearch", "OGSecret", "OGSalutation", "OGStorage", "OGSystemKit", "OGViewStore",
        "OGTenantCore", "OGTestEnvironmentKit", "OGTenantKit", "OGTenantSwitch", "OGTracker",
        "OGTrackerOptInService", "OGScreenViewUpdate", "OGWebBridge", "OGWebView", "OGSwiftConcurrencyExtras",
        "OGUserCore", "OGURLCredentialStorage", "OGWebBridgeTracker", "OGZipArchiver",
        "OGAdjustReporterTestsUtils", "OGAirshipKitTestsUtils", "OGAppEnvironmentTestsUtils", "OGBadgeTestsUtils",
        "OGCoreTestsUtils", "OGCopyCodeBannerTestsUtils", "OGDetectiveTestsUtils", "OGDialogCoordinatorTestsUtils",
        "OGDomainStoreTestsUtils", "OGViewStoreTestsUtils", "OGFirebaseKitTestsUtils", "OGFeatureAdapterTestsUtils",
        "OGFeatureManagerTestsUtils", "OGHTTPClientTestsUtils", "OGIdentifierTestsUtils",
        "OGLoggerTestsUtils", "OGNavigationTestsUtils", "OGNavigationCoreTestsUtils", "OGRouterTestsUtils", "OGSalutationTestsUtils", "OGStorageTestsUtils",
        "OGSecretTestsUtils", "OGSystemKitTestsUtils", "OGTenantCoreTestsUtils", "OGTenantKitTestsUtils",
        "OGTenantSwitchTestsUtils", "OGTrackerTestsUtils", "OGTrackerCoreTestsUtils", "OGTrackerOptInServiceTestsUtils",
        "OGTestEnvironmentKitTestsUtils", "OGWebBridgeTrackerTestsUtils", "OGURLCredentialStorageTestsUtils",
        "OGUserCoreTestsUtils", "OGWebBridgeTestsUtils", "OGWebViewTestsUtils", "OGUserAgent"
      ],
      path: "Source",
      resources: [.process("Resources/PrivacyInfo.xcprivacy")]
    ),
    .target(
      name: "OGAppEnvironment",
      path: "Packages/OGCore/Packages/OGAppEnvironment/Source"
    ),
    .target(
      name: "OGAppEnvironmentTestsUtils",
      dependencies: ["OGAppEnvironment"],
      path: "Packages/OGCore/Packages/OGAppEnvironment/TestsUtils"
    ),
    .target(
      name: "OGAppLifecycle",
      dependencies: ["OGCore", "OGDomainStore", "OGDIService"],
      path: "Packages/OGAppLifecycle/Sources"
    ),
    .target(
      name: "OGBackports",
      dependencies: ["SwiftUIBackports"],
      path: "Packages/OGExternalDependencies/OGBackports/Sources/OGBackports"
    ),
    .target(
      name: "OGBadge",
      dependencies: ["OGDIService", "OGWebBridge", "OGNavigationCore", "OGUserCore"],
      path: "Packages/OGBadge/Sources"
    ),
    .target(
      name: "OGCore",
      dependencies: [
        "OGAppEnvironment",
        "OGIdentifier",
        "OGMacros",
        "OGStorage",
        "OGLogger",
        "OGDIService",
        "OGZipArchiver"
      ],
      path: "Packages/OGCore/Sources"
    ),
    .target(
      name: "OGCopyCodeBanner",
      dependencies: [
        "OGCore",
        "OGExternalDependencies",
        "OGDialogCoordinator",
        "OGDIService",
        "OGFeatureKit",
        "OGScreenViewUpdate"
      ],
      path: "Packages/OGCopyCodeBanner/Sources"
    ),
    .target(
      name: "OGUserAgent",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGDomainStore"
      ],
      path: "Packages/OGUserAgent/Sources/OGUserAgent"
    ),
    .target(
      name: "OGUserCore",
      dependencies: [
        "OGAirshipKit",
        "OGCore",
        "OGDIService",
        "OGDomainStore",
        "OGWebBridge"
      ],
      path: "Packages/OGUserCore/Sources/OGUserCore"
    ),
    .target(
      name: "OGURLCredentialStorage",
      dependencies: [
        "OGDIService",
        "OGCore",
        "OGFeatureAdapter",
        "OGFeatureCore"
      ],
      path: "Packages/OGURLCredentialStorage/Sources/OGURLCredentialStorage"
    ),
    .target(
      name: "OGL10n",
      dependencies: [
        "OGCore",
        "OGFirebaseKit",
        "OGDIService",
        "OGTenantKit",
        "OGTenantCore"
      ],
      path: "Packages/OGL10n/Sources/OGL10n"
    ),
    .target(
      name: "OGL10nTestsUtils",
      dependencies: [
        "OGL10n"
      ],
      path: "Packages/OGL10n/TestsUtils"
    ),
    .target(
      name: "OGExternalDependencies",
      dependencies: [
        "ZipArchive",
        .product(name: "OGAppKitSDK", package: "og-dx_aac-multiplatform-sdk"),
        .product(name: "SnowplowTracker", package: "snowplow-ios-tracker"),
        .product(name: "FirebaseAnalytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseCrashlytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseRemoteConfig", package: "firebase-ios-sdk"),
        .product(name: "FirebaseFirestore", package: "firebase-ios-sdk"),
        .product(name: "FirebasePerformance", package: "firebase-ios-sdk")

      ],
      path: "Packages/OGExternalDependencies/Sources/OGExternalDependencies"
    ),
    .target(
      name: "OGDetective",
      dependencies: [
        "OGCore",
        "OGNetworkLogger",
        "ZipArchive",
        "OGDomainStore",
        "OGViewStore",
        .product(name: "OGAppKitSDK", package: "og-dx_aac-multiplatform-sdk")
      ],
      path: "Packages/OGDetective/Sources/OGDetective",
      resources: [.process("Resources")]
    ),
    .target(
      name: "OGDeepLinkHandler",
      dependencies: ["OGCore", "OGRouter", "OGDIService", "OGFeatureAdapter"],
      path: "Packages/OGDeepLinkHandler/Sources/OGDeepLinkHandler"
    ),
    .target(
      name: "OGDialogCoordinator",
      dependencies: [
        "OGDomainStore",
        "OGRouter",
        "OGWebBridge",
        "OGDIService",
        "OGFeatureAdapter",
        "OGFeatureCore",
        "OGScreenViewUpdate",
        "OGSystemKit"
      ],
      path: "Packages/OGDialogCoordinator/Sources/OGDialogCoordinator"
    ),
    .target(
      name: "OGScreenViewUpdate",
      dependencies: ["OGDIService"],
      path: "Packages/OGDialogCoordinator/Packages/OGScreenViewUpdate/Sources/OGScreenViewUpdate"
    ),
    .target(
      name: "OGAirshipKit",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGSecret",
        "OGRouter",
        "OGDeepLinkHandler",
        "OGViewStore",
        "OGDomainStore",
        "OGTenantKit",
        .product(name: "AirshipCore", package: "ios-library"),
        .product(name: "AirshipAutomation", package: "ios-library"),
        .product(name: "AirshipMessageCenter", package: "ios-library")
      ],
      path: "Packages/OGAirshipKit/Sources/OGAirshipKit"
    ),
    .target(
      name: "OGExternalBrowser",
      dependencies: [
        "OGCore",
        "OGRouter",
        "OGDIService",
        "OGFeatureAdapter"
      ],
      path: "Packages/OGExternalBrowser/Sources/OGExternalBrowser"
    ),
    .target(
      name: "OGFirebaseKit",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGFeatureCore",
        "OGTrackerOptInService",
        .product(name: "FirebaseRemoteConfig", package: "firebase-ios-sdk"),
        .product(name: "FirebaseCrashlytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseAnalytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseFirestore", package: "firebase-ios-sdk")
      ],
      path: "Packages/OGFirebaseKit/Sources/OGFirebaseKit"
    ),
    .target(
      name: "OGHTTPClient",
      dependencies: [
        "OGDIService",
        "OGUserAgent",
        .product(name: "HTTPTypes", package: "swift-http-types")
      ],
      path: "Packages/OGHTTPClient/Sources/OGHTTPClient"
    ),
    .target(
      name: "OGInAppBrowser",
      dependencies: [
        "OGCore",
        "OGRouter",
        "OGDIService",
        "OGFeatureAdapter"
      ],
      path: "Packages/OGInAppBrowser/Sources/OGInAppBrowser"
    ),
    .target(
      name: "OGNetworkLogger",
      dependencies: ["OGDIService"],
      path: "Packages/OGNetworkLogger/Sources/OGNetworkLogger"
    ),
    .target(
      name: "OGNavigationBar",
      dependencies: ["OGNavigationCore", "OGDIService", "OGSearch", "OGRouter", "OGCore", "OGViewStore"],
      path: "Packages/OGNavigationBar/Sources/OGNavigationBar"
    ),

    .target(
      name: "OGDIService",
      dependencies: ["Factory"],
      path: "Packages/OGExternalDependencies/OGDIService/Sources/OGDIService"
    ),
    .target(
      name: "OGSwiftConcurrencyExtras",
      dependencies: [.product(name: "ConcurrencyExtras", package: "swift-concurrency-extras")],
      path: "Packages/OGExternalDependencies/OGSwiftConcurrencyExtras/Sources/OGSwiftConcurrencyExtras"
    ),
    .target(
      name: "OGDomainStore",
      dependencies: [
        .product(
          name: "AsyncAlgorithms",
          package: "swift-async-algorithms"
        )
      ],
      path: "Packages/OGDomainStore/Sources/OGDomainStore"
    ),
    .target(
      name: "OGIdentifier",
      path: "Packages/OGCore/Packages/OGIdentifier/Sources/OGIdentifier"
    ),
    .target(
      name: "OGFeatureKit",
      dependencies: [
        "OGBundledFeatureSetFetcher",
        "OGFeatureAdapter",
        "OGFeatureCore",
        "OGFeatureConfigView",
        "OGFeatureManager",
        "OGTenantCore",
        "OGTenantKit",
        "OGTestEnvironmentKit",
        "OGRemoteFeatureSetFetcher"
      ],
      path: "Packages/OGFeatureKit/Source"
    ),
    .target(
      name: "OGBundledFeatureSetFetcher",
      dependencies: [
        "OGFeatureCore",
        "OGFeatureManager",
        "OGTenantKit",
        "OGDIService"
      ],
      path: "Packages/OGFeatureKit/Packages/OGBundledFeatureSetFetcher/Sources"
    ),
    .target(
      name: "OGFeatureAdapter",
      dependencies: [
        "OGCore",
        "OGFeatureCore",
        "OGFeatureManager",
        "OGDIService"
      ],
      path: "Packages/OGFeatureKit/Packages/OGFeatureAdapter/Sources"
    ),
    .target(
      name: "OGFeatureConfigView",
      dependencies: [
        "OGCore",
        "OGDetective",
        "OGFeatureCore",
        "OGFeatureManager",
        "OGDIService"
      ],
      path: "Packages/OGFeatureKit/Packages/OGFeatureConfigView/Sources"
    ),
    .target(
      name: "OGFeatureCore",
      dependencies: ["OGIdentifier"],
      path: "Packages/OGFeatureKit/Packages/OGFeatureCore/Sources"
    ),
    .target(
      name: "OGFeatureManager",
      dependencies: [
        "OGCore",
        "OGFeatureCore",
        "OGTenantCore",
        "OGDIService"
      ],
      path: "Packages/OGFeatureKit/Packages/OGFeatureManager/Source"
    ),
    .target(
      name: "OGRemoteFeatureSetFetcher",
      dependencies: [
        "OGFirebaseKit",
        "OGDIService",
        "OGTenantKit",
        "OGFeatureManager",
        "OGFeatureCore",
        "OGCore",
        "OGTenantCore"
      ],
      path: "Packages/OGFeatureKit/Packages/OGRemoteFeatureSetFetcher/Sources"
    ),
    .target(
      name: "OGTenantCore",
      dependencies: ["OGCore"],
      path: "Packages/OGFeatureKit/Packages/OGTenantCore/Sources"
    ),
    .target(
      name: "OGDetectiveComponent",
      dependencies: [
        "OGTenantCore",
        "OGTenantKit",
        "OGTestEnvironmentKit",
        "OGDetective",
        "OGFeatureAdapter",
        "OGFeatureConfigView"
      ],
      path: "Packages/OGDetectiveComponent/Sources/OGDetectiveComponent",
      resources: [.process("Resources")]
    ),
    .target(
      name: "OGTenantKit",
      dependencies: [
        "OGCore",
        "OGTenantCore",
        "OGDIService",
        "OGFeatureCore"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTenantKit/Sources"
    ),
    .target(
      name: "OGTenantSwitch",
      dependencies: [
        "OGBundledFeatureSetFetcher",
        "OGCore",
        "OGFeatureCore",
        "OGTenantCore",
        "OGDIService"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTenantSwitch/Sources"
    ),
    .target(
      name: "OGTestEnvironmentKit",
      dependencies: [
        "OGCore",
        "OGFeatureCore",
        "OGDIService",
        "OGFeatureManager",
        "OGTenantKit"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTestEnvironmentKit/Sources"
    ),
    .target(
      name: "OGStorage",
      path: "Packages/OGCore/Packages/OGStorage/Source"
    ),
    .target(
      name: "OGStorageTestsUtils",
      dependencies: [
        "OGStorage",
        "OGMacros",
        "OGMock"
      ],
      path: "Packages/OGCore/Packages/OGStorage/TestsUtils"
    ),
    .target(
      name: "OGZipArchiver",
      dependencies: [
        "ZipArchive"
      ],
      path: "Packages/OGCore/Packages/OGZipArchiver/Sources"
    ),
    .target(
      name: "OGLogger",
      path: "Packages/OGCore/Packages/OGLogger/Source/OGLogger"
    ),
    .target(
      name: "OGSecret",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGIdentifierTestsUtils"
      ],
      path: "Packages/OGSecret/Sources/OGSecret"
    ),
    .target(
      name: "OGTracker",
      dependencies: [
        "OGCore",
        "OGDomainStore",
        "OGAdjustReporter",
        "OGAirshipReporter",
        "OGFirebaseReporter",
        "OGTrackerCore",
        "OGTrackerOptInService",
        "OGWebView",
        .product(name: "OGAppKitSDK", package: "og-dx_aac-multiplatform-sdk"),
        .product(name: "SnowplowTracker", package: "snowplow-ios-tracker")
      ],
      path: "Packages/OGTracker/Sources/OGTracker"
    ),
    .target(
      name: "OGTrackerTestsUtils",
      dependencies: [
        "OGTracker",
        "OGCoreTestsUtils"
      ],
      path: "Packages/OGTracker/TestsUtils"
    ),
    .target(
      name: "OGAirshipReporter",
      dependencies: [
        "OGAirshipKit",
        "OGDIService",
        "OGSecret",
        "OGTrackerCore",
        "OGTrackerOptInService"
      ],
      path: "Packages/OGTracker/Packages/OGAirshipReporter/Sources"
    ),
    .target(
      name: "OGAdjustReporter",
      dependencies: [
        .product(name: "AdjustSdk", package: "ios_sdk"),
        "OGDIService",
        "OGSecret",
        "OGTrackerCore",
        "OGTrackerOptInService",
        "OGDeepLinkHandler"
      ],
      path: "Packages/OGTracker/Packages/OGAdjustReporter/Sources"
    ),
    .target(
      name: "OGAdjustReporterTestsUtils",
      dependencies: [
        "OGAdjustReporter",
        "OGCoreTestsUtils",
        "OGTrackerCoreTestsUtils",
        "OGTrackerOptInServiceTestsUtils"
      ],
      path: "Packages/OGTracker/Packages/OGAdjustReporter/TestsUtils"
    ),
    .target(
      name: "OGDialogCoordinatorTestsUtils",
      dependencies: [
        "OGDialogCoordinator",
        "OGCoreTestsUtils",
        "OGDomainStoreTestsUtils"
      ],
      path: "Packages/OGDialogCoordinator/TestsUtils"
    ),
    .target(
      name: "OGFirebaseReporter",
      dependencies: [
        "OGDIService",
        "OGFeatureAdapter",
        "OGFirebaseKit",
        "OGTrackerCore",
        "OGTrackerOptInService"
      ],
      path: "Packages/OGTracker/Packages/OGFirebaseReporter/Sources"
    ),
    .target(
      name: "OGTrackerCore",
      dependencies: ["OGFeatureAdapter"],
      path: "Packages/OGTracker/Packages/OGTrackerCore/Sources"
    ),
    .target(
      name: "OGTrackerCoreTestsUtils",
      dependencies: ["OGTrackerCore"],
      path: "Packages/OGTracker/Packages/OGTrackerCore/TestsUtils"
    ),
    .target(
      name: "OGTrackerOptInService",
      dependencies: [
        "OGDomainStore",
        "OGCore",
        "OGDIService",
        "OGTrackerCore",
        "OGAirshipKit"
      ],
      path: "Packages/OGTracker/Packages/OGTrackerOptInService/Sources"
    ),
    .target(
      name: "OGViewStore",
      path: "Packages/OGViewStore/Sources/OGViewStore"
    ),
    .target(
      name: "OGRouter",
      dependencies: [
        "OGDIService",
        "OGFeatureManager",
        "OGFeatureCore"
      ],
      path: "Packages/OGRouter/Sources/OGRouter"
    ),
    .target(
      name: "OGWebBridge",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGRouter",
        "OGTrackerOptInService"
      ],
      path: "Packages/OGWebBridge/Sources/OGWebBridge"
    ),
    .target(
      name: "OGWebView",
      dependencies: [
        "OGBackports",
        "OGCore",
        "OGCopyCodeBanner",
        "OGDIService",
        "OGDialogCoordinator",
        "OGRouter",
        "OGTenantSwitch",
        "OGNetworkLogger",
        "OGSearch",
        "OGScreenViewUpdate",
        "OGViewStore",
        "OGNavigationCore",
        "OGNavigationBar",
        "OGLoginButton",
        "OGSalutation",
        "OGTrackerOptInService",
        "OGWebBridge",
        "OGUserAgent"
      ],
      path: "Packages/OGWebView/Sources/OGWebView"
    ),
    .target(
      name: "OGSalutation",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureKit"
      ],
      path: "Packages/OGSalutation/Sources/OGSalutation"
    ),
    .target(
      name: "OGLoginButton",
      dependencies: [
        "OGAppLifecycle",
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGUserCore"
      ],
      path: "Packages/OGLoginButton/Sources/OGLoginButton"
    ),
    .target(
      name: "OGWebBridgeTracker",
      dependencies: ["OGCore", "OGDIService", "OGWebBridge", "OGTracker", "OGAdjustReporter"],
      path: "Packages/OGTracker/Packages/OGWebBridgeTracker/Sources/OGWebBridgeTracker"
    ),
    .target(
      name: "OGWebBridgeTrackerTestsUtils",
      dependencies: [
        "OGWebBridgeTracker",
        "OGCoreTestsUtils"
      ],
      path: "Packages/OGTracker/Packages/OGWebBridgeTracker/TestsUtils"
    ),
    .target(
      name: "OGMacros",
      dependencies: ["OGIdentifierMacros", "OGMocksMacros"],
      path: "Packages/OGCore/Packages/OGMacros/Sources/OGMacros"
    ),
    .target(
      name: "OGMock",
      path: "Packages/OGCore/Packages/OGMock/Sources/OGMock"
    ),
    .target(
      name: "OGNavigation",
      dependencies: [
        "OGDIService",
        "NavigationBackport",
        "OGFeatureAdapter",
        "OGRouter",
        "OGFeatureCore",
        "OGBadge",
        "OGNavigationCore",
        "OGScreenViewUpdate"
      ],
      path: "Packages/OGNavigation/Sources/OGNavigation"
    ),
    .target(
      name: "OGNavigationCore",
      dependencies: [
        "OGDIService",
        "OGL10n"
      ],
      path: "Packages/OGNavigation/Packages/OGNavigationCore/Sources/OGNavigationCore"
    ),
    .target(
      name: "OGSearch",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGFeatureCore",
        "OGL10n"
      ],
      path: "Packages/OGSearch/Sources/OGSearch"
    ),
    .target(
      name: "OGSystemKit",
      dependencies: ["OGCore", "OGDIService", "OGNavigation"],
      path: "Packages/OGSystemKit/Sources/OGSystemKit"
    ),
    .target(
      name: "OGSystemKitTestsUtils",
      dependencies: ["OGSystemKit"],
      path: "Packages/OGSystemKit/TestsUtils"
    ),
    .target(
      name: "OGBadgeTestsUtils",
      dependencies: [
        "OGBadge",
        "OGNavigationCore",
        "OGMock"
      ],
      path: "Packages/OGBadge/TestsUtils"
    ),
    .target(
      name: "OGCoreTestsUtils",
      dependencies: [
        "OGCore",
        "OGIdentifierTestsUtils",
        "OGMacros",
        "OGMock",
        "OGSwiftConcurrencyExtras"
      ],
      path: "Packages/OGCore/TestsUtils"
    ),
    .target(
      name: "OGCopyCodeBannerTestsUtils",
      dependencies: ["OGCopyCodeBanner"],
      path: "Packages/OGCopyCodeBanner/TestsUtils"
    ),
    .target(
      name: "OGDetectiveTestsUtils",
      dependencies: ["OGDetective"],
      path: "Packages/OGDetective/TestsUtils"
    ),
    .target(
      name: "OGDomainStoreTestsUtils",
      dependencies: ["OGDomainStore"],
      path: "Packages/OGDomainStore/TestsUtils"
    ),
    .target(
      name: "OGViewStoreTestsUtils",
      dependencies: ["OGViewStore"],
      path: "Packages/OGViewStore/TestsUtils"
    ),
    .target(
      name: "OGFeatureAdapterTestsUtils",
      dependencies: ["OGFeatureAdapter"],
      path: "Packages/OGFeatureKit/Packages/OGFeatureAdapter/TestsUtils"
    ),
    .target(
      name: "OGFeatureManagerTestsUtils",
      dependencies: [
        "OGFeatureManager",
        "OGIdentifierTestsUtils"
      ],
      path: "Packages/OGFeatureKit/Packages/OGFeatureManager/TestsUtils"
    ),
    .target(
      name: "OGAirshipKitTestsUtils",
      dependencies: [
        "OGAirshipKit",
        "OGCoreTestsUtils",
        "OGDomainStoreTestsUtils",
        "OGRouterTestsUtils",
        "OGTenantCoreTestsUtils",
        "OGTenantKitTestsUtils"
      ],
      path: "Packages/OGAirshipKit/TestsUtils"
    ),
    .target(
      name: "OGFirebaseKitTestsUtils",
      dependencies: [
        "OGFirebaseKit",
        "OGIdentifierTestsUtils",
        "OGTrackerOptInServiceTestsUtils"
      ],
      path: "Packages/OGFirebaseKit/TestsUtils"
    ),
    .target(
      name: "OGHTTPClientTestsUtils",
      dependencies: [
        "OGHTTPClient",
        "OGCoreTestsUtils"
      ],
      path: "Packages/OGHTTPClient/TestsUtils"
    ),
    .target(
      name: "OGIdentifierTestsUtils",
      dependencies: ["OGIdentifier"],
      path: "Packages/OGCore/Packages/OGIdentifier/TestsUtils"
    ),
    .target(
      name: "OGNavigationTestsUtils",
      dependencies: ["OGNavigation"],
      path: "Packages/OGNavigation/TestsUtils"
    ),
    .target(
      name: "OGNavigationCoreTestsUtils",
      dependencies: [
        "OGNavigationCore",
        "OGCoreTestsUtils",
        "OGCore"
      ],
      path: "Packages/OGNavigation/Packages/OGNavigationCore/TestsUtils"
    ),
    .target(
      name: "OGLoggerTestsUtils",
      dependencies: ["OGLogger"],
      path: "Packages/OGCore/Packages/OGLogger/TestsUtils"
    ),
    .target(
      name: "OGLoginButtonTestsUtils",
      dependencies: ["OGLoginButton"],
      path: "Packages/OGLoginButton/TestsUtils"
    ),
    .target(
      name: "OGRouterTestsUtils",
      dependencies: [
        "OGRouter",
        "OGCoreTestsUtils",
        "OGFeatureManagerTestsUtils"
      ],
      path: "Packages/OGRouter/TestsUtils"
    ),
    .target(
      name: "OGSecretTestsUtils",
      dependencies: [
        "OGSecret",
        "OGIdentifierTestsUtils"
      ],
      path: "Packages/OGSecret/TestsUtils"
    ),
    .target(
      name: "OGTenantKitTestsUtils",
      dependencies: [
        "OGTenantKit",
        "OGCoreTestsUtils"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTenantKit/TestsUtils"
    ),
    .target(
      name: "OGTenantSwitchTestsUtils",
      dependencies: [
        "OGTenantSwitch",
        "OGTenantCoreTestsUtils"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTenantSwitch/TestsUtils"
    ),
    .target(
      name: "OGTestEnvironmentKitTestsUtils",
      dependencies: [
        "OGTestEnvironmentKit",
        "OGAppEnvironmentTestsUtils",
        "OGFeatureManagerTestsUtils",
        "OGTenantCoreTestsUtils"
      ],
      path: "Packages/OGFeatureKit/Packages/OGTestEnvironmentKit/TestsUtils"
    ),
    .target(
      name: "OGTenantCoreTestsUtils",
      dependencies: ["OGTenantCore"],
      path: "Packages/OGFeatureKit/Packages/OGTenantCore/TestsUtils"
    ),
    .target(
      name: "OGTrackerOptInServiceTestsUtils",
      dependencies: [
        "OGTrackerOptInService",
        "OGAirshipKitTestsUtils",
        "OGCoreTestsUtils",
        "OGDomainStoreTestsUtils"
      ],
      path: "Packages/OGTracker/Packages/OGTrackerOptInService/TestsUtils"
    ),
    .target(
      name: "OGURLCredentialStorageTestsUtils",
      dependencies: ["OGURLCredentialStorage"],
      path: "Packages/OGURLCredentialStorage/TestsUtils"
    ),
    .target(
      name: "OGUserCoreTestsUtils",
      dependencies: [
        "OGUserCore",
        "OGAirshipKitTestsUtils",
        "OGCoreTestsUtils",
        "OGDomainStoreTestsUtils",
        "OGWebBridgeTestsUtils"
      ],
      path: "Packages/OGUserCore/TestsUtils"
    ),
    .target(
      name: "OGWebBridgeTestsUtils",
      dependencies: [
        "OGWebBridge",
        "OGCoreTestsUtils"
      ],
      path: "Packages/OGWebBridge/TestsUtils"
    ),
    .target(
      name: "OGWebViewTestsUtils",
      dependencies: [
        "OGWebView",
        "OGCoreTestsUtils",
        "OGLoginButtonTestsUtils",
        "OGRouterTestsUtils",
        "OGSalutationTestsUtils",
        "OGWebBridgeTestsUtils"
      ],
      path: "Packages/OGWebView/TestsUtils"
    ),
    .target(
      name: "OGSalutationTestsUtils",
      dependencies: ["OGSalutation"],
      path: "Packages/OGSalutation/TestsUtils"
    ),
    .target(
      name: "PluginsBundle",
      path: "Plugins/Bundle",
      plugins: [.plugin(name: "SecretServiceBuildPlugin")]
    ),
    .binaryTarget(
      name: "secret-service",
      path: "Plugins/SecretService/Release/secret-service.artifactbundle"
    ),
    .plugin(
      name: "SecretServiceBuildPlugin",
      capability: .buildTool(),
      dependencies: [.target(name: "secret-service")],
      path: "Plugins/SecretService/Plugins/SecretServiceBuildPlugin"
    ),
    .macro(
      name: "OGIdentifierMacros",
      dependencies: [
        .product(name: "SwiftSyntaxMacros", package: "swift-syntax"),
        .product(name: "SwiftCompilerPlugin", package: "swift-syntax")
      ],
      path: "Packages/OGCore/Packages/OGMacros/Sources/OGIdentifierMacros"
    ),
    .macro(
      name: "OGMocksMacros",
      dependencies: [
        .product(name: "SwiftSyntaxMacros", package: "swift-syntax"),
        .product(name: "SwiftCompilerPlugin", package: "swift-syntax")
      ],
      path: "Packages/OGCore/Packages/OGMacros/Sources/OGMocksMacros"
    )
  ],
  swiftLanguageVersions: [.v5]
)
