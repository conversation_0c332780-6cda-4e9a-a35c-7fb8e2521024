# Otto Group Kit
The OttoGroupKit for iOS (OGKit) is a shared internal framework of mobile specific eCommerce as well as mCommerce modules to use within shopping apps throughout the Otto Group. 

## Table of contents 
- [Otto Group Kit](#otto-group-kit)
  - [Table of contents](#table-of-contents)
  - [Usage](#usage)
    - [Adding the Otto Group Kit as a Dependency](#adding-the-otto-group-kit-as-a-dependency)
    - [Adding the SSH key to your GitHub Action Workflow file](#adding-the-ssh-key-to-your-github-action-workflow-file)
  - [Submitting a Bug Report](#submitting-a-bug-report)
  - [Submitting a Feature Request](#submitting-a-feature-request)
  - [Contributing to the Otto Group Kit](#contributing-to-the-otto-group-kit)
  - [Package documentations](#package-documentations)
  - [Tools documentations](#tools-documentations)
  - [Plugins documentations](#plugins-documentations)
  - [Deprecated](#deprecated)


## Usage

Please see
the documentation
for more detailed usage instructions.

### Adding the Otto Group Kit as a Dependency
To use the Otto Group Kit with your package, first add it as a dependency:

```swift
let package = Package(
    // name, platforms, products, etc.
    dependencies: [
        // other dependencies
        .package(url: "**************:aacml/og-dx_aac-ios-module-otto_group_commerce_kit.git", from: "1.0.0"),
    ],
    targets: [
        // targets
    ]
)
```
### Adding the [SSH](https://github.com/marketplace/actions/install-ssh-key) key to your GitHub Action Workflow file 

```bash
- name: Install SSH key
	uses: shimataro/ssh-key-action@v2
	with:
		key: ${{ secrets.OGKIT_SSH_PRIVATE_KEY }}
		name: id_rsa # optional
		known_hosts: ${{ secrets.KNOWN_HOSTS_OGKIT }}
		if_key_exists: fail # replace / ignore / fail; optional (defaults to fail)
```
## Submitting a Bug Report

The Otto Group Kit tracks all bug reports with
[GitHub Issues](https://github.com/aacml/og-dx_aac-ios-module-otto_group_commerce_kit/issues).
When you submit a bug report we ask that you follow the
[provided template](https://github.com/aacml/og-dx_aac-ios-module-otto_group_commerce_kit/issues/new?template=BUG_REPORT.md)
and provide as many details as possible.

If you can confirm that the bug occurs when using the latest commit of the The Otto Group Kit
from the `main` branch, that will help us track down the bug faster.

## Submitting a Feature Request

For feature requests, please feel free to file a
[GitHub issue](https://github.com/aacml/og-dx_aac-ios-module-otto_group_commerce_kit/issues/new?template=FEATURE_REQUEST.md).

Don't hesitate to submit a feature request if you see a way
the Otto Group Kit can be improved to better meet your needs.

## Contributing to the Otto Group Kit

Please see the [contributing guide](/docs/docs/Contributing.md) for more information.

## Package documentations
* [OGKit](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit)
	Click to view all available packages.
* [OGFeatureKit](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogfeaturekit/documentation/ogfeaturekit)

    The OGFeatureKit combines Tenants, Environments, and Features into one Package

    * [OGTestEnvironmentKit](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogtestenvironmentkit/documentation/ogtestenvironmentkit)

        A framework for managing OGTestEnvironment objects in an iOS app. The OGTestEnvironment objects represent sets of features that can be enabled or disabled for testing purposes.

    * [OGFeatureManager](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogfeaturemanager/documentation/ogfeaturemanager)

        A framework that manages the features for your app. It receives updates from multiple sources (default, remote, test environment) and combines them to provide a single source of truth for the features that are enabled in your app. It also provides methods for interacting with the features, such as checking if a feature is enabled, getting a specific feature, and updating or saving local features.

    * [OGFeatureAdapter](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogfeatureadapter/documentation/ogfeatureadapter)

        A framework that provides a set of tools for retrieving single features from a config.

    * [OGRemoteFeatureSetFetcher](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogremotefeaturesetfetcher/documentation/ogremotefeaturesetfetcher)

        A framework with a default implantation of a remote feature set fetcher using Firebase Remote Config.

    * [OGTenantKit](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogtenantkit/documentation/ogtenantkit)

        A framework that provides a set of tools for managing tenant selection in your app. It includes selecting tenants, receiving tenant selection updates, and providing information about available tenants.

    * OGBundledFeatureSetFetcher

        A framework with a default implantation of a bundled feature set fetcher.


    * OGFeatureConfigView
  
        A framework that provides a view that allows editing the current config. It also indicates if the original config has been edit locally.

    * OGTenantSwitch

        A framework that provides a tenant for a given url.

    * OGFeatureCore

        A framework that holds core classes used by other packages.

    * OGTenantCore

        A framework that holds core classes used by other packages.
* [OGSecret](Packages/OGSecret/README.md)

    A package that provides a set of functionalities to publish a secret based on an identifier. 

* [L10nRemoteService](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogl10nservice/documentation/ogl10nservice/)

* OGCore
    * OGIdentifier
    * OGLogger
    * OGAppEnvironment
    * OGStorage

## Tools documentations
* [OGAssetsFetcher](Tools/OGAssetsFetcher/README.md)

    A command line tool to fetch and download assets (colors, images, fonts) from Zeplin.

* [OGFastlane](Tools/OGFastlane/README.md)

    A Swift command-line tool that intelligently scans Swift packages for test targets and generates Fastlane configuration files for automated testing. Uses git diff analysis to detect changed packages and automatically includes dependent packages.

* [OGKeychainConverter](Tools/OGKeychainConverter/README.md)

    A command line tool that converts a given Keychain into a `OGKeyStore`.

* [OGKeyStore](Tools/OGKeyStore/README.md)

    A mac app to create and manage .ogkeystore files.

* [OGFeatureAdapterMaker](Tools/OGFeatureAdapterMaker/README.md)

    A command line tool which creates FeatureAdapters out of a given config.

* [OGRegex](Tools/OGRegex/README.md)

    A command line tool that applies a regular expression to a specified file and executes a specified shell command with the regex results.

## Plugins documentations
* [SecretService](Plugins/SecretService/README.md)

    A plugin and command line tool that obfuscates passwords and generates a file out of it.

## Deprecated
* [AssetsFetcher](/Tools/AssetsFetcher/README.md) -> Use [OGAssetsFetcher](/Tools/OGAssetsFetcher/README.md) instead.
