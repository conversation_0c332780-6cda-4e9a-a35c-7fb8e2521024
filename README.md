# OG App Kit SDK

This repository contains the multiplatform OG App Kit SDK providing shared functionality to our iOS and Android
applications.

The SDK is composed of multiple modules that are combined into a central entry point. All components are published as
Android library artifacts and as a bundled XCFramework for iOS.

## Usage

### Android installation

Like everything published to the GitHub packages Maven repository, using this SDK requires setting up access to it, as
[outlined here](https://studious-tribble-7140fbc3.pages.github.io/ogkit/setup/).

Afterward, simply add the dependency on the SDK library artifact:

```kotlin
// build.gradle.kts
depdendencies {
    implementation("com.ottogroup.ogappkit:sdk-android:2.0.0-alpha.8")
}
```

### iOS Installation

Multiplatform SDK requires [Glycerin iOS Setup](https://crispy-adventure-q4v74mk.pages.github.io/setup/ios/), before
adding it as package.

⚠️ Due to this project using slightly different release process which uses GitHub releases an additional entry is
required in `~/.netrc`:

```
machine api.github.com
login GITHUB_USER_NAME
password GITHUB_PERSONAL_ACCESS_TOKEN
```

After Glycerin setup, you can add Multiplatform SDK to an Xcode project by adding it as a package dependency.

In Xcode go to Files > Add Packages… to add the package https://github.com/aacml/og-dx_aac-multiplatform-sdk.git to your
Xcode project via the Swift Package Manager.

In case of using the Multiplatform SDK Swift Package as a dependency within another Package, add a package dependency
and a product to the respective Package.swift file.

```
// package dependency
dependencies: [
  .package(
      url: "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git",
      exact: "0.6.0'
    )
],
...
// target dependency
.product(
    name: "OGAppKitSDK",
    package: "og-dx_aac-multiplatform-sdk"
)
```

## Features

- Tracking
- [Native UI](ogappkit/nativeui/README.md)

## Development

Active feature development happens on the `main` branch, or on dedicated `epic/*` branches, if desired. Bug fix SDK
releases for ongoing app releases happen on a matching release branch, e.g. `release/6.0.0`. To ensure that fixes are
also included in future builds, PRs should typically be created on either `main` or `release/*` and then cherry-picked
after merge onto the other branch to keep them in sync.

Pull requests against `main`, `epic` and `release` branches require code checks.

Each domain (i.e. feature) of the SDK is separated into a subdirectory within `ogappkit`. Typically, a domain needs two
submodules: one for the public implementation of functionality and definition of its API, called `api` and one for the
SDK-internal plumbing code that is required to wire the modules together into a single SDK, called `internal`.

Much of the required setup for a Kotlin multiplatform build is handled by the Gradle plugins in `buildSrc`. New feature
modules can take an existing implementation as a blueprint.

To make a new feature available via the SDK, it needs to be added into the list of `sdkModules` in the `sdk` module's
`build.gradle.kts` file. It also needs to be hooked into the `OGAppKit` implementation in the same module.

### Versioning & publishing

Versioning and publishing is currently handled manually! The current library version is set as an argument to the
dispatch of the GitHub actions workflow `Publish`.

The version MUST adhere to [Semantic Versioning](https://semver.org/) standards. To make integration into the apps easier, the SDK
version should match the targeted app version, with an added build number (counter) suffix, e.g. `6.0.0+3` or
`6.1.0+42`. We accept that this versioning scheme does not reflect the semantics of semantic versioning.

Releases published off the `main` branch are expected to target the upcoming app version and releases published off
a `release/*` branch are expected to target the matching app version.

It is also possible to publish off of any other branch, in which case a pre-release suffix should be used, e.g.
`6.1.0-alpha+1`.

### Local builds

To test things locally during development, you can create builds of the SDK on your machine:

For iOS, run the following Gradle command: `./gradle spmDevBuild`. An XCFramework will be created in
`ogappkit/sdk/build/XCFrameworks/debug/` that you can drag into an XCode project.

For Android, run the Gradle task `./gradlew publishAndroidToMavenLocal`, which will publish the SDK libraries into your
local Maven repository from where they can be included in Android projects using the standard Maven coordinate, so long
as the `mavenLocal()` repository is configured in Gradle.

You can specify a local SDK version in the file `local.properties`:

```
LIBRARY_VERSION=2.0.0-alpha.8
```

This version is not checked into the repository, as in CI the version is determined by the workflow run. If no local
version is set, the default is `1.0.0`.
