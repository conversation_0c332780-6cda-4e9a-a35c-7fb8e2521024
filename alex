#!/bin/sh
# This script is used to run the alex program with the specified arguments.
# It sets the PATH environment variable to include the directory where the alex program is located.


# Check if python is installed
if ! command -v python3 &> /dev/null
then
    echo "python3 could not be found"
    # ask user if they want to install python3
    read -p "Do you want to install python3? (y/n) " answer
    if [ "$answer" = "y" ]; then
        # install python3
        echo "Installing python3..."
        if [ "$(uname)" = "Linux" ]; then
            # install python3 on linux
            sudo apt-get update
            sudo apt-get install -y python3 python3-pip
        elif [ "$(uname)" = "Darwin" ]; then
            # install python3 on macOS
            brew install python3
        elif [ "$(uname)" = "Windows_NT" ]; then
            # install python3 on Windows
            echo "Please install python3 from https://www.python.org/downloads/ and try again."
        fi
    else
        echo "Please install python3 and try again."
        exit 1
    fi
    exit
fi

# setup virtualenv if it doesn't exist
if [ ! -d "ogappkit/l10n/alex/.venv" ]; then
    python3 -q -m venv ogappkit/l10n/alex/.venv
fi
source ogappkit/l10n/alex/.venv/bin/activate

# install dependencies
pip install --upgrade -q -e ogappkit/l10n/alex --disable-pip-version-check
# run alex with predefined arguments
python ogappkit/l10n/alex/src/alex/cli.py -t ogappkit/l10n/data/translations -c ogappkit/l10n/data/changes "$@"
