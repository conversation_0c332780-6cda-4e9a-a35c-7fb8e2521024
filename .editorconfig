[*]
insert_final_newline = true
trim_trailing_whitespace = true

[{*.kt,*.kts}]
ij_kotlin_code_style_defaults = KOTLIN_OFFICIAL
ij_kotlin_continuation_indent_size = 4
ij_kotlin_imports_layout = *,^
ij_kotlin_name_count_to_use_star_import = 50000
ij_kotlin_name_count_to_use_star_import_for_members = 50000
ij_kotlin_packages_to_use_import_on_demand = nothing

# noinspection EditorConfigKeyCorrectness
ktlint_code_style = intellij_idea
# Disabled rules:
# noinspection EditorConfigKeyCorrectness
ktlint_standard_trailing-comma-on-call-site = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_trailing-comma-on-declaration-site = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_import-ordering = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_multiline-if-else = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_multiline-expression-wrapping = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_string-template-indent = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_function-signature = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_backing-property-naming = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_function-expression-body = disabled
# noinspection EditorConfigKeyCorrectness
ktlint_standard_class-signature = disabled
# noinspection EditorConfigKeyCorrectness
continuation_indent_size = 4

indent_style = space
indent_size = 4
