[versions]
# not unused, just referenced in an unorthodox way
android-minSdk = "24"
android-compileSdk = "34"

adjustAndroid = "5.4.1"
agp = "8.10.1"
apollo = "4.1.1"
coroutines = "1.10.2"
datastore = "1.1.3"
firebaseAnalytics = "21.6.2"
firebaseFirestore = "25.1.2"
gitLiveFirebase = "2.1.0"
kermit = "2.0.3"
kmmbridge = "1.2.1"
koin = "4.0.3"
kotlin = "2.2.0"
kotlinpoet = "2.2.0"
kotlin-serialization = "1.8.1"
ksp = "2.2.0-2.0.2"
kspCompileTesting = "0.8.0"
ktor = "3.2.2"
okio = "3.9.1"
skie = "0.10.4"
snowplowAndroidTracker = "6.0.3"
stately = "2.1.0"
turbine = "1.1.0"
androidxLifecycle = "2.9.1"
androidxRoom = "2.7.2"
sqlite = "2.5.0-SNAPSHOT"
roomCommon = "2.7.2"

[libraries]
adjust-android = { module = "com.adjust.sdk:adjust-android", version.ref = "adjustAndroid" }
androidx-datastore-core = { module = "androidx.datastore:datastore-core", version.ref = "datastore" }
androidx-datastore-core-okio = { module = "androidx.datastore:datastore-core-okio", version.ref = "datastore" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "androidxRoom" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "androidxRoom" }
androidx-startup-runtime = { module = "androidx.startup:startup-runtime", version = "1.1.1" }
androidx-lifecycle = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidxLifecycle" }
apollo-adapters-kotlinx-datetime = { module = "com.apollographql.adapters:apollo-adapters-kotlinx-datetime", version = "0.0.4" }
apollo-mockserver = { module = "com.apollographql.mockserver:apollo-mockserver", version = "0.1.0" }
apollo-normalizedCache = { module = "com.apollographql.apollo:apollo-normalized-cache", version.ref = "apollo" }
apollo-runtime = { module = "com.apollographql.apollo:apollo-runtime", version.ref = "apollo" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics", version.ref = "firebaseAnalytics" }
firebase-firestore = { module = "com.google.firebase:firebase-firestore", version.ref = "firebaseFirestore" }
gitlive-firebase-firestore = { module = "dev.gitlive:firebase-firestore", version.ref = "gitLiveFirebase" }
kotlinpoet = { module = "com.squareup:kotlinpoet", version.ref = "kotlinpoet" }
kermit = { module = "co.touchlab:kermit", version.ref = "kermit" }
kermit-koin = { module = "co.touchlab:kermit-koin", version.ref = "kermit" }
koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
kotlinx-coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version = "0.6.0" }
kotlinx-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlin-serialization" }
kotlinx-serialization-properties = { module = "org.jetbrains.kotlinx:kotlinx-serialization-properties", version.ref = "kotlin-serialization" }
ksoup = { module = "com.fleeksoft.ksoup:ksoup-lite", version = "0.2.0" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-ios = { module = "io.ktor:ktor-client-ios", version.ref = "ktor" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-mock = { module = "io.ktor:ktor-client-mock", version.ref = "ktor" }
ktor-serialization-kotlin-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktor-utils = { module = "io.ktor:ktor-utils", version.ref = "ktor" }
skie-annotations = { module = "co.touchlab.skie:configuration-annotations", version.ref = "skie" }
snowplow-android-tracker = { module = "com.snowplowanalytics:snowplow-android-tracker", version.ref = "snowplowAndroidTracker" }
sqlite-bundled = { module = "androidx.sqlite:sqlite-bundled", version.ref = "sqlite" }
square-okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
stately-concurrency = { module = "co.touchlab:stately-concurrency", version.ref = "stately" }
stately-concurrentCollections = { module = "co.touchlab:stately-concurrent-collections", version.ref = "stately" }
turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }
androidx-room-common = { group = "androidx.room", name = "room-common", version.ref = "roomCommon" }
ksp = { group = "com.google.devtools.ksp", name = "symbol-processing-api", version.ref = "ksp" }
ksp-compile-testing = { group = "dev.zacsweers.kctfork", name = "ksp", version.ref = "kspCompileTesting" }
junit = { module = "junit:junit", version = "4.13.2" }
kotlinGradlePlugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin"}
kotlinTestJUnit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin" }

[plugins]
androidLibrary = { id = "com.android.library", version.ref = "agp" }
apollo = { id = "com.apollographql.apollo", version.ref = "apollo" }
detekt = { id = "io.gitlab.arturbosch.detekt", version = "1.23.6" }
kmmbridge = { id = "co.touchlab.kmmbridge.github", version.ref = "kmmbridge" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
kotlinxSerialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
ktlint = { id = "org.jlleitschuh.gradle.ktlint", version = "12.1.1" }
room = { id = "androidx.room", version.ref = "androidxRoom" }
skie = { id = "co.touchlab.skie", version.ref = "skie" }
