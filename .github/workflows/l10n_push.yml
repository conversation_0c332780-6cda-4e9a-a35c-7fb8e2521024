name: Push Translations to Phrase

on:
  pull_request_target:
    types:
      - closed
    branches:
      - 'main'
    paths:
      - 'ogappkit/l10n/data/changes/**'

  workflow_dispatch:
    inputs:
      filename:
        description: 'Name of the file to process'
        required: true
        default: 'ogappkit/l10n/data/changes/base_fixes.txt'  # Optional default value

concurrency:
  group: l10n
  cancel-in-progress: false
jobs:
  prepare:
    if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      changesFile: ${{ steps.set-recent-changed-file.outputs.changesFile }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'
      - name: Get changes file
        id: set-recent-changed-file
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            output="${{ github.event.inputs.filename }}"
          else
            output="$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} -- ogappkit/l10n/data/changes | xargs -I {} ls -t "{}" 2>/dev/null | head -n 1)"
          fi
          echo "changesFile=$output" >> $GITHUB_OUTPUT
          echo "Processing file: $output"

  push:
    needs: prepare
    runs-on: ubuntu-latest
    if: ${{ needs.prepare.outputs.changesFile }} != ''
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'

      - name: Restore cached virtualenv
        uses: actions/cache/restore@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Install dependencies
        run: |
          python -m venv .venv
          source .venv/bin/activate
          pip install -r ogappkit/l10n/alex/requirements.txt
          echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH
          echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV

      - name: Saved cached virtualenv
        uses: actions/cache/save@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Setup Firebase CLI
        run: npm install -g firebase-tools

      - name: Deploy Project
        env:
          PHRASE_API_TOKEN: ${{ secrets.PHRASE_API_TOKEN }}
        run: python ogappkit/l10n/alex/deployment/push.py --changes "${{ needs.prepare.outputs.changesFile }}" --translations ogappkit/l10n/data/translations
