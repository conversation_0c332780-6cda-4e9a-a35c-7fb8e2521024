#!/usr/bin/env kotlin

@file:Repository("https://repo.maven.apache.org/maven2/")
@file:DependsOn("io.github.typesafegithub:github-workflows-kt:3.4.0")
@file:Repository("https://bindings.krzeminski.it")
@file:DependsOn("actions:checkout:v4")
@file:DependsOn("actions:setup-java:v4")
@file:DependsOn("actions:upload-artifact:v4")
@file:DependsOn("gradle:actions__setup-gradle:v4")
@file:DependsOn("gradle:actions__wrapper-validation:v4")
@file:DependsOn("dorny:paths-filter:v3")

import io.github.typesafegithub.workflows.actions.actions.Checkout
import io.github.typesafegithub.workflows.actions.actions.SetupJava
import io.github.typesafegithub.workflows.actions.actions.UploadArtifact
import io.github.typesafegithub.workflows.actions.dorny.PathsFilter_Untyped
import io.github.typesafegithub.workflows.actions.gradle.ActionsSetupGradle
import io.github.typesafegithub.workflows.actions.gradle.ActionsWrapperValidation
import io.github.typesafegithub.workflows.domain.Concurrency
import io.github.typesafegithub.workflows.domain.JobOutputs
import io.github.typesafegithub.workflows.domain.Mode
import io.github.typesafegithub.workflows.domain.Permission
import io.github.typesafegithub.workflows.domain.RunnerType
import io.github.typesafegithub.workflows.domain.triggers.PullRequest
import io.github.typesafegithub.workflows.dsl.JobBuilder
import io.github.typesafegithub.workflows.dsl.expressions.Contexts.github
import io.github.typesafegithub.workflows.dsl.expressions.expr
import io.github.typesafegithub.workflows.dsl.workflow
import io.github.typesafegithub.workflows.yaml.DEFAULT_CONSISTENCY_CHECK_JOB_CONFIG
import kotlin.script.experimental.dependencies.DependsOn
import kotlin.script.experimental.dependencies.Repository

workflow(
    sourceFile = __FILE__,
    name = "Check",
    on = listOf(
        PullRequest(
            pathsIgnore = listOf(
                "docs/**",
                "*.md",
            ),
            branches = listOf(
                "main",
                "epic/**",
		"release/**"
            )
        )
    ),
    concurrency = Concurrency(group = expr(github.head_ref), cancelInProgress = true),
    consistencyCheckJobConfig =  DEFAULT_CONSISTENCY_CHECK_JOB_CONFIG.copy(
        useLocalBindingsServerAsFallback = true,
    ),
) {
    val detectChanges = job(
        id = "detectChanges",
        name = "Detect changed files",
        runsOn = RunnerType.UbuntuLatest,
        timeoutMinutes = 5,
        permissions = mapOf(Permission.PullRequests to Mode.Read),
        outputs = object : JobOutputs() {
            var nonTranslationDataFiles by output()
        }
    ) {
        val filter = uses(
            name = "Filter files",
            id = "filter",
            action = PathsFilter_Untyped(
                filters_Untyped = """
                    nonTranslations:
                      - '!ogappkit/l10n/data/**'
                """.trimIndent()
            )
        )
        jobOutputs.nonTranslationDataFiles = filter.outputs["nonTranslations"]
    }

    fun checkJob(
        id: String,
        name: String,
        runsOn: RunnerType,
        timeoutMinutes: Int = 60,
        checkStep: JobBuilder<JobOutputs.EMPTY>.() -> Unit = {},
    ) {
        job(
            id = id,
            name = name,
            runsOn = runsOn,
            timeoutMinutes = timeoutMinutes,
            needs = listOf(detectChanges),
            `if` = expr { "${detectChanges.outputs.nonTranslationDataFiles} == 'true'" }
        ) {
            uses(
                name = "Checkout",
                action = Checkout()
            )
            uses(
                name = "Gradle Wrapper validation",
                action = ActionsWrapperValidation()
            )
            uses(
                name = "Setup Java",
                action = SetupJava(
                    javaVersion = "21",
                    distribution = SetupJava.Distribution.Temurin
                )
            )
            uses(
                name = "Setup Gradle",
                action = ActionsSetupGradle()
            )

            checkStep()

            uses(
                name = "Upload reports",
                action = UploadArtifact(
                    name = "reports_$id",
                    path = listOf("**/build/reports/**"),
                    ifNoFilesFound = UploadArtifact.BehaviorIfNoFilesFound.Ignore,
                ),
                `if` = expr { failure() }
            )
        }
    }

    checkJob(
        id = "checkAndroid",
        name = "Check Kotlin + Android",
        runsOn = RunnerType.UbuntuLatest,
        timeoutMinutes = 60,
    ) {
        run(
            name = "Run code checks and Android tests",
            command = "./gradlew detekt ktlintCheck test",
        )
    }

    checkJob(
        id = "checkiOS",
        name = "Check iOS",
        runsOn = RunnerType.MacOSLatest,
        timeoutMinutes = 60,
    ) {
        run(
            name = "Run iOS tests",
            command = "./gradlew iosX64Test",
        )
    }
}

Unit // prevent printing the Workflow to stdout
