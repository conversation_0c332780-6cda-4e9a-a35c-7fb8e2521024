name: Merge Strings from GC Projects

on:
  workflow_dispatch:
  schedule:
    - cron: '0 5 * * 1' # Runs every Monday at 5 AM

concurrency:
  group: l10n
  cancel-in-progress: false
jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.set-matrix.outputs.projects }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'
      - name: Get deployable projects
        id: set-matrix
        run: |
          output=$(python ogappkit/l10n/alex/deployment/projects.py)
          echo "projects=$output" >> $GITHUB_OUTPUT
  setenv:
    name: Set Environment Variables for ${{ matrix.project.name }}
    runs-on: ubuntu-latest
    needs: prepare
    strategy:
      matrix:
        project: ${{ fromJson(needs.prepare.outputs.projects).projects }}
      fail-fast: false
    steps:
      - name: Set environment variables
        id: set-env
        run: |
          {
            echo "${{ matrix.project.service_account_secret_name }}<<EOF"
            echo "${{ secrets[matrix.project.service_account_secret_name] }}"
            echo EOF
          } >> "$GITHUB_ENV"

  merge:
    needs: [ prepare,setenv ]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'

      - name: Restore cached virtualenv
        uses: actions/cache/restore@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Install dependencies
        run: |
          python -m venv .venv
          source .venv/bin/activate
          pip install -r ogappkit/l10n/alex/requirements.txt
          echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH
          echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV

      - name: Saved cached virtualenv
        uses: actions/cache/save@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Setup Firebase CLI
        run: npm install -g firebase-tools

      - name: Merge GC Translations
        env:
          PHRASE_API_TOKEN: ${{ secrets.PHRASE_API_TOKEN }}
        run: |
          python ogappkit/l10n/alex/deployment/merge.py --translations ogappkit/l10n/data/translations
          python ogappkit/l10n/alex/deployment/merge.py --translations ogappkit/l10n/data/translations -p BPX --phrase

      - name: Generate iOS InfoPlist.strings
        run: python ogappkit/l10n/alex/deployment/generate.py --translations ogappkit/l10n/data/translations --output ogappkit/l10n/data/infoPlist

      - name: Commit changes
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add ogappkit/l10n/data/translations
          git commit -m 'Merge GC translations'

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'Merge GC translations'
          branch: 'l10n/merge-translations'
          title: 'Merge GC Translations'
          body: 'This PR merges the latest GC translations.'
          labels: 'translation'
          reviewers: 'reviewer-username'
