# This file was generated using Kotlin DSL (.github/workflows/check.main.kts).
# If you want to modify the workflow, please change the Kotlin file and regenerate this YAML file.
# Generated with https://github.com/typesafegithub/github-workflows-kt

name: 'Check'
on:
  pull_request:
    branches:
    - 'main'
    - 'epic/**'
    - 'release/**'
    paths-ignore:
    - 'docs/**'
    - '*.md'
concurrency:
  group: '${{ github.head_ref }}'
  cancel-in-progress: true
jobs:
  check_yaml_consistency:
    name: 'Check YAML consistency'
    runs-on: 'ubuntu-latest'
    steps:
    - id: 'step-0'
      name: 'Check out'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      name: 'Execute script'
      continue-on-error: true
      run: 'rm ''.github/workflows/check.yaml'' && ''.github/workflows/check.main.kts'''
    - id: 'step-2'
      name: '[Fallback] Start the local server'
      run: 'docker run -p 8080:8080 krzema12/github-workflows-kt-jit-binding-server &'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-3'
      name: '[Fallback] Wait for the server'
      run: 'curl --head -X GET --retry 60 --retry-all-errors --retry-delay 1 http://localhost:8080/status'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-4'
      name: '[Fallback] Replace server URL in script'
      run: 'sed -i -e ''s/https:\/\/bindings.krzeminski.it/http:\/\/localhost:8080/g'' .github/workflows/check.main.kts'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-5'
      name: '[Fallback] Execute script again'
      run: 'rm -f ''.github/workflows/check.yaml'' && ''.github/workflows/check.main.kts'''
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-6'
      name: 'Consistency check'
      run: 'git diff --exit-code ''.github/workflows/check.yaml'''
  detectChanges:
    name: 'Detect changed files'
    runs-on: 'ubuntu-latest'
    permissions:
      pull-requests: 'read'
    needs:
    - 'check_yaml_consistency'
    timeout-minutes: 5
    outputs:
      nonTranslationDataFiles: '${{ steps.filter.outputs.nonTranslations }}'
    steps:
    - id: 'filter'
      name: 'Filter files'
      uses: 'dorny/paths-filter@v3'
      with:
        filters: |-
          nonTranslations:
            - '!ogappkit/l10n/data/**'
  checkAndroid:
    name: 'Check Kotlin + Android'
    runs-on: 'ubuntu-latest'
    needs:
    - 'detectChanges'
    - 'check_yaml_consistency'
    if: '${{ needs.detectChanges.outputs.nonTranslationDataFiles == ''true'' }}'
    timeout-minutes: 60
    steps:
    - id: 'step-0'
      name: 'Checkout'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      name: 'Gradle Wrapper validation'
      uses: 'gradle/actions/wrapper-validation@v4'
    - id: 'step-2'
      name: 'Setup Java'
      uses: 'actions/setup-java@v4'
      with:
        java-version: '21'
        distribution: 'temurin'
    - id: 'step-3'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v4'
    - id: 'step-4'
      name: 'Run code checks and Android tests'
      run: './gradlew detekt ktlintCheck test'
    - id: 'step-5'
      name: 'Upload reports'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'reports_checkAndroid'
        path: '**/build/reports/**'
        if-no-files-found: 'ignore'
      if: '${{ failure() }}'
  checkiOS:
    name: 'Check iOS'
    runs-on: 'macos-latest'
    needs:
    - 'detectChanges'
    - 'check_yaml_consistency'
    if: '${{ needs.detectChanges.outputs.nonTranslationDataFiles == ''true'' }}'
    timeout-minutes: 60
    steps:
    - id: 'step-0'
      name: 'Checkout'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      name: 'Gradle Wrapper validation'
      uses: 'gradle/actions/wrapper-validation@v4'
    - id: 'step-2'
      name: 'Setup Java'
      uses: 'actions/setup-java@v4'
      with:
        java-version: '21'
        distribution: 'temurin'
    - id: 'step-3'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v4'
    - id: 'step-4'
      name: 'Run iOS tests'
      run: './gradlew iosX64Test'
    - id: 'step-5'
      name: 'Upload reports'
      uses: 'actions/upload-artifact@v4'
      with:
        name: 'reports_checkiOS'
        path: '**/build/reports/**'
        if-no-files-found: 'ignore'
      if: '${{ failure() }}'
