name: Deploy Firebase Projects

on:
  pull_request_target:
    types:
      - closed
    branches:
      - 'main'
    paths:
      - 'ogappkit/l10n/alex/firebase/**'

  workflow_dispatch:
    inputs:
      project:
        description: 'Name of the project to deploy'
        required: false
        default: ''  # Optional default value
concurrency:
  group: l10n
  cancel-in-progress: false
jobs:
  prepare:
    if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.set-matrix.outputs.projects }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'
      - name: Get deployable projects
        id: set-matrix
        run: |
          args=""
          if [ -n "${{ github.event.inputs.project }}" ]; then
            args="-p ${{ github.event.inputs.project }}"
          fi
          output=$(python ogappkit/l10n/alex/deployment/projects.py $args)
          echo "projects=$output" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy ${{ matrix.project.name }}
    needs: prepare
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: ${{ fromJson(needs.prepare.outputs.projects).projects }}
      fail-fast: false
      max-parallel: 4

    steps:
      - uses: actions/checkout@v4
      - uses: 'google-github-actions/auth@v2'
        with:
          project_id: ${{ matrix.project.project_id }}
          credentials_json: ${{ secrets[matrix.project.service_account_secret_name] }}

      - uses: actions/setup-python@v5
        id: setup_python
        with:
          python-version: '3.13'

      - name: Restore cached virtualenv
        uses: actions/cache/restore@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Install dependencies
        run: |
          python -m venv .venv
          source .venv/bin/activate
          pip install -r ogappkit/l10n/alex/requirements.txt
          echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH
          echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV
          ln -sf "$VIRTUAL_ENV" ogappkit/l10n/alex/firebase/functions/venv

      - name: Saved cached virtualenv
        uses: actions/cache/save@v4
        with:
          key: venv-${{ runner.os }}-${{ steps.setup_python.outputs.python-version }}-${{ hashFiles('ogappkit/l10n/alex/requirements.txt') }}
          path: .venv

      - name: Setup Firebase CLI
        run: npm install -g firebase-tools

      - name: Deploy Project
        env:
          PHRASE_API_TOKEN: ${{ secrets[matrix.project.phrase_api_secret_name] }}
          PROJECT_NAME: ${{ matrix.project.name }}
          PROJECT_ID: ${{ matrix.project.project_id }}
          REGION: ${{ matrix.project.region }}
        run: python ogappkit/l10n/alex/deployment/deploy.py --project ${{ matrix.project.name }}

      - name: Print log if deployment failed
        if: failure()
        run: |
          LOG_PATH="ogappkit/l10n/alex/firebase/firebase-debug.log"
          if [ -f "$LOG_PATH" ]; then
            echo "===== firebase-debug.log ====="
            cat "$LOG_PATH"
            echo "============================="
          elif [ -f "firebase-debug.log" ]; then
            echo "===== firebase-debug.log ====="
            cat "firebase-debug.log"
            echo "============================="
          else
            echo "firebase-debug.log not found"
          fi
