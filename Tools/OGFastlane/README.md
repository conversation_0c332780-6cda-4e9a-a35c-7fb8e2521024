# OGFastlane

A Swift command-line tool that intelligently scans Swift packages for test targets and generates Fastlane configuration files for automated testing. OGFastlane uses git diff analysis to detect changed packages and automatically includes dependent packages that need testing.

## Features

- **Smart Package Detection**: Automatically discovers Swift packages with test targets
- **Git-Aware Testing**: Uses git diff to identify changed packages between branches/commits
- **Dependency Tracking**: Finds and includes dependent packages when dependencies change
- **iOS-Focused**: Filters packages to include only iOS-compatible targets
- **Template-Based Generation**: Uses Stencil templates for flexible configuration generation
- **Multi-File Output**: Generates Fastfile, packages.yml, and project.yml for complete project setup

## Generated Files

OGFastlane generates three key files:

1. **Fastfile**: Contains Fastlane lanes for running package tests with proper device configuration
2. **packages.yml**: XcodeGen package configuration mapping package names to paths
3. **project.yml**: Complete XcodeGen project configuration with schemes, targets, and build settings

## Build

### Using Makefile (Recommended)
```bash
make build_og-fastlane
```

### Manual Release Build
```bash
cd Tools/OGFastlane
swift build --configuration release --arch x86_64
cp -f .build/x86_64-apple-macosx/release/og-fastlane Release/og-fastlane_x86_64
```

### Debug Build
```bash
cd Tools/OGFastlane
swift run og-fastlane
```

## Usage

### Command Line Interface
```bash
og-fastlane [<destination-file-path>] [--project-path <project-path>] [--template-path <template-path>] [--macos-platform <macos-platform>] [--ios-platform <ios-platform>]
```

### Arguments
| Argument | Description |
| :--- | :--- |
| `<destination-file-path>` | The file destination path for the generated Fastfile |

### Options
| Option | Description | Default |
| :--- | :--- | :--- |
| `--project-path <project-path>` | Path to the project root directory | Current directory |
| `--template-path <template-path>` | Path to custom Stencil template file | Bundled Fastfile.stencil |
| `--macos-platform <macos-platform>` | macOS platform for test execution | `platform=macOS` |
| `--ios-platform <ios-platform>` | iOS platform for test execution | `platform=iOS Simulator,name=iPhone 15 Pro,OS=17.0.1` |
| `-h`, `--help` | Show help information | - |

### Examples

#### Basic Usage
```bash
og-fastlane /fastlane/Fastfile
```

#### With Custom Template
```bash
og-fastlane /fastlane/Fastfile --template-path /path/to/custom/template.stencil
```

#### Using Makefile Integration
```bash
make generate_fastfile
```

## How It Works

### Package Discovery
1. **Scan Phase**: Recursively searches for `Package.swift` files in the project directory
2. **Filter Phase**: Excludes packages in `.build` directories and packages without test targets
3. **iOS Detection**: Identifies packages with iOS platform support using `.iOS` platform declarations

### Change Detection
OGFastlane uses git to determine which packages need testing:

- **On main branch**: Compares HEAD with the previous commit
- **On feature branches**: Compares current branch with `origin/main`
- **Dependency tracking**: When a package changes, finds all packages that depend on it

### Template Processing
Uses Stencil templating engine to generate configuration files with:
- **Package metadata**: Names, paths, and schemes for discovered packages
- **Platform configuration**: iOS simulator settings and device specifications
- **Build settings**: Proper configurations for Alpha, Beta, and Release builds
- **Changelog integration**: Git commit messages for deployment tracking

## Integration

### Makefile Integration
OGFastlane is integrated into the project's Makefile:

```makefile
# Build the tool
.PHONY: build_og-fastlane
build_og-fastlane:
    cd Tools/OGFastlane && swift build --configuration release --arch x86_64

# Generate Fastfile
.PHONY: generate_fastfile
generate_fastfile: build_og-fastlane
    ./Tools/OGFastlane/Release/og-fastlane_x86_64 /fastlane/Fastfile --template-path /Tools/OGFastlane/Source/Template/Fastfile.stencil
```

### Testing Workflow
1. **Install dependencies**: `make install` (includes building OGFastlane)
2. **Generate configuration**: `make generate_fastfile`
3. **Run tests**: `make test` (uses generated Fastfile)

## Dependencies

OGFastlane uses the following Swift packages:
- **ArgumentParser**: Command-line argument parsing
- **Stencil**: Template processing engine
- **PathKit**: File system path manipulation

## Template Customization

### Default Templates
- `Fastfile.stencil`: Fastlane configuration with test lanes
- `packages.yml.stencil`: XcodeGen package mapping
- `project.yml.stencil`: Complete XcodeGen project configuration

### Custom Templates
You can provide custom Stencil templates using the `--template-path` option. Templates have access to:
- `destinations`: Dictionary of package names to iOS destinations
- `paths`: Dictionary of package names to relative paths
- `schemes`: Dictionary of package names to Xcode schemes
- `changeLog`: Git commit messages for the current changes

## Troubleshooting

### Common Issues

**No packages found**: Ensure packages have test targets and contain `.iOS` platform declarations

**Git diff errors**: Verify you're in a git repository with proper remote configuration

**Template errors**: Check Stencil template syntax and ensure all required variables are available

**Build failures**: Ensure Xcode command line tools are installed and Swift 5.7+ is available
