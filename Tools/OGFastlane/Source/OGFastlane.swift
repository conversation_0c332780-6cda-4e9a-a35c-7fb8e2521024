import ArgumentParser
import Foundation
import PathKit
import Stencil

// MARK: - Target

struct Target: Decodable, Hashable {
  let name: String
  let scheme: String
  let path: String
  let destination: String
}

// MARK: - OGFastlane

@main
struct OGFastlane: AsyncParsableCommand {
  @Argument(help: "The Destination file path for the generate Fastfile.") private var destinationFilePath: String?
  @Option(name: .long, help: "Path to the project to use. Default is current directory path.") private var projectPath: String = FileManager.default.currentDirectoryPath
  @Option(name: .long, help: "Path to the stencil file to use. Note: Universal Binaries do not support the bundled stencil.") private var templatePath: String?
  @Option(name: .customLong("macos-platform"), help: "The macOS platform to execute the tests on.") private var macOSDestination: String = "platform=macOS"
  @Option(name: .customLong("ios-platform"), help: "The iOS platform to execute the tests on.") private var iOSDestination: String = "platform=iOS Simulator,name=iPhone 15 Pro,OS=17.0.1"

  func run() async throws {
    guard let destinationPath = destinationFilePath else {
      print("⛔️  Missing generated file destination path")
      throw ExitCode.failure
    }

    let packagesSubpaths = try findPackages(at: projectPath).compactMap { $0.contains(".build") ? nil : $0 }
    let targets = try findTestTargets(at: packagesSubpaths)
    print("ℹ️  Loading Template")
    let defaultTemplatePath: URL?
    if let templatePath {
      defaultTemplatePath = URL(string: projectPath)?.appendingPathComponent(templatePath)
    } else {
      defaultTemplatePath = Bundle.module.url(forResource: "Fastfile", withExtension: "stencil")
    }

    guard let resourcePath = defaultTemplatePath?.pathComponents.dropLast().joined(separator: "/") else {
      print("⛔️  Resource path not found")
      throw ExitCode.failure
    }

    guard let templateName = defaultTemplatePath?.pathComponents.last else {
      print("⛔️  Template not found")
      throw ExitCode.failure
    }

    guard let path = URL(string: projectPath)?.appendingPathComponent(destinationPath).path else {
      print("⛔️  Destination path not found")
      throw ExitCode.failure
    }

    var environment = Environment()
    environment.loader = FileSystemLoader(paths: [Path(resourcePath)])
    try environment.writeTemplate(name: templateName, with: targets, and: changeLog() ?? "", to: path, relativePath: destinationPath)
    try environment.writeTemplate(name: "packages.yml.stencil", with: targets, and: changeLog() ?? "", to: "\(projectPath)/packages.yml", relativePath: "/")
    try environment.writeTemplate(name: "project.yml.stencil", with: targets, and: changeLog() ?? "", to: "\(projectPath)/project.yml", relativePath: "/")
    throw ExitCode.success
  }

  private func changeLog() -> String? {
    let changes: Commands.Result
    let currentBranch = Commands.run("git branch --show-current")
    if currentBranch.output == "main" {
      let hashes = Commands.run("git log --format=\"%H\" -n 2")
      let lastHash = hashes.output.split(separator: "\n").last ?? ""
      changes = Commands.run("git log --pretty=%B HEAD...\(lastHash)")
      print("ℹ️  Changes between HEAD and \(lastHash): \(changes) ")
    } else {
      changes = Commands.run("git log --pretty=%B \(currentBranch.output)...origin/main")
      print("ℹ️  Changes between \(currentBranch.output) and main: \(changes) ")
    }

    return changes.statusCode == 0 ? changes.output.replacingOccurrences(of: "\"", with: "") : nil
  }

  private func findPackages(at path: String) throws -> [String] {
    print("🚀  Searching for Packages at \(path)")
    return try FileManager.default.subpathsOfDirectory(atPath: path).compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
  }

  private func find(dependency: String) throws -> [Target] {
    let path = try? findPackages(at: projectPath).compactMap { $0.contains(".build") ? nil : $0 }
    var targets = [Target]()
    try path?.forEach { packagePath in
      let relativePackagePath = relativePackagePath(for: packagePath)
      let content = try String(contentsOfFile: packagePath, encoding: .utf8)
      if
        relativePackagePath.components(separatedBy: dependency).last != dependency,
        content.contains(dependency),
        hasTestTarget(in: content),
        let name = getTargetName(form: content) {
        let containsIOS = content.contains(".iOS")
        print("\(containsIOS ? "📱" : "🖥") Found test target \(name) at \(relativePackagePath) with modified dependency \(dependency)")
        if containsIOS {
          targets.append(createTarget(from: content, name: name, relativePackagePath: relativePackagePath))
        }
      }
    }
    return targets
  }

  private func createTarget(from content: String, name: String, relativePackagePath: String) -> Target {
    let destination = content.contains(".iOS") ? iOSDestination : macOSDestination
    if content.components(separatedBy: ".library").count > 2 {
      return Target(name: name, scheme: "\(name)-Package", path: relativePackagePath, destination: destination)
    } else {
      return Target(name: name, scheme: name, path: relativePackagePath, destination: destination)
    }
  }

  private func relativePackagePath(for packagePath: String) -> String {
    String(packagePath
      .replacingOccurrences(of: projectPath, with: "")
      .replacingOccurrences(of: "Package.swift", with: "")
      .dropFirst()
      .dropLast())
  }

  private func create(in content: String) -> Bool {
    let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
    let range = NSRange(location: 0, length: content.utf16.count)
    let commentRegex = try? NSRegularExpression(pattern: "//.*testTarget", options: regexOptions)
    return commentRegex?.firstMatch(in: content, range: range) == nil
  }

  private func hasTestTarget(in content: String) -> Bool {
    guard content.contains(".testTarget") else { return false }
    let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
    let range = NSRange(location: 0, length: content.utf16.count)
    let commentRegex = try? NSRegularExpression(pattern: "//.*testTarget", options: regexOptions)
    return commentRegex?.firstMatch(in: content, range: range) == nil
  }

  func getTargetName(form content: String) -> String? {
    let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
    let range = NSRange(location: 0, length: content.utf16.count)
    let nameRegex = try? NSRegularExpression(pattern: "name:.*\"(.*)\",", options: regexOptions)
    if let nameResult = nameRegex?.firstMatch(in: content, range: range) {
      return String(content[Range(nameResult.range(at: 1), in: content)!])
    }
    return nil
  }

  private func findTestTargets(at path: [String]) throws -> [Target] {
    let currentBranch = Commands.run("git branch --show-current")
    let diff: Commands.Result
    if currentBranch.output == "main" {
      let hashes = Commands.run("git log --format=\"%H\" -n 2")
      let lastHash = hashes.output.split(separator: "\n").last ?? ""
      diff = Commands.run("git diff --name-only --diff-filter=AMDR HEAD..\(lastHash) -- . ':!docs'")
      print("ℹ️  Diffing on main branch between HEAD and \(lastHash)")
    } else {
      diff = Commands.run("git diff --name-only --diff-filter=AMDR \(currentBranch.output)..origin/main -- . ':!docs'")
      print("ℹ️  Diffing on \(currentBranch.output) branch between \(currentBranch.output) and origin/main")
    }

    // Filter out dependency-only changes
    let changedFiles = diff.output.components(separatedBy: "\n").filter { !$0.isEmpty }
    let meaningfulChanges = filterMeaningfulChanges(changedFiles)

    if meaningfulChanges.isEmpty, !changedFiles.isEmpty {
      print("ℹ️  Only dependency resolution files changed (Package.resolved, etc.). Skipping tests.")
      return []
    }

    var targets = [Target]()
    try path.forEach { packagePath in
      let content = try String(contentsOfFile: packagePath, encoding: .utf8)
      if
        hasTestTarget(in: content),
        let name = getTargetName(form: content) {
        let relativePackagePath = relativePackagePath(for: packagePath)
        if hasChangesInPackage(meaningfulChanges, packagePath: relativePackagePath) {
          let containsIOS = content.contains(".iOS")
          print("\(containsIOS ? "📱" : "🖥")  Found test target \(name) at \(relativePackagePath)")
          if containsIOS {
            targets.append(createTarget(from: content, name: name, relativePackagePath: relativePackagePath))
            targets = try Array(Set(targets).union(Set(find(dependency: name))))
          }
        } else {
          print("ℹ️  \(name) at \(relativePackagePath) does not contain any meaningful changes")
        }
      } else {
        print("⚠️  Package \(String(describing: packagePath.split(separator: "/").dropLast().last ?? Substring(packagePath))) does not include a test target")
      }
    }
    return targets
  }

  private func filterMeaningfulChanges(_ changedFiles: [String]) -> [String] {
    let ignoredFiles = [
      "Package.resolved",
      ".package.resolved",
      "Package-resolved.json"
    ]

    let ignoredPatterns = [
      ".resolved",
      ".lock",
      ".lockfile"
    ]

    return changedFiles.filter { filePath in
      let fileName = String(filePath.split(separator: "/").last ?? "")

      // Skip if it's an ignored file
      if ignoredFiles.contains(fileName) {
        print("ℹ️  Ignoring dependency file: \(filePath)")
        return false
      }

      // Skip if it matches an ignored pattern
      for pattern in ignoredPatterns {
        if fileName.hasSuffix(pattern) {
          print("ℹ️  Ignoring dependency file: \(filePath)")
          return false
        }
      }

      return true
    }
  }

  private func hasChangesInPackage(_ changedFiles: [String], packagePath: String) -> Bool {
    return changedFiles.contains { changedFile in
      // Check if the changed file is within the package directory
      // The changed file should start with the package path
      return changedFile.hasPrefix(packagePath + "/") || changedFile == packagePath
    }
  }
}

extension Environment {
  func writeTemplate(name: String, with targets: [Target], and changeLog: String, to path: String, relativePath: String) throws {
    print("⚙️  Generating fastfile at:\(path)...")
    print("ℹ️  Using \(name) Template")
    let filePath = relativePath.split(separator: "/").dropLast().joined(separator: "/")
    if !FileManager.default.fileExists(atPath: filePath), !filePath.isEmpty {
      print("ℹ️  Creating \(filePath) Folder")
      try FileManager.default.createDirectory(atPath: filePath, withIntermediateDirectories: true)
    }
    var destinations = [String: String]()
    var paths = [String: String]()
    var schemes = [String: String]()
    targets.forEach {
      destinations[$0.name] = $0.destination
      paths[$0.name] = $0.path
      schemes[$0.name] = $0.scheme
    }
    let rendering = try renderTemplate(
      name: name,
      context: [
        "destinations": destinations,
        "paths": paths,
        "schemes": schemes,
        "changeLog": changeLog
      ]
    )
    try Path(path).write(rendering)
    print("🍀  Done")
  }
}
