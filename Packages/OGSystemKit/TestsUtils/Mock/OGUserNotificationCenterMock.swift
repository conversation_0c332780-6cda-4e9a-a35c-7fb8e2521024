import Combine
import OGCore
import OGMacros
import OGMock
import OGSystemKit
import UserNotifications

@OGMock
public final class OGUserNotificationCenterMock: OGUserNotificationCenterHandling {
  public init() {}

  public var isPushOptInGranted: CurrentValueSubject<Bool, Never> {
    get { mock.isPushOptInGranted.getter.record() }
    set { mock.isPushOptInGranted.setter.record(newValue) }
  }

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await mock.authorizationStatus()
  }

  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    await mock.requestAuthorization(options: options)
  }
}
