// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGExternalDependencies",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGExternalDependencies",
      targets: ["OGExternalDependencies"]
    )
  ],
  dependencies: [
    .package(
      url: "https://github.com/ZipArchive/ZipArchive.git",
      exact: "2.4.3"
    ),
    .package(
      url: "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git",
      exact: "6.3.0-10"
    ),
    .package(
      url: "https://github.com/snowplow/snowplow-ios-tracker",
      exact: "6.0.7"
    ),
    .package(
      url: "https://github.com/firebase/firebase-ios-sdk.git",
      exact: "11.13.0"
    )
  ],
  targets: [
    .target(
      name: "OGExternalDependencies",
      dependencies: [
        "ZipArchive",
        .product(name: "OGAppKitSDK", package: "og-dx_aac-multiplatform-sdk"),
        .product(name: "SnowplowTracker", package: "snowplow-ios-tracker"),
        .product(name: "FirebaseAnalytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseCrashlytics", package: "firebase-ios-sdk"),
        .product(name: "FirebaseRemoteConfig", package: "firebase-ios-sdk"),
        .product(name: "FirebaseFirestore", package: "firebase-ios-sdk")
      ]
    ),
    .testTarget(
      name: "OGExternalDependenciesTests",
      dependencies: ["OGExternalDependencies"]
    )
  ]
)
