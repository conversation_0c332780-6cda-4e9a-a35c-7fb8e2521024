// swiftlint:disable type_name

import OGDIService
import OGSearch
import SwiftUI

// MARK: - OGNavigationBar

public struct OGNavigationBar<LeadingView: View, TitleView: View, TrailingView: View, ContentView: View>: View {
  @Environment(\.styleOGNavigationBar) private var style
  @Binding private var contentOffset: CGPoint
  @State private var state: OGNavigationBarScrollViewState
  @State private var timer: Timer?
  @ObservedObject private var hasSearchBar: OGObservedBoolean
  @ObservedObject private var hasSearchIcon: OGObservedBoolean
  @ObservedObject private var hasBackButton: OGObservedBoolean
  @OGInjected(\SearchFeatureAdapterContainer.search) private var search

  private let titleView: TitleView
  private let leadingView: LeadingView
  private let trailingView: TrailingView
  private let contentView: () -> ContentView

  public init(
    contentOffset: Binding<CGPoint> = .constant(.zero),
    leadingView: LeadingView = EmptyView(),
    titleView: TitleView = EmptyView(),
    trailingView: TrailingView = EmptyView(),
    hasSearchBar: OGObservedBoolean = OGObservedBoolean(),
    hasSearchIcon: OGObservedBoolean = OGObservedBoolean(),
    hasBackButton: OGObservedBoolean = OGObservedBoolean(true),
    @ViewBuilder content: @escaping () -> ContentView
  ) {
    _contentOffset = contentOffset
    _state = State(
      wrappedValue: OGNavigationBarScrollViewState(
        contentOffset: contentOffset.wrappedValue,
        hasSearchBar: hasSearchBar.value,
        hasSearchIcon: hasSearchIcon.value,
        hasBackButton: hasBackButton.value
      )
    )
    self.titleView = titleView
    self.trailingView = trailingView
    self.hasSearchBar = hasSearchBar
    self.contentView = content
    self.hasSearchIcon = hasSearchIcon
    self.hasBackButton = hasBackButton
    self.leadingView = leadingView
  }

  public var body: some View {
    VStack(spacing: .zero, content: {
      style.makeBody(
        configuration: NavigationBarStyleConfiguration(
          content: NavigationBarStyleConfiguration.Content(
            content: content
          )
        )
      )
      .zIndex(1)
      contentView()
        .zIndex(0)
      Spacer(minLength: 0)
    })
    .onChange(of: hasSearchBar, perform: {
      state.hasSearchBar = $0.value && search.configuration.value.isEnabled
    })
    .onChange(of: hasBackButton, perform: {
      state.hasBackButton = $0.value
    })
    .onChange(of: hasSearchIcon, perform: {
      state.hasSearchIcon = $0.value && search.configuration.value.isEnabled
    })
    .onChange(of: contentOffset, perform: {
      onChangeOf(contentOffset: $0)
    })
    .onReceive(search.configuration, perform: { configuration in
      if !configuration.isEnabled {
        state.hasSearchBar = false
        state.hasSearchIcon = false
      }
    })
    .navigationBarHidden(true)
  }

  var content: some View {
    VStack(spacing: .zero) {
      topBar
      if state.hasSearchBar {
        searchBar
      }
    }
    .frame(maxWidth: .infinity)
    .frame(height: state.navBarHeight)
    .dynamicTypeSize(...DynamicTypeSize.xLarge)
  }

  private var topBar: some View {
    OGNavigationBarTop(
      state: $state,
      leadingView: leadingView,
      titleView: titleView,
      trailingView: trailingView
    )
  }

  @ViewBuilder private var searchBar: some View {
    HStack(spacing: .zero) {
      OGNavigationBarSearchField(state: $state)
    }
    .frame(height: state.searchBarHeight)
  }

  func onChangeOf(contentOffset: CGPoint) {
    state.contentOffset = contentOffset
    timer?.invalidate()
    timer = Timer.scheduledTimer(
      withTimeInterval: 0.4,
      repeats: false
    ) { _ in
      guard state.isSearchBarSlightlyVisible else {
        return
      }
      withAnimation {
        state = OGNavigationBarScrollViewState(
          contentOffset: .init(
            x: 0,
            y: state.scrollDirection == .up ? state.minHeight : state.maxHeight
          ),
          hasSearchBar: hasSearchBar.value,
          hasSearchIcon: hasSearchIcon.value,
          hasBackButton: hasBackButton.value
        )
      }
    }
  }
}

// MARK: - UINavigationController + UIGestureRecognizerDelegate

extension UINavigationController: UIGestureRecognizerDelegate {
  override open func viewDidLoad() {
    super.viewDidLoad()
    interactivePopGestureRecognizer?.delegate = nil
  }
}
