import Foundation
import OGAppKitSDK
import OGCoreTestsUtils
@testable import OGDialogCoordinator

extension OGDialogCoordinatorFeatureConfig {
  public static let stub = OGDialogCoordinatorFeatureConfig(
    isEnabled: true,
    webBridgeNames: ["purchaseCompleted", "webBridgeCallName"],
    behaviorsJson: """
    [
      {
        "action": {
          "type": "navigation",
          "url": "app://pushPromotionLayer?og_origin=screenviews"
        },
        "conditions": [
          {
            "period": 10,
            "type": "screenViews"
          }
        ],
        "disabledUrls": [
          "onboarding"
        ],
        "id": "openPushPromoAfterWelcomeScreen",
        "maxInvocations": 1,
        "precondition": "pushDisabled"
      },
      {
        "action": {
          "type": "navigation",
          "url": "app://pushPromotionLayer?og_origin=orderconfirmation"
        },
        "conditions": [
          {
            "type": "webBridgeCall",
            "webBridgeCallName": "purchaseCompleted"
          }
        ],
        "disabledUrls": [
          "onboarding",
          "customerLoginModal",
          "login"
        ],
        "id": "push",
        "maxInvocations": 1,
        "precondition": "pushDisabled"
      }
    ]
    """,
    debounceMs: 500
  )
}

extension OGDialogCoordinatorState {
  public static let stub = OGDialogCoordinatorState(
    isAwaitingUpdate: false
  )
  public static func stub(willStartNavigateAt _: TimeInterval) -> OGDialogCoordinatorState {
    OGDialogCoordinatorState(
      isAwaitingUpdate: false
    )
  }

  public static func stub(
    willStartNavigateAt _: TimeInterval,
    didStartNavigateAt _: TimeInterval
  )
    -> OGDialogCoordinatorState {
    OGDialogCoordinatorState(
      isAwaitingUpdate: false
    )
  }
}

