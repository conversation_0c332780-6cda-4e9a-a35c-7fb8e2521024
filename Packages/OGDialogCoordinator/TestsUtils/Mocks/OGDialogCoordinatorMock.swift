import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGCore
import <PERSON>GCoreTestsUtils
import OGDialogCoordinator
import <PERSON>GMock
import OGRouter
import OGWebBridge

// MARK: - OGDialogCoordinatorMock

@OGMock
public final class OGDialogCoordinatorWebBrideProvidableMock: OGDialogCoordinatorWebBrideProvidable {

  private let _webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable

  public init(webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable = OGDialogCoordinatorWebBridgeActionHandlerMock()) {
    self._webBridgeActionHandler = webBridgeActionHandler
  }

  public var webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable {
    get async { _webBridgeActionHandler }
  }



  public func addWebBridgeNames(names: [String]) {
    mock.addWebBridgeNames(names: names)
  }


}
