import OGAppKitSDK
import <PERSON>GCore
import OGMock

@OGMock
public final class OGAppKitCoordinatorMock: OGAppKitSDK.OGCoordinator {
  
  public init() {}
  
  public func setPushPermissionDelegate(delegate: any PushPermissionDelegate) {
    mock.setPushPermissionDelegate(delegate: delegate)
  }
  
  public func configure(config: OGAppKitSDK.CoordinatorConfig) {
    mock.configure(config: config)
  }
  
  public func configure(debounceMs: Int64, behaviorsJson: String, pushOptInGranted: Bool) {
    mock.configure(debounceMs: debounceMs, behaviorsJson: behaviorsJson, pushOptInGranted: pushOptInGranted)
  }
  
  public func getActions() -> OGAppKitSDK.SkieSwiftFlow<any Action> {
    mock.getActions()
  }

  public func onEvent(event: any OGAppKitSDK.CoordinatorEvent) {
    mock.onEvent(event: event)
  }
}
