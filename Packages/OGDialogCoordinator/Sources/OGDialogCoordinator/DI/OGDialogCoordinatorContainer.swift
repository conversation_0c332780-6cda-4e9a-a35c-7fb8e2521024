import OGDIService

public final class OGDialogCoordinatorContainer: OGDISharedContainer {
  public static var shared: OGDialogCoordinatorContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var dialogCoordinatorStore: OGDIService<OGDialogCoordinatorStore> {
    self {
      OGDialogCoordinatorStore.make()
    }.cached
  }

  var dialogCoordinatorWebBrideProvider: OGDIService<OGDialogCoordinatorWebBrideProvidable> {
    self {
      OGDialogCoordinatorWebBrideProvider()
    }.cached
  }
  
  var pushPermissionProvider: OGDIService<OGDialogCoordinatorPushPermissionProvidable> {
    self {
      OGDialogCoordinatorPushPermissionProvider()
    }.cached
  }
  
  
  var webBridgeActionHandler: OGDIService<OGDialogCoordinatorWebBridgeActionHandlable> {
    self {
      OGDialogCoordinatorWebBridgeActionHandler()
    }.cached
  }
}
