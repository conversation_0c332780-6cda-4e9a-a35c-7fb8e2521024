import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGDIService
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros
import OGNavigation
import OGScreenViewUpdate
import UIKit
import UserNotifications

// MARK: - OGDialogCoordinatorFeatureConfig

public struct OGDialogCoordinatorFeatureConfig: Equatable, Sendable {
  public var isEnabled: Bool
  public var webBridgeNames: [String]
  public var behaviorsJson: String
  public var debounceMs: Int

  public init(isEnabled: Bool, webBridgeNames: [String], behaviorsJson: String, debounceMs: Int) {
    self.isEnabled = isEnabled
    self.webBridgeNames = webBridgeNames
    self.behaviorsJson = behaviorsJson
    self.debounceMs = debounceMs
  }
}

// MARK: - OGDialogCoordinatorFeatureAdapter

public final class OGDialogCoordinatorFeatureAdapter: OGFeatureAdapter, CoordinatorFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.coordinator.value }

  public let configuration: CurrentValueSubject<OGDialogCoordinatorFeatureConfig, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(
    configuration: OGDialogCoordinatorFeatureConfig?
  ) {
    let configuration = configuration ?? OGDialogCoordinatorFeatureConfig(isEnabled: false, webBridgeNames: [], behaviorsJson: "", debounceMs: 0)
    self.configuration = CurrentValueSubject(configuration)
    super.init()
    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value

      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }

      updatedConfiguration.isEnabled = feature.isEnabled

      do {
        let behaviorsModel = try feature.customValue([AnyJSONType].self, for: OGFeatureKey.CustomValues.Coordinator.behaviors) ?? []
        
        let jsonData = try JSONEncoder().encode(behaviorsModel)
        updatedConfiguration.behaviorsJson = String(data: jsonData, encoding: .utf8) ?? updatedConfiguration.behaviorsJson
      } catch {
        logger.critical(domain: .decoding, message: error.localizedDescription)
      }
      do {
        let dialogBehaviors = try feature.customValue([DialogBehavior].self, for: OGFeatureKey.CustomValues.Coordinator.behaviors) ?? []
        updatedConfiguration.webBridgeNames = Array(Set(dialogBehaviors
          .flatMap(\.conditions)
          .compactMap(\.webBridgeCallName)))
      } catch {
        logger.critical(domain: .decoding, message: error.localizedDescription)
      }
      
      let debounceMs: Int = (feature.customValue(for: OGFeatureKey.CustomValues.Coordinator.debounceMs)) ?? self.configuration.value.debounceMs
      updatedConfiguration.debounceMs = debounceMs
      self.configuration.send(updatedConfiguration)
    }
    .store(in: &subscriptions)
  }
}

// MARK: - CoordinatorFeatureAdaptable

public protocol CoordinatorFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<OGDialogCoordinatorFeatureConfig, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.Coordinator

extension OGFeatureKey.CustomValues {
  public enum Coordinator: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case behaviors
    case debounceMs
  }
}

extension OGIdentifier {
  public static let coordinator = #identifier("coordinator")
}
