import OGAppKitSDK
import OGDIService
import OGSystemKit

protocol OGDialogCoordinatorPushPermissionProvidable: PushPermissionDelegate {
  func isPushOptInGranted() async  -> Bool
}

class OGDialogCoordinatorPushPermissionProvider: OGDialogCoordinatorPushPermissionProvidable {
  @OGInjected(\OGSystemKitContainer.userNotificationCenter) private var userNotificationCenter
  func isPushOptInGranted() async  -> Bool {
    await userNotificationCenter.authorizationStatus() == .authorized ? true : false
  }
  
  func __isPushOptInGranted() async throws -> KotlinBoolean {
    let granted = await isPushOptInGranted()
    return KotlinBoolean(value: granted)
  }
}
