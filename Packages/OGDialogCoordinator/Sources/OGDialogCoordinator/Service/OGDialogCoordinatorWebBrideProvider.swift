import Combine
import Foundation
@preconcurrency import OGAppKitSDK
import OGCore
import OGDIService
import OGScreenViewUpdate
import OGSystemKit
import OGWebBridge
import UIKit
import UserNotifications

// MARK: - OGDialogCoordinable

public protocol OGDialogCoordinatorWebBrideProvidable {
  var webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable { get async }
  func addWebBridgeNames(names: [String])
}

// MARK: - OGDialogCoordinator

final class OGDialogCoordinatorWebBrideProvider: OGDialogCoordinatorWebBrideProvidable {
  @OGInjected(\OGDialogCoordinatorContainer.webBridgeActionHandler) private var _webBridgeActionHandler
  @OGInjected(\OGWebBridgeContainer.globalWebBridge) private var globalWebBridge

  var webBridgeActionHandler: OGDialogCoordinatorWebBridgeActionHandlable {
    get async { _webBridgeActionHandler }
  }

  init() {
    globalWebBridge.addActionHandler(_webBridgeActionHandler)
  }

  func addWebBridgeNames(names: [String]) {
    _webBridgeActionHandler.update(webBridgeCallName: names)
  }
}
