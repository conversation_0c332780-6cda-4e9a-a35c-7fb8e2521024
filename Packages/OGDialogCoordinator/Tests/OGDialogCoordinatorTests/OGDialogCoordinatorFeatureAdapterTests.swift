import XCTest
import Combine
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils

final class OGDialogCoordinatorFeatureAdapterTests: XCTestCase {
  private var cancellables: Set<AnyCancellable>!
  
  override func setUpWithError() throws {
    try super.setUpWithError()
    cancellables = Set<AnyCancellable>()
  }
  
  override func tearDownWithError() throws {
    cancellables = nil
    try super.tearDownWithError()
  }
  
  func test_featureConfig_webBridgeNames_extractsCorrectly() {
    let behaviorsJson = """
    [
      {
        "action": {
          "type": "navigation",
          "url": "app://pushPromotionLayer?og_origin=orderconfirmation"
        },
        "conditions": [
          {
            "type": "webBridgeCall",
            "webBridgeCallName": "purchaseCompleted"
          }
        ],
        "disabledUrls": [
          "onboarding",
          "customerLoginModal",
          "login"
        ],
        "id": "push",
        "maxInvocations": 1,
        "precondition": "pushDisabled"
      },
      {
        "action": {
          "type": "navigation",
          "url": "app://review"
        },
        "conditions": [
          {
            "type": "webBridgeCall",
            "webBridgeCallName": "purchaseCompleted"
          }
        ],
        "id": "review",
        "precondition": "none"
      }
    ]
    """
    
    let config = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: ["purchaseCompleted"],
      behaviorsJson: behaviorsJson,
      debounceMs: 500
    )
    
    XCTAssertEqual(config.webBridgeNames, ["purchaseCompleted"])
    XCTAssertTrue(config.behaviorsJson.contains("purchaseCompleted"))
    XCTAssertTrue(config.behaviorsJson.contains("app://pushPromotionLayer"))
    XCTAssertTrue(config.behaviorsJson.contains("app://review"))
  }
  
  func test_featureConfig_withComplexBehaviorsJson() {
    let behaviorsJson = """
    [
      {
        "action": {
          "type": "navigation",
          "url": "app://pushPromotionLayer?og_origin=screenviews"
        },
        "conditions": [
          {
            "period": 10,
            "type": "screenViews"
          }
        ],
        "disabledUrls": [
          "onboarding"
        ],
        "id": "openPushPromoAfterWelcomeScreen",
        "maxInvocations": 1,
        "precondition": "pushDisabled"
      },
      {
        "action": {
          "type": "navigation",
          "url": "app://pushPromotionLayer?og_origin=orderconfirmation"
        },
        "conditions": [
          {
            "type": "webBridgeCall",
            "webBridgeCallName": "purchaseCompleted"
          }
        ],
        "disabledUrls": [
          "onboarding",
          "customerLoginModal",
          "login"
        ],
        "id": "push",
        "maxInvocations": 1,
        "precondition": "pushDisabled"
      },
      {
        "action": {
          "type": "navigation",
          "url": "app://review"
        },
        "conditions": [
          {
            "start": 10,
            "type": "appStarts"
          }
        ],
        "id": "review",
        "maxInvocations": 1,
        "precondition": "none"
      },
      {
        "action": {
          "type": "navigation",
          "url": "app://review"
        },
        "conditions": [
          {
            "type": "webBridgeCall",
            "webBridgeCallName": "purchaseCompleted"
          }
        ],
        "id": "review",
        "precondition": "none"
      }
    ]
    """
    
    let config = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: ["purchaseCompleted"],
      behaviorsJson: behaviorsJson,
      debounceMs: 500
    )
    
    // Test that the configuration is properly set
    XCTAssertTrue(config.isEnabled)
    XCTAssertEqual(config.debounceMs, 500)
    XCTAssertEqual(config.webBridgeNames, ["purchaseCompleted"])
    
    // Test that the JSON contains expected behaviors
    XCTAssertTrue(config.behaviorsJson.contains("openPushPromoAfterWelcomeScreen"))
    XCTAssertTrue(config.behaviorsJson.contains("push"))
    XCTAssertTrue(config.behaviorsJson.contains("review"))
    XCTAssertTrue(config.behaviorsJson.contains("purchaseCompleted"))
    XCTAssertTrue(config.behaviorsJson.contains("screenViews"))
    XCTAssertTrue(config.behaviorsJson.contains("appStarts"))
    XCTAssertTrue(config.behaviorsJson.contains("webBridgeCall"))
  }
  
  func test_featureConfig_withEmptyWebBridgeNames() {
    let config = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: [],
      behaviorsJson: "[]",
      debounceMs: 0
    )
    
    XCTAssertTrue(config.webBridgeNames.isEmpty)
    XCTAssertEqual(config.behaviorsJson, "[]")
    XCTAssertEqual(config.debounceMs, 0)
  }
  
  func test_featureConfig_withMultipleWebBridgeNames() {
    let config = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: ["purchaseCompleted", "userRegistered", "cartUpdated"],
      behaviorsJson: "[{\"id\":\"test\"}]",
      debounceMs: 1000
    )
    
    XCTAssertEqual(config.webBridgeNames.count, 3)
    XCTAssertTrue(config.webBridgeNames.contains("purchaseCompleted"))
    XCTAssertTrue(config.webBridgeNames.contains("userRegistered"))
    XCTAssertTrue(config.webBridgeNames.contains("cartUpdated"))
  }
  
  func test_featureConfig_disabled() {
    let config = OGDialogCoordinatorFeatureConfig(
      isEnabled: false,
      webBridgeNames: ["purchaseCompleted"],
      behaviorsJson: "[{\"id\":\"test\"}]",
      debounceMs: 500
    )
    
    XCTAssertFalse(config.isEnabled)
    XCTAssertEqual(config.webBridgeNames, ["purchaseCompleted"])
    XCTAssertEqual(config.debounceMs, 500)
  }
}
