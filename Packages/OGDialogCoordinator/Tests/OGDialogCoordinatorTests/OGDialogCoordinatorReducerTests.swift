import OGAppKitSDK
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import XCTest

final class OGDialogCoordinatorReducerTests: XCTestCase {
  func test_GIVEN_anyState_WHEN_anyAction_THEN_stateUnchanged() {
    // Test that the reducer is a no-op for all actions
    var state = OGDialogCoordinatorState.initial
    let originalState = state

    // Test all action types
    let actions: [OGDialogCoordinatorAction] = [
      ._update(debounceMs: 1_000, behaviorsJson: "[]", pushOptInGranted: true),
      ._navigate("https://example.com"),
      ._receivedScreenView(URL(string: "https://example.com")!),
      ._receivedWebBridge("testWebBridge")
    ]

    for action in actions {
      OGDialogCoordinatorState.Reducer.reduce(&state, with: action)
      XCTAssertEqual(originalState, state, "State should remain unchanged for action: \(action)")
    }
  }

  func test_GIVEN_stubState_WHEN_multipleActions_THEN_stateUnchanged() {
    var state = OGDialogCoordinatorState.stub
    let originalState = state

    // Apply multiple actions in sequence
    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._update(debounceMs: 500, behaviorsJson: "[]", pushOptInGranted: true)
    )

    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._receivedScreenView(URL(string: "https://example.com")!)
    )

    OGDialogCoordinatorState.Reducer.reduce(
      &state,
      with: ._navigate("test://url")
    )

    // State should remain unchanged for this implementation
    XCTAssertEqual(originalState, state)
  }
}
