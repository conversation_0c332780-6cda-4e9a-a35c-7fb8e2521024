import Combine
import OGAppKitSDK
import OGCore
import OGCoreTestsUtils
@testable import OGDialogCoordinator
import OGDialogCoordinatorTestsUtils
import OGDIService
import OGScreenViewUpdate
import XCTest

final class OGDialogCoordinatorConnectorTests: XCTestCase {
  private var dialogCoordinatorWebBridgeActionHandlerMock: OGDialogCoordinatorWebBridgeActionHandlerMock!
  private var dialogCoordinatorWebBrideProvidableMock: OGDialogCoordinatorWebBrideProvidableMock!
  override func setUpWithError() throws {
    dialogCoordinatorWebBridgeActionHandlerMock = OGDialogCoordinatorWebBridgeActionHandlerMock()
    dialogCoordinatorWebBrideProvidableMock = OGDialogCoordinatorWebBrideProvidableMock(webBridgeActionHandler:
      dialogCoordinatorWebBridgeActionHandlerMock
    )
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.register {
      self.dialogCoordinatorWebBridgeActionHandlerMock
    }
    OGDialogCoordinatorFeatureAdapterContainer.shared.feature.register {
      FeatureAdapterMock()
    }
    OGDialogCoordinatorContainer.shared.dialogCoordinatorWebBrideProvider.register {
      self.dialogCoordinatorWebBrideProvidableMock
    }
    OGScreenViewUpdateContainer.shared.screenViewUpdate.register {
      ScreenViewUpdateMock()
    }
    let pushPermissionProvider = OGDialogCoordinatorPushPermissionProviderMock()
    OGDialogCoordinatorContainer.shared.pushPermissionProvider.register {
      pushPermissionProvider
    }

    pushPermissionProvider.mock.isPushOptInGrantedCalls.mockCall { _ in
      true
    }

    try super.setUpWithError()
  }

  override func tearDownWithError() throws {
    OGDialogCoordinatorFeatureAdapterContainer.shared.feature.reset()
    OGDialogCoordinatorContainer.shared.dialogCoordinatorWebBrideProvider.reset()
    OGScreenViewUpdateContainer.shared.screenViewUpdate.reset()
    OGDialogCoordinatorContainer.shared.coordinator.reset()
    try super.tearDownWithError()
  }

  func test_GIVEN_configure_dispatch_update() async throws {
    let expectation = expectation(description: "Expected _update event")
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._update = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    await fulfillment(of: [expectation], timeout: 0.5)

    let actorEvents = await testActor.events
    XCTAssertTrue(actorEvents.contains { if case ._update = $0 { return true } else { return false } })
  }

  func test_WHEN_screenView_dispatch_receivedScreenView() async throws {
    let expectation = expectation(description: "Expected _receivedScreenView event")
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._receivedScreenView = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    OGScreenViewUpdateContainer.shared.screenViewUpdate().send(with: .stub)

    await fulfillment(of: [expectation], timeout: 0.5)

    let actorEvents = await testActor.events
    XCTAssertTrue(actorEvents.contains { if case ._update = $0 { return true } else { return false } })
    XCTAssertTrue(actorEvents.contains { if case ._receivedScreenView = $0 { return true } else { return false } })
  }

  func test_WHEN_customWebBridgeActionHandler_usedByConnector() async throws {
    let expectation = expectation(description: "Expected _receivedWebBridge event from dialogCoordinatorWebBridgeActionHandlerMock")
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case let ._receivedWebBridge(callName) = event, callName == "webBridgeCallName" {
        expectation.fulfill()
      } else {
        print("Received event: \(event)")
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send("webBridgeCallName")

    await fulfillment(of: [expectation], timeout: 0.5)
    let actorEvents = await testActor.events
    XCTAssertTrue(actorEvents.contains { if case let ._receivedWebBridge(callName) = $0 { return callName == "webBridgeCallName" } else { return false } })
  }

  func test_WHEN_webBridgeActionHandler_sendsEmptyCallName_THEN_receivedWebBridgeDispatched() async throws {
    let expectation = expectation(description: "Expected _receivedWebBridge event with empty call name")
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case let ._receivedWebBridge(callName) = event, callName == "" {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send("")

    await fulfillment(of: [expectation], timeout: 0.5)
    let actorEvents = await testActor.events
    XCTAssertTrue(actorEvents.contains { if case let ._receivedWebBridge(callName) = $0 { return callName == "" } else { return false } })
  }

  func test_WHEN_webBridgeActionHandler_sendsNilCallName_THEN_noReceivedWebBridgeDispatched() async throws {
    let expectation = expectation(description: "Expected only _update event, no _receivedWebBridge")
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._update = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send(nil)

    await fulfillment(of: [expectation], timeout: 0.5)
    let actorEvents = await testActor.events
    XCTAssertTrue(actorEvents.contains { if case ._update = $0 { return true } else { return false } })
    XCTAssertFalse(actorEvents.contains { if case ._receivedWebBridge = $0 { return true } else { return false } })
  }

  func test_WHEN_screenViewUpdate_sendsMultipleEvents_THEN_multipleReceivedScreenViewDispatched() async throws {
    let expectation = expectation(description: "Expected multiple _receivedScreenView events")
    expectation.expectedFulfillmentCount = 2
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._receivedScreenView = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    OGScreenViewUpdateContainer.shared.screenViewUpdate().send(with: .stub)
    OGScreenViewUpdateContainer.shared.screenViewUpdate().send(with: URL(string: "https://example.com")!)

    await fulfillment(of: [expectation], timeout: 1.0)
    let actorEvents = await testActor.events
    let screenViewEvents = actorEvents.filter { if case ._receivedScreenView = $0 { return true } else { return false } }
    XCTAssertEqual(screenViewEvents.count, 2)
  }

  func test_WHEN_webBridgeActionHandler_sendsMultipleCallNames_THEN_multipleReceivedWebBridgeDispatched() async throws {
    let expectation = expectation(description: "Expected multiple _receivedWebBridge events")
    expectation.expectedFulfillmentCount = 3
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._receivedWebBridge = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send("call1")
    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send("call2")
    dialogCoordinatorWebBridgeActionHandlerMock.webBridgeCallName.send("call3")

    await fulfillment(of: [expectation], timeout: 1.0)
    let actorEvents = await testActor.events
    let webBridgeEvents = actorEvents.filter { if case ._receivedWebBridge = $0 { return true } else { return false } }
    XCTAssertEqual(webBridgeEvents.count, 3)
  }

  func test_WHEN_featureConfigurationChanges_THEN_updateEventDispatched() async throws {
    let expectation = expectation(description: "Expected _update event when feature config changes")
    expectation.expectedFulfillmentCount = 2 // Initial + change
    let testActor = TestEventActor<OGDialogCoordinatorAction>()

    let dispatch = { event in
      await testActor.addEvent(event)
      if case ._update = event {
        expectation.fulfill()
      }
    }

    let sut = OGDialogCoordinatorConnector()
    await sut.configure(dispatch: dispatch)

    // Simulate feature configuration change
    let newConfig = OGDialogCoordinatorFeatureConfig(isEnabled: false, webBridgeNames: [], behaviorsJson: "[]", debounceMs: 1_000)
    OGDialogCoordinatorFeatureAdapterContainer.shared.feature().configuration.send(newConfig)

    await fulfillment(of: [expectation], timeout: 1.0)
    let actorEvents = await testActor.events
    let updateEvents = actorEvents.filter { if case ._update = $0 { return true } else { return false } }
    XCTAssertGreaterThanOrEqual(updateEvents.count, 2)
  }
}
