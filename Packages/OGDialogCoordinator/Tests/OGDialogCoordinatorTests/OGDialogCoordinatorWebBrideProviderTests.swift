import Combine
import OGCore
import OGCoreTestsUtils
import OGDialogCoordinatorTestsUtils
import OGFeatureAdapter
import OGMock
import OGScreenViewUpdate
import OGStorage
import OGSystemKit
import OGWebBridge
import OGWebBridgeTestsUtils
import XCTest

@testable import OGDialogCoordinator

final class OGDialogCoordinatorWebBrideProviderTests: XCTestCase {
  private var webBridgeMock: WebBridgeMock!
  private var webBridgeHandlerMock: OGDialogCoordinatorWebBridgeActionHandlerMock!
  private var sut: OGDialogCoordinatorWebBrideProvider!

  override func setUpWithError() throws {
    try super.setUpWithError()

    webBridgeMock = WebBridgeMock()

    OGWebBridgeContainer.shared.globalWebBridge.register { self.webBridgeMock }

    webBridgeHandlerMock = OGDialogCoordinatorWebBridgeActionHandlerMock()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.register { self.webBridgeHandlerMock }

    sut = OGDialogCoordinatorWebBrideProvider()
  }

  override func tearDownWithError() throws {
    OGWebBridgeContainer.shared.globalWebBridge.reset()
    OGDialogCoordinatorContainer.shared.webBridgeActionHandler.reset()
    webBridgeMock = nil
    webBridgeHandlerMock = nil
    sut = nil

    try super.tearDownWithError()
  }

  func test_WHEN_init_THEN_webBridgeHandlerAdded() {
    XCTAssertEqual(webBridgeMock.mock.addActionHandlerCalls.callsCount, 1)
    XCTAssertEqual(
      webBridgeMock.mock.addActionHandlerCalls.latestCall?.webBridgeNames,
      webBridgeHandlerMock.webBridgeNames
    )
  }

  func test_WHEN_addWebBridgeNames_THEN_handlerUpdated() {
    let names = ["testName1", "testName2"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }



  func test_WHEN_addWebBridgeNames_withEmptyArray_THEN_handlerUpdated() {
    let names: [String] = []
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_WHEN_addWebBridgeNames_withMultipleNames_THEN_handlerUpdated() {
    let names = ["name1", "name2", "name3", "name4"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }

  func test_WHEN_addWebBridgeNames_calledMultipleTimes_THEN_handlerUpdatedEachTime() {
    let firstNames = ["first"]
    let secondNames = ["second", "third"]

    sut.addWebBridgeNames(names: firstNames)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.callsCount, 1)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, firstNames)

    sut.addWebBridgeNames(names: secondNames)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.callsCount, 2)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, secondNames)
  }

  func test_WHEN_addWebBridgeNames_withDuplicateNames_THEN_handlerUpdated() {
    let names = ["duplicate", "duplicate", "unique"]
    sut.addWebBridgeNames(names: names)
    XCTAssertEqual(webBridgeHandlerMock.mock.updateCalls.latestCall, names)
  }
}

// MARK: - OGDialogCoordinatorFeatureConfig Tests

extension OGDialogCoordinatorTests {
  func test_featureConfig_withWebBridgeNames() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: ["webBridge1", "webBridge2"],
      behaviorsJson: "[{\"id\":\"test\"}]",
      debounceMs: 250
    )

    XCTAssertEqual(featureConfig.webBridgeNames, ["webBridge1", "webBridge2"])
    XCTAssertEqual(featureConfig.behaviorsJson, "[{\"id\":\"test\"}]")
    XCTAssertEqual(featureConfig.debounceMs, 250)
    XCTAssertTrue(featureConfig.isEnabled)
  }

  func test_featureConfig_withEmptyWebBridgeNames() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(
      isEnabled: true,
      webBridgeNames: [],
      behaviorsJson: "[]",
      debounceMs: 750
    )

    XCTAssertEqual(featureConfig.webBridgeNames, [])
    XCTAssertEqual(featureConfig.behaviorsJson, "[]")
    XCTAssertEqual(featureConfig.debounceMs, 750)
  }

  func test_featureConfig_withZeroDebounce() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(
      isEnabled: false,
      webBridgeNames: ["testBridge"],
      behaviorsJson: "[{\"id\":\"rating\"}]",
      debounceMs: 0
    )

    XCTAssertEqual(featureConfig.debounceMs, 0)
    XCTAssertFalse(featureConfig.isEnabled)
    XCTAssertEqual(featureConfig.webBridgeNames, ["testBridge"])
  }

  func test_featureConfig_withDisabledFeature() {
    let featureConfig = OGDialogCoordinatorFeatureConfig(
      isEnabled: false,
      webBridgeNames: ["testBridge"],
      behaviorsJson: "[{\"id\":\"push\"}]",
      debounceMs: 500
    )

    XCTAssertFalse(featureConfig.isEnabled)
    XCTAssertEqual(featureConfig.debounceMs, 500)
    XCTAssertEqual(featureConfig.webBridgeNames, ["testBridge"])
    XCTAssertEqual(featureConfig.behaviorsJson, "[{\"id\":\"push\"}]")
  }
}
