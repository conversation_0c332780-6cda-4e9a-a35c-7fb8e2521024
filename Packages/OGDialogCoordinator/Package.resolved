{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "061b3afe0358a0da7ce568f8272c847910be3dd7", "version": "2.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "3663b1aa6c7a1bed67ee80fd09dc6d0f9c3bb660", "version": "11.13.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "543071966b3fb6613a2fc5c6e7112d1e998184a7", "version": "11.13.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "c756a29784521063b6a1202907e2cc47f41b667c", "version": "4.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "ios-library", "kind": "remoteSourceControl", "location": "https://github.com/urbanairship/ios-library", "state": {"revision": "53040c77617a2acc5d9a7b69cf5bbbf36f94ad4b", "version": "17.7.3"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "navigationbackport", "kind": "remoteSourceControl", "location": "https://github.com/johnpatrickmorgan/NavigationBackport", "state": {"revision": "fb860a404f8c0aabeca1016400e41ece5363a369", "version": "0.9.0"}}, {"identity": "og-dx_aac-multiplatform-sdk", "kind": "remoteSourceControl", "location": "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git", "state": {"revision": "504d13584d128953ed7fe3b32abef0d94c9aa8cf", "version": "6.3.0-10"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "snowplow-ios-tracker", "kind": "remoteSourceControl", "location": "https://github.com/snowplow/snowplow-ios-tracker", "state": {"revision": "20b1fea9c58334e569cb63d71875d3c2d0243483", "version": "6.0.7"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms", "state": {"revision": "9cfed92b026c524674ed869a4ff2dcfdeedf8a2a", "version": "0.1.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "a902f1823a7ff3c9ab2fba0f992396b948eda307", "version": "1.0.5"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-syntax.git", "state": {"revision": "0687f71944021d616d34d922343dcef086855920", "version": "600.0.1"}}, {"identity": "ziparchive", "kind": "remoteSourceControl", "location": "https://github.com/ZipArchive/ZipArchive.git", "state": {"revision": "38e0ce0598e06b034271f296a8e15b149c91aa19", "version": "2.4.3"}}], "version": 2}