// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGDialogCoordinator",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGDialogCoordinator",
      targets: ["OGDialogCoordinator"]
    ),
    .library(
      name: "OGDialogCoordinatorTestsUtils",
      targets: ["OGDialogCoordinatorTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../OGDomainStore"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGRouter"),
    .package(path: "../OGCore"),
    .package(path: "../OGWebBridge"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureAdapter"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureCore"),
    .package(path: "../OGSystemKit"),
    .package(path: "Packages/OGScreenViewUpdate")

  ],
  targets: [
    .target(
      name: "OGDialogCoordinator",
      dependencies: [
        "OGDIService",
        "OGFeatureAdapter",
        "OGRouter",
        "OGFeatureCore",
        "OGDomainStore",
        "OGWebBridge",
        "OGCore",
        "OGScreenViewUpdate",
        "OGSystemKit"
      ]
    ),
    .target(
      name: "OGDialogCoordinatorTestsUtils",
      dependencies: [
        "OGDialogCoordinator",
        .product(name: "OGCoreTestsUtils", package: "OGCore"),
        .product(name: "OGDomainStoreTestsUtils", package: "OGDomainStore")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGDialogCoordinatorTests",
      dependencies: [
        "OGDialogCoordinator",
        "OGDialogCoordinatorTestsUtils",
        .product(name: "OGWebBridgeTestsUtils", package: "OGWebBridge"),
        .product(name: "OGCoreTestsUtils", package: "OGCore"),
        .product(name: "OGRouterTestsUtils", package: "OGRouter"),
        .product(name: "OGSystemKitTestsUtils", package: "OGSystemKit")
      ]
    )
  ]
)
