import Foundation
import OGAppEnvironment
import OGAppKitSDK
import OGDIService
import OGStorage

/// The OGCoreContainer is a container for core services. It provides a way to register and retrieve these services.
public final class OGCoreContainer: OGDISharedContainer {
  public static var shared: OGCoreContainer = .init()

  public var manager: OGDIContainerManager = .init()

  public var storage: OGDIService<any AnyPersistable> {
    self { OGStorage() }
  }

  public var coordinator: OGDIService<any OGCoordinator> {
    self {
      OGAppKitSdk.shared.coordinator()
    }.cached
  }

  public var avCaptureDevicePermissionService: OGDIService<AVCaptureDevicePermissionServicing> {
    self { AVCaptureDevicePermissionService() }
  }

  public var appEnvironment: OGDIService<OGAppEnvironmental> {
    self { OGAppEnvironment(isDebugOrBetaBuild: false, isDebugBuild: false) }
  }

  public var logger: OGDIService<any OGLoggingDistributable> {
    self {
      OGLoggingDistributor()
    }.cached
  }

  public var consoleLogger: OGDIService<OGLogReceivable> {
    self {
      OGConsoleLogger(logDistributable: self.logger())
    }.cached
  }

  public var deviceTokenReceiver: OGDIService<OGDeviceTokenReceivable> {
    self {
      OGDeviceTokenReceiver()
    }.cached
  }

  public var deviceDeepLinkReceiver: OGDIService<OGDeviceDeepLinkReceivable> {
    self {
      OGDeviceDeepLinkReceiver()
    }.cached
  }

  public var titlePublisher: OGDIService<OGTitlePublishing> {
    self {
      OGTitlePublisher()
    }.cached
  }
}
