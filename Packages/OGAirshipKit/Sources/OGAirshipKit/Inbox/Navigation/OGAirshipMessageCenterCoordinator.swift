import AirshipCore
import AirshipMessageCenter
import OGDeepLinkHandler
import OGIdentifier
import <PERSON>GRouter
import WebKit

// MARK: - OGAirshipMessageCenterCoordinable

public protocol OGAirshipMessageCenterCoordinable {
  func authHeader() async -> String?
  func createNavigationDelegate() -> WKNavigationDelegate
}

// MARK: - OGAirshipMessageCenterCoordinator

public final class OGAirshipMessageCenterCoordinator: OGAirshipMessageCenterCoordinable {
  private let router: OGRoutePublishing
  private let inboxService: OGAirshipInboxServing?
  public init(
    router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
    inboxService: OGAirshipInboxServing? = OGAirshipContainer.shared.inboxService()
  ) {
    self.router = router
    self.inboxService = inboxService
    MessageCenter.shared.displayDelegate = self
  }

  public func createNavigationDelegate() -> WKNavigationDelegate {
    NativeBridge()
  }
}

// MARK: MessageCenterDisplayDelegate

extension OGAirshipMessageCenterCoordinator: MessageCenterDisplayDelegate {
  public func authHeader() async -> String? {
    guard
      let user = await MessageCenter.shared.inbox.user
    else { return nil }
    return AirshipUtils.authHeader(username: user.username, password: user.password)
  }

  public func displayMessageCenter(messageID: String) {
    Task {
      await inboxService?.markRead(messageId: messageID)
    }
    router.send(OGRoute(OGIdentifier.inboxDetail.value, data: messageID))
  }

  public func displayMessageCenter() {
    router.send(OGRoute(OGIdentifier.inbox.value))
  }

  public func dismissMessageCenter() {
    router.pop()
  }
}
