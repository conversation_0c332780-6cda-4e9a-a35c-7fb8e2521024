import AirshipCore
import Combine
import Foundation
import OGCore
import OGDIService
import OG<PERSON>ogger
import OGTenantKit

// MARK: - OGAirshipPushServing

public protocol OGAirshipPushServing: RegistrationDelegate, PushNotificationDelegate, DeepLinkDelegate {}

// MARK: - OGAirshipPushService

public final class OGAirshipPushService: NSObject, OGAirshipPushServing {
  @OGInjected(\OGCoreContainer.logger) private var logger
  @OGInjected(\OGTenantContainer.observerService) private var tenantService
  @OGInjected(\OGAirshipFeatureAdapterContainer.airshipPush) private var airshipPushFeature
  private var cancellables = Set<AnyCancellable>()
  private let airshipChannel: OGAirshipChannelServing
  private let airshipPushSetup: OGAirshipPushCreatable
  private let authorizationStatus: () -> UAAuthorizationStatus
  private let userPushNotificationsEnabled: (Bool) -> Void
  public init(
    airshipChannel: OGAirshipChannelServing = OGAirshipChannelService(),
    airshipPushSetup: OGAirshipPushCreatable = OGAirshipPushSetup(),
    authorizationStatus: @escaping () -> UAAuthorizationStatus = { Airship.push.authorizationStatus },
    userPushNotificationsEnabled: @escaping (Bool) -> Void = { isEnabled in Airship.push.userPushNotificationsEnabled = isEnabled }
  ) {
    self.airshipChannel = airshipChannel
    self.airshipPushSetup = airshipPushSetup
    self.authorizationStatus = authorizationStatus
    self.userPushNotificationsEnabled = userPushNotificationsEnabled
    super.init()
    setupPush()
    watch()
  }

  private func setupPush() {
    airshipPushSetup.setupPush(with: self)
  }

  private func watch() {
    airshipPushFeature.configuration.sink { [weak self] configuration in
      self?.userPushNotificationsEnabled(configuration.isEnabled)
    }.store(
      in: &cancellables
    )

    tenantService.selectedTenant.sink { [weak self] tenant in
      guard let self, let tenant else {
        return
      }
      let regionCode: String? = {
        switch tenant.identifier.value {
        case "de-CH":
          "dech"
        case "fr-CH":
          "frch"
        default:
          tenant.locale.regionCode?.lowercased()
        }
      }()

      guard let regionCode else {
        return
      }
      airshipChannel.set(
        attributes: ["shopCountryCode": regionCode]
      )
    }.store(
      in: &cancellables
    )
  }

  private func updateProvisional(for authorizationStatus: UAAuthorizationStatus) {
    switch authorizationStatus {
    case .provisional:
      airshipChannel.set(
        attributes: [
          "provisional_authorization_ts": Date(),
          "provisional_authorization": "true"
        ]
      )
    default:
      airshipChannel.remove(
        keys: [
          "provisional_authorization_ts",
          "provisional_authorization"
        ]
      )
    }
  }
}

// MARK: DeepLinkDelegate

extension OGAirshipPushService: DeepLinkDelegate {
  @MainActor
  public func receivedDeepLink(_ deepLink: URL) async {
    logger.log(.debug, domain: .service, message: "receivedDeepLink \(deepLink)")
    await UIApplication.shared.open(deepLink)
  }
}

// MARK: RegistrationDelegate

extension OGAirshipPushService: RegistrationDelegate {
  public func notificationAuthorizedSettingsDidChange(_: UAAuthorizedNotificationSettings) {
    updateProvisional(for: authorizationStatus())
  }

  public func apnsRegistrationSucceeded(withDeviceToken deviceToken: Data) {
    logger.log(.debug, domain: .service, message: "Device token: \(String(data: deviceToken, encoding: .utf8) ?? "NAN")")
  }

  public func apnsRegistrationFailedWithError(_ error: Error) {
    logger.log(.critical, domain: .service, message: "Airship push registration failed with error: \(error.localizedDescription)")
  }

  public func notificationRegistrationFinished(
    withAuthorizedSettings _: UAAuthorizedNotificationSettings,
    categories _: Set<UNNotificationCategory>,
    status: UAAuthorizationStatus
  ) {
    updateProvisional(for: status)
  }
}

// MARK: PushNotificationDelegate

extension OGAirshipPushService: PushNotificationDelegate {
  public func receivedBackgroundNotification(_: [AnyHashable: Any], completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    completionHandler(.noData)
  }

  public func receivedForegroundNotification(_: [AnyHashable: Any], completionHandler: @escaping () -> Void) {
    completionHandler()
  }

  public func receivedNotificationResponse(_: UNNotificationResponse, completionHandler: @escaping () -> Void) {
    completionHandler()
  }

  public func extend(_: UNNotificationPresentationOptions, notification _: UNNotification) -> UNNotificationPresentationOptions {
    [.list, .banner, .sound]
  }
}
