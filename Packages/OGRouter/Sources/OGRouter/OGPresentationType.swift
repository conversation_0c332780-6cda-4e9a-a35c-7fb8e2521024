import Foundation
import SwiftUI

// MARK: - OGPresentationType

/// Represents the presentation type for a route in OGNavigation.
public enum OGPresentationType: Equatable, Hashable {
  public static func == (lhs: OGPresentationType, rhs: OGPresentationType) -> Bool {
    switch (lhs, rhs) {
    case let (.sheet(lhsValue), .sheet(rhsValue)):
      return lhsValue == rhsValue
    case (.push, .push):
      return true
    case (.pop, .pop):
      return true
    case (.dismiss, .dismiss):
      return true
    case (.fullScreenCover, .fullScreenCover):
      return true
    case (.fullScreenCoverNoStack, .fullScreenCoverNoStack):
      return true
    case (.replace, .replace):
      return true
    case (.none, .none):
      return true
    case (.overlay, .overlay):
      return true
    case (.banner, banner):
      return true
    default:
      return false
    }
  }

  /// Push: Presents the route by pushing it onto the navigation stack.
  case push

  /// Pop: Presents the route by popping it from the navigation stack.
  case pop

  /// Dismiss: Presents the route by dismissing it.
  case dismiss

  /// Sheet: Presents the route as a modal sheet.
  case sheet(Set<OGPresentationDetent> = Set([]))

  /// Full Screen Cover: Presents the route as a full-screen cover.
  case fullScreenCover

  /// Full Screen Cover No Stack: Presents the route as a full-screen cover without a navigation stack.
  case fullScreenCoverNoStack

  /// Replace: Replaces the current route with the new route.
  case replace

  /// None: No specific presentation type specified.
  case none

  /// Overlay: Presents the route as an overlay.
  case overlay

  /// Banner: Presents the route as a banner.
  case banner
}

// MARK: - OGPresentationDetent

public enum OGPresentationDetent {
  case medium
  case large

  @available(iOS 16.0, *)
  public func toPresentationDetent() -> PresentationDetent {
    switch self {
    case .medium:
      return .medium
    case .large:
      return .large
    }
  }
}
