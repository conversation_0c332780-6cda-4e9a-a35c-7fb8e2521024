{"formatVersion": 1, "database": {"version": 1, "identityHash": "e47f46267c64eca768920d6f1686b656", "entities": [{"tableName": "LogEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`logId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `timestamp` INTEGER NOT NULL, `message` TEXT NOT NULL)", "fields": [{"fieldPath": "logId", "columnName": "logId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["logId"]}, "indices": [{"name": "index_LogEntity_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogEntity_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}]}, {"tableName": "EventEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`eventId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `properties` TEXT NOT NULL)", "fields": [{"fieldPath": "eventId", "columnName": "eventId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "properties", "columnName": "properties", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["eventId"]}, "indices": [{"name": "index_EventEntity_name", "unique": false, "columnNames": ["name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_EventEntity_name` ON `${TABLE_NAME}` (`name`)"}]}, {"tableName": "LogToEventMappingEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`logIdRef` INTEGER NOT NULL, `eventIdRef` INTEGER NOT NULL, PRIMARY KEY(`logIdRef`, `eventIdRef`), <PERSON>OREI<PERSON><PERSON> KEY(`eventIdRef`) REFERENCES `EventEntity`(`eventId`) ON UPDATE CASCADE ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`logIdRef`) REFERENCES `LogEntity`(`logId`) ON UPDATE CASCADE ON DELETE CASCADE )", "fields": [{"fieldPath": "logIdRef", "columnName": "logIdRef", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "eventIdRef", "columnName": "eventIdRef", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["logIdRef", "eventIdRef"]}, "indices": [{"name": "index_LogToEventMappingEntity_logIdRef", "unique": false, "columnNames": ["logIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToEventMappingEntity_logIdRef` ON `${TABLE_NAME}` (`logIdRef`)"}, {"name": "index_LogToEventMappingEntity_eventIdRef", "unique": false, "columnNames": ["eventIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToEventMappingEntity_eventIdRef` ON `${TABLE_NAME}` (`eventIdRef`)"}], "foreignKeys": [{"table": "EventEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["eventIdRef"], "referencedColumns": ["eventId"]}, {"table": "LogEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["logIdRef"], "referencedColumns": ["logId"]}]}, {"tableName": "EventToTrackingServiceMappingEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`eventIdRef` INTEGER NOT NULL, `serviceIdRef` INTEGER NOT NULL, PRIMARY KEY(`eventIdRef`, `serviceIdRef`), <PERSON>OREI<PERSON><PERSON> KEY(`eventIdRef`) REFERENCES `EventEntity`(`eventId`) ON UPDATE CASCADE ON DELETE CASCADE , FOREIGN KEY(`serviceIdRef`) REFERENCES `TrackingServiceEntity`(`serviceId`) ON UPDATE CASCADE ON DELETE CASCADE )", "fields": [{"fieldPath": "eventIdRef", "columnName": "eventIdRef", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serviceIdRef", "columnName": "serviceIdRef", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["eventIdRef", "serviceIdRef"]}, "indices": [{"name": "index_EventToTrackingServiceMappingEntity_eventIdRef", "unique": false, "columnNames": ["eventIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_EventToTrackingServiceMappingEntity_eventIdRef` ON `${TABLE_NAME}` (`eventIdRef`)"}, {"name": "index_EventToTrackingServiceMappingEntity_serviceIdRef", "unique": false, "columnNames": ["serviceIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_EventToTrackingServiceMappingEntity_serviceIdRef` ON `${TABLE_NAME}` (`serviceIdRef`)"}], "foreignKeys": [{"table": "EventEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["eventIdRef"], "referencedColumns": ["eventId"]}, {"table": "TrackingServiceEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["serviceIdRef"], "referencedColumns": ["serviceId"]}]}, {"tableName": "ServiceEventEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serviceEventId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `properties` TEXT NOT NULL)", "fields": [{"fieldPath": "serviceEventId", "columnName": "serviceEventId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "properties", "columnName": "properties", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["serviceEventId"]}, "indices": [{"name": "index_ServiceEventEntity_name", "unique": false, "columnNames": ["name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_ServiceEventEntity_name` ON `${TABLE_NAME}` (`name`)"}]}, {"tableName": "TrackingServiceEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serviceId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `serviceName` TEXT NOT NULL, `isEnabled` INTEGER NOT NULL, `contextParameter` TEXT NOT NULL)", "fields": [{"fieldPath": "serviceId", "columnName": "serviceId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serviceName", "columnName": "serviceName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isEnabled", "columnName": "isEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "contextParameter", "columnName": "contextParameter", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["serviceId"]}, "indices": [{"name": "index_TrackingServiceEntity_serviceName", "unique": false, "columnNames": ["serviceName"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_TrackingServiceEntity_serviceName` ON `${TABLE_NAME}` (`serviceName`)"}]}, {"tableName": "TrackingServiceToServiceEventMappingEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serviceIdRef` INTEGER NOT NULL, `serviceEventIdRef` INTEGER NOT NULL, PRIMARY KEY(`serviceIdRef`, `serviceEventIdRef`), <PERSON>OR<PERSON><PERSON><PERSON> KEY(`serviceIdRef`) REFERENCES `TrackingServiceEntity`(`serviceId`) ON UPDATE CASCADE ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`serviceEventIdRef`) REFERENCES `ServiceEventEntity`(`serviceEventId`) ON UPDATE CASCADE ON DELETE CASCADE )", "fields": [{"fieldPath": "serviceIdRef", "columnName": "serviceIdRef", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serviceEventIdRef", "columnName": "serviceEventIdRef", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["serviceIdRef", "serviceEventIdRef"]}, "indices": [{"name": "index_TrackingServiceToServiceEventMappingEntity_serviceIdRef", "unique": false, "columnNames": ["serviceIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_TrackingServiceToServiceEventMappingEntity_serviceIdRef` ON `${TABLE_NAME}` (`serviceIdRef`)"}, {"name": "index_TrackingServiceToServiceEventMappingEntity_serviceEventIdRef", "unique": false, "columnNames": ["serviceEventIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_TrackingServiceToServiceEventMappingEntity_serviceEventIdRef` ON `${TABLE_NAME}` (`serviceEventIdRef`)"}], "foreignKeys": [{"table": "TrackingServiceEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["serviceIdRef"], "referencedColumns": ["serviceId"]}, {"table": "ServiceEventEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["serviceEventIdRef"], "referencedColumns": ["serviceEventId"]}]}, {"tableName": "NetDataEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`netId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `method` TEXT NOT NULL, `url` TEXT NOT NULL, `httpStatus` INTEGER NOT NULL, `responseTime` INTEGER NOT NULL, `requestHeader` TEXT NOT NULL, `requestBody` TEXT NOT NULL, `responseHeader` TEXT NOT NULL, `responseBody` TEXT NOT NULL, `responseSize` INTEGER NOT NULL)", "fields": [{"fieldPath": "netId", "columnName": "netId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "method", "columnName": "method", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "httpStatus", "columnName": "httpStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "responseTime", "columnName": "responseTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestHeader", "columnName": "requestHeader", "affinity": "TEXT", "notNull": true}, {"fieldPath": "requestBody", "columnName": "requestBody", "affinity": "TEXT", "notNull": true}, {"fieldPath": "responseHeader", "columnName": "responseHeader", "affinity": "TEXT", "notNull": true}, {"fieldPath": "responseBody", "columnName": "responseBody", "affinity": "TEXT", "notNull": true}, {"fieldPath": "responseSize", "columnName": "responseSize", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["netId"]}}, {"tableName": "LogToNetMappingEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`logIdRef` INTEGER NOT NULL, `netIdRef` INTEGER NOT NULL, PRIMARY KEY(`logIdRef`, `netIdRef`), <PERSON>OR<PERSON><PERSON><PERSON> KEY(`netIdRef`) REFERENCES `NetDataEntity`(`netId`) ON UPDATE CASCADE ON DELETE CASCADE , FOREI<PERSON>N KEY(`logIdRef`) REFERENCES `LogEntity`(`logId`) ON UPDATE CASCADE ON DELETE CASCADE )", "fields": [{"fieldPath": "logIdRef", "columnName": "logIdRef", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "netIdRef", "columnName": "netIdRef", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["logIdRef", "netIdRef"]}, "indices": [{"name": "index_LogToNetMappingEntity_logIdRef", "unique": false, "columnNames": ["logIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToNetMappingEntity_logIdRef` ON `${TABLE_NAME}` (`logIdRef`)"}, {"name": "index_LogToNetMappingEntity_netIdRef", "unique": false, "columnNames": ["netIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToNetMappingEntity_netIdRef` ON `${TABLE_NAME}` (`netIdRef`)"}], "foreignKeys": [{"table": "NetDataEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["netIdRef"], "referencedColumns": ["netId"]}, {"table": "LogEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["logIdRef"], "referencedColumns": ["logId"]}]}, {"tableName": "SysDataEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sysId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `priority` INTEGER NOT NULL, `tag` TEXT, `stacktrace` TEXT, `fileName` TEXT, `methodName` TEXT, `lineNumber` INTEGER)", "fields": [{"fieldPath": "sysId", "columnName": "sysId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "priority", "columnName": "priority", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT"}, {"fieldPath": "stacktrace", "columnName": "stacktrace", "affinity": "TEXT"}, {"fieldPath": "fileName", "columnName": "fileName", "affinity": "TEXT"}, {"fieldPath": "methodName", "columnName": "methodName", "affinity": "TEXT"}, {"fieldPath": "lineNumber", "columnName": "lineNumber", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": true, "columnNames": ["sysId"]}}, {"tableName": "LogToSysMappingEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`logIdRef` INTEGER NOT NULL, `sysIdRef` INTEGER NOT NULL, PRIMARY KEY(`logIdRef`, `sysIdRef`), <PERSON>OR<PERSON><PERSON><PERSON> KEY(`sysIdRef`) REFERENCES `SysDataEntity`(`sysId`) ON UPDATE CASCADE ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`logIdRef`) REFERENCES `LogEntity`(`logId`) ON UPDATE CASCADE ON DELETE CASCADE )", "fields": [{"fieldPath": "logIdRef", "columnName": "logIdRef", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sysIdRef", "columnName": "sysIdRef", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["logIdRef", "sysIdRef"]}, "indices": [{"name": "index_LogToSysMappingEntity_logIdRef", "unique": false, "columnNames": ["logIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToSysMappingEntity_logIdRef` ON `${TABLE_NAME}` (`logIdRef`)"}, {"name": "index_LogToSysMappingEntity_sysIdRef", "unique": false, "columnNames": ["sysIdRef"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LogToSysMappingEntity_sysIdRef` ON `${TABLE_NAME}` (`sysIdRef`)"}], "foreignKeys": [{"table": "SysDataEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["sysIdRef"], "referencedColumns": ["sysId"]}, {"table": "LogEntity", "onDelete": "CASCADE", "onUpdate": "CASCADE", "columns": ["logIdRef"], "referencedColumns": ["logId"]}]}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e47f46267c64eca768920d6f1686b656')"]}}