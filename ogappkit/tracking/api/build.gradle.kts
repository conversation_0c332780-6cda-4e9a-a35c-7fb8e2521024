plugins {
    id("ogAppKit.libraryModule")
    alias(libs.plugins.room)
    alias(libs.plugins.ksp)
}

kotlin {
    sourceSets {
        androidMain.dependencies {
            implementation(libs.firebase.analytics)
            implementation(libs.adjust.android)
            implementation(libs.snowplow.android.tracker)
            implementation(libs.androidx.startup.runtime)
        }
        iosMain.dependencies {
        }
        commonMain.dependencies {
            implementation(libs.square.okio)
            implementation(libs.androidx.datastore.core)
            implementation(libs.androidx.datastore.core.okio)
            implementation(libs.kotlinx.datetime)
            implementation(libs.kotlinx.serialization)
            implementation(libs.kotlinx.serialization.properties)
            implementation(libs.androidx.room.runtime)
            implementation(libs.sqlite.bundled)
            implementation(libs.androidx.room.common)
            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.client.logging)
            implementation(libs.ktor.serialization.kotlin.json)
            implementation(libs.stately.concurrentCollections)
        }
        commonTest.dependencies {
            implementation(libs.ktor.client.mock)
        }
    }
}

dependencies {
    add("kspAndroid", libs.androidx.room.compiler)
    add("kspIosSimulatorArm64", libs.androidx.room.compiler)
    add("kspIosX64", libs.androidx.room.compiler)
    add("kspIosArm64", libs.androidx.room.compiler)

    // Add our screen mapper processor for all targets
    add("kspCommonMainMetadata", projects.ogappkit.tracking.processor)
    add("kspAndroid", projects.ogappkit.tracking.processor)
    add("kspIosSimulatorArm64", projects.ogappkit.tracking.processor)
    add("kspIosX64", projects.ogappkit.tracking.processor)
    add("kspIosArm64", projects.ogappkit.tracking.processor)
}

room {
    schemaDirectory("$projectDir/schemas")
}
