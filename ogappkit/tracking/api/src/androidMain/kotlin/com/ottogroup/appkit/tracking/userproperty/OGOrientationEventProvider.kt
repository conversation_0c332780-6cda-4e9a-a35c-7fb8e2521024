package com.ottogroup.appkit.tracking.userproperty

import android.content.Context
import android.view.OrientationEventListener
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

public interface OGOrientationEventProvider {
    public val orientation: Flow<Int>
    public fun onOrientationChanged(orientation: Int)
    public fun canDetectOrientation(): Boolean
    public fun enable()
    public fun disable()
}

public class OGOrientationEventProviderImpl(context: Context) :
    OrientationEventListener(context), OGOrientationEventProvider {
    private val _orientation: MutableStateFlow<Int> =
        MutableStateFlow(context.resources.configuration.orientation)
    override val orientation: Flow<Int> = _orientation.asStateFlow()

    override fun onOrientationChanged(orientation: Int) {
        _orientation.value = orientation
    }

    override fun canDetectOrientation(): Boolean {
        return super.canDetectOrientation()
    }

    override fun enable() {
        if (canDetectOrientation()) {
            super.enable()
        }
    }

    override fun disable() {
        super.disable()
    }
}
