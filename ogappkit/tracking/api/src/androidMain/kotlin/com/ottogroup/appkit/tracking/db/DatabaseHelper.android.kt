package com.ottogroup.appkit.tracking.db

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase
import com.ottogroup.appkit.base.file.FileSystem

internal actual class DatabaseHelper(
    internal val context: Context,
    internal val fileSystem: FileSystem
)

internal actual inline fun <reified T : RoomDatabase> DatabaseHelper.createDatabaseBuilder(fileName: String): RoomDatabase.Builder<T> {
    return Room.databaseBuilder(context, fileSystem.getDatabasePath(fileName).toString())
}

internal actual inline fun <reified T : RoomDatabase> DatabaseHelper.createInMemoryDatabaseBuilder(): RoomDatabase.Builder<T> {
    return Room.inMemoryDatabaseBuilder(context)
}
