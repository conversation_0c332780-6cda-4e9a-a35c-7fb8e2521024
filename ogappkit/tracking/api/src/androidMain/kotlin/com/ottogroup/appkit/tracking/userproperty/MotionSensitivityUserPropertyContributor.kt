package com.ottogroup.appkit.tracking.userproperty

import android.provider.Settings
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map

internal class MotionSensitivityUserPropertyContributor(
    lifeCycleProvider: ApplicationLifecycleProvider,
    animationScaleProvider: OGAnimationScaleProvider,
) : UserPropertyContributor {

    override val userProperty: Flow<UserProperty> =
        lifeCycleProvider.lifecycle
            .filter { it == LifecycleEvent.AppInForeground }
            .map { UserProperty.MotionSensitivityUserProperty(isActive(animationScaleProvider)) }

    /**
     * Checks if the motion sensitivity is active by verifying if the windows animations are
     * disabled which is the case when the scale is set to 0.0f.
     */
    private fun isActive(animationScaleProvider: OGAnimationScaleProvider): Boolean {
        return try {
            animationScaleProvider.getAnimationScale() == 0.0f
        } catch (e: Settings.SettingNotFoundException) {
            false
        }
    }
}
