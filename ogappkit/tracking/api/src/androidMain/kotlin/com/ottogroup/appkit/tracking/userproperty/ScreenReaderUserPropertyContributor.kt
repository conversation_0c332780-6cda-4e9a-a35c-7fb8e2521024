package com.ottogroup.appkit.tracking.userproperty

import android.view.accessibility.AccessibilityManager
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

internal class ScreenReaderUserPropertyContributor(
    lifeCycleProvider: ApplicationLifecycleProvider,
    coroutineScope: CoroutineScope,
    private val accessibilityManager: OGAccessibilityManager
) :
    UserPropertyContributor {

    private val _userProperty: MutableStateFlow<UserProperty> =
        MutableStateFlow(UserProperty.ScreenReaderUserProperty(accessibilityManager.isEnabled))
    override val userProperty: StateFlow<UserProperty> = _userProperty.asStateFlow()

    private val accessibilityStateChangeListener =
        AccessibilityManager.AccessibilityStateChangeListener { enabled ->
            _userProperty.value = UserProperty.ScreenReaderUserProperty(enabled)
        }

    init {
        lifeCycleProvider.lifecycle
            .distinctUntilChanged()
            .map { event ->
                when (event) {
                    LifecycleEvent.AppInForeground -> start()
                    LifecycleEvent.AppInBackground -> stop()
                }
            }.stateIn(coroutineScope, SharingStarted.Eagerly, LifecycleEvent.AppInBackground)
    }

    private fun stop() {
        accessibilityManager.removeAccessibilityStateChangeListener(accessibilityStateChangeListener)
    }

    private fun start() {
        accessibilityManager.addAccessibilityStateChangeListener(accessibilityStateChangeListener)
    }
}
