package com.ottogroup.appkit.tracking.userproperty

import android.content.Context
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map

internal class FontSizeZoomUserPropertyContributor(
    context: Context,
    lifeCycleProvider: ApplicationLifecycleProvider,
) : UserPropertyContributor {

    override val userProperty: Flow<UserProperty> =
        lifeCycleProvider.lifecycle
            .filter { it == LifecycleEvent.AppInForeground }
            .map { UserProperty.FontSizeZoomUserProperty(context.resources.configuration.fontScale) }
}
