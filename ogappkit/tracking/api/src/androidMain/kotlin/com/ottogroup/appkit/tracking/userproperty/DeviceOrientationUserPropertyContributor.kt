package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn

internal class DeviceOrientationUserPropertyContributor(
    lifeCycleProvider: ApplicationLifecycleProvider,
    orientationEventProvider: OGOrientationEventProvider,
    coroutineScope: CoroutineScope
) : UserPropertyContributor {

    override val userProperty: StateFlow<UserProperty> = combine(
        lifeCycleProvider.lifecycle,
        orientationEventProvider.orientation
    ) { lifecycleEvent, orientation ->
        Pair(lifecycleEvent, orientation)
    }.onEach { (lifecycleEvent, _) ->
        when (lifecycleEvent) {
            LifecycleEvent.AppInForeground -> orientationEventProvider.enable()
            LifecycleEvent.AppInBackground -> orientationEventProvider.disable()
        }
    }.filter { (lifecycleEvent, _) -> lifecycleEvent == LifecycleEvent.AppInForeground }
        .map { (_, orientation) -> orientationToUserProperty(orientation) }
        .stateIn(
            coroutineScope,
            SharingStarted.Eagerly,
            UserProperty.DeviceOrientationUserProperty.Portrait
        )

    /**
     * mapping from device rotation and degree values
     * to Portrait or Landscape orientation.
     *
     * Visualization of orientation ranges (360° circle):
     *
     *           0°/360° (Portrait)
     *                 ↑
     *                 |
     *                 |
     *   270° ←────────┼────────→ 90°
     *  (Landscape)    |    (Landscape)
     *                 |
     *                 ↓
     *              180° (Portrait)
     *
     * Degree range mapping:
     * - Portrait:   0°-45°, 315°-360°, 135°-224°
     * - Landscape:  46°-134°, 225°-314°
     *
     * The ranges are chosen to prevent frequent orientation changes
     * from small hand movements.
     */
    private fun orientationToUserProperty(orientation: Int): UserProperty {
        val orientationResult = when (orientation) {
            in 0..45, in 135..224, in 315..360 -> {
                Orientation.PORTRAIT
            }

            in 46..134, in 225..314 -> {
                Orientation.LANDSCAPE
            }
            // defaults to portrait
            else -> {
                Orientation.PORTRAIT
            }
        }
        return orientationResult.toUserProperty()
    }

    private enum class Orientation {
        PORTRAIT,
        LANDSCAPE;

        fun toUserProperty(): UserProperty {
            return when (this) {
                PORTRAIT -> UserProperty.DeviceOrientationUserProperty.Portrait
                LANDSCAPE -> UserProperty.DeviceOrientationUserProperty.Landscape
            }
        }
    }
}
