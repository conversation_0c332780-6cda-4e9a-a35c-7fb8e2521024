package com.ottogroup.appkit.tracking.userproperty

import android.view.accessibility.AccessibilityManager

public interface OGAccessibilityManager {
    public val isEnabled: Boolean
    public fun addAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener)
    public fun removeAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener)
}

public class OGAccessibilityManagerImpl(
    private val accessibilityManager: AccessibilityManager
) : OGAccessibilityManager {
    override val isEnabled: <PERSON>olean
        get() = accessibilityManager.isEnabled

    override fun addAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener) {
        accessibilityManager.addAccessibilityStateChangeListener(listener)
    }

    override fun removeAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener) {
        accessibilityManager.removeAccessibilityStateChangeListener(listener)
    }
}
