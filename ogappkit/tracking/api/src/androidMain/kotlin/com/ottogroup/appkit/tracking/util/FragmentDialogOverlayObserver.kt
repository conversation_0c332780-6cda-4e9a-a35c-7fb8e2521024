package com.ottogroup.appkit.tracking.util

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.ottogroup.appkit.tracking.overlays.OverlayObserver
import com.ottogroup.appkit.tracking.overlays.OverlayHandler

internal class FragmentDialogOverlayObserver(
    private val application: Application,
    overlayHandler: OverlayHandler,
) : OverlayObserver {
    private val fragmentListener = object : FragmentManager.FragmentLifecycleCallbacks() {

        override fun onFragmentStarted(fm: FragmentManager, f: Fragment) {
            super.onFragmentStarted(fm, f)
            if (f is DialogFragment) {
                overlayHandler.onShowOverlay(f.toString())
            }
        }

        override fun onFragmentStopped(fm: FragmentManager, f: Fragment) {
            super.onFragmentStopped(fm, f)
            if (f is DialogFragment) {
                overlayHandler.onDismissOverlay(f.toString())
            }
        }
    }

    private val activityLifecycleCallbacks = object :
        Application.ActivityLifecycleCallbacks {

        override fun onActivityStarted(activity: Activity) {
            if (activity is FragmentActivity) {
                try {
                    activity.supportFragmentManager.registerFragmentLifecycleCallbacks(
                        fragmentListener,
                        true
                    )
                } catch (_: Exception) {
                    // ignore
                }
            }
        }

        override fun onActivityStopped(activity: Activity) {
            if (activity is FragmentActivity) {
                try {
                    activity.supportFragmentManager.unregisterFragmentLifecycleCallbacks(
                        fragmentListener
                    )
                } catch (_: Exception) {
                    // ignore
                }
            }
        }

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) = Unit
        override fun onActivityResumed(activity: Activity) = Unit
        override fun onActivityPaused(activity: Activity) = Unit
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) = Unit
        override fun onActivityDestroyed(activity: Activity) = Unit
    }
    private var isObserving = false
    override fun startObserving() {
        if (isObserving) return
        isObserving = true
        application.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }

    override fun stopObserving() {
        application.unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks)
        isObserving = false
    }
}
