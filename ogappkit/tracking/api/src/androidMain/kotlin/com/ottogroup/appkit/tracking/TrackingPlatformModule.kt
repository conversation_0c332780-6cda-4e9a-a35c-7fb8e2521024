package com.ottogroup.appkit.tracking

import android.content.Context.ACCESSIBILITY_SERVICE
import android.view.accessibility.AccessibilityManager
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.tracking.db.DatabaseHelper
import com.ottogroup.appkit.tracking.overlays.OverlayObserver
import com.ottogroup.appkit.tracking.userproperty.DeviceOrientationUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.FontSizeZoomUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.MotionSensitivityUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.OGAccessibilityManager
import com.ottogroup.appkit.tracking.userproperty.OGAccessibilityManagerImpl
import com.ottogroup.appkit.tracking.userproperty.OGAnimationScaleProvider
import com.ottogroup.appkit.tracking.userproperty.OGAnimationScaleProviderImpl
import com.ottogroup.appkit.tracking.userproperty.OGOrientationEventProvider
import com.ottogroup.appkit.tracking.userproperty.OGOrientationEventProviderImpl
import com.ottogroup.appkit.tracking.userproperty.ScreenReaderUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.UserPropertyContributor
import com.ottogroup.appkit.tracking.util.FragmentDialogOverlayObserver
import org.koin.android.ext.koin.androidApplication
import org.koin.android.ext.koin.androidContext
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.module

public actual fun trackingPlatformModule(): Module = module {
    single<OverlayObserver> {
        FragmentDialogOverlayObserver(
            androidApplication(),
            get(),
        )
    }
    single<DatabaseHelper> { DatabaseHelper(androidApplication(), get()) }

    single {
        OGOrientationEventProviderImpl(
            androidContext()
        )
    } bind OGOrientationEventProvider::class

    single {
        DeviceOrientationUserPropertyContributor(
            get(),
            get(),
            getCoroutineScope(),
        )
    } bind UserPropertyContributor::class
    single {
        FontSizeZoomUserPropertyContributor(
            androidContext(),
            get(),
        )
    } bind UserPropertyContributor::class

    single {
        OGAccessibilityManagerImpl(
            androidContext().getSystemService(ACCESSIBILITY_SERVICE) as AccessibilityManager
        )
    } bind OGAccessibilityManager::class

    single {
        ScreenReaderUserPropertyContributor(
            get(),
            getCoroutineScope(),
            get()
        )
    } bind UserPropertyContributor::class

    single {
        OGAnimationScaleProviderImpl(
            androidContext()
        )
    } bind OGAnimationScaleProvider::class

    single {
        MotionSensitivityUserPropertyContributor(
            get(),
            get()
        )
    } bind UserPropertyContributor::class
}
