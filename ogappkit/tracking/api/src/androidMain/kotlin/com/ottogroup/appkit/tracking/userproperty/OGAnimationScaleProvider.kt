package com.ottogroup.appkit.tracking.userproperty

import android.content.Context
import android.provider.Settings

public interface OGAnimationScaleProvider {
    public fun getAnimationScale(): Float
}

public class OGAnimationScaleProviderImpl(
    private val context: Context
) : OGAnimationScaleProvider {

    override fun getAnimationScale(): Float {
        return try {
            Settings.Global.getFloat(
                context.contentResolver,
                Settings.Global.TRANSITION_ANIMATION_SCALE
            )
        } catch (e: Settings.SettingNotFoundException) {
            1.0f // Default scale if not found
        }
    }
}
