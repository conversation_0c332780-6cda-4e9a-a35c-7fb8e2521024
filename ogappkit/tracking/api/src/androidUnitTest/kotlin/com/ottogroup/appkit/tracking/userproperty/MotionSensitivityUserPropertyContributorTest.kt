package com.ottogroup.appkit.tracking.userproperty

import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

internal class MotionSensitivityUserPropertyContributorTest {

    private lateinit var mockLifecycleProvider: MockLifecycleProvider

    @Before
    fun setup() {
        mockLifecycleProvider = MockLifecycleProvider()
    }

    @Test
    fun `should emit true for motion sensitivity status when scale is 0`() = runTest {
        // Given
        val scaleProviderMock = createScaleProviderMock(0.0f)

        val contributor =
            MotionSensitivityUserPropertyContributor(mockLifecycleProvider, scaleProviderMock)

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.MotionSensitivityUserProperty(true), awaitItem())
        }
    }

    @Test
    fun `should emit false for motion sensitivity status when scale is 1`() = runTest {
        // Given
        val scaleProviderMock = createScaleProviderMock(1.0f)

        val contributor =
            MotionSensitivityUserPropertyContributor(mockLifecycleProvider, scaleProviderMock)

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.MotionSensitivityUserProperty(false), awaitItem())
        }
    }

    @Test
    fun `should not emit when app is in background`() = runTest {
        // Given
        val scaleProviderMock = createScaleProviderMock(1.0f)

        val contributor =
            MotionSensitivityUserPropertyContributor(mockLifecycleProvider, scaleProviderMock)

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInBackground)
            expectNoEvents()
        }
    }

    @Test
    fun `should emit updated status when setting changes and app returns to foreground`() =
        runTest {
            // Given
            val scaleProviderMock = createScaleProviderMock(1.0f)

            val contributor =
                MotionSensitivityUserPropertyContributor(mockLifecycleProvider, scaleProviderMock)

            // When/Then
            contributor.userProperty.test {
                mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
                assertEquals(UserProperty.MotionSensitivityUserProperty(false), awaitItem())

                // Simulate background
                mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInBackground)

                // Change setting while in background
                scaleProviderMock.setAnimationScale(0.0f)

                // Return to foreground
                mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)

                // Then - should emit updated value
                assertEquals(UserProperty.MotionSensitivityUserProperty(true), awaitItem())
            }
        }

    private fun createScaleProviderMock(initialScale: Float): OGAnimationScaleProviderMock {
        return OGAnimationScaleProviderMock(initialScale)
    }
}

private class OGAnimationScaleProviderMock(initialScale: Float) : OGAnimationScaleProvider {

    private var scale: Float = initialScale
    override fun getAnimationScale(): Float = scale

    fun setAnimationScale(newScale: Float) {
        scale = newScale
    }
}
