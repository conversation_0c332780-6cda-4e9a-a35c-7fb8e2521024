package com.ottogroup.appkit.tracking.userproperty

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender
import android.content.ServiceConnection
import android.content.res.Configuration
import android.content.res.Resources
import android.database.DatabaseErrorHandler
import android.database.sqlite.SQLiteDatabase
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.UserHandle
import android.view.Display
import java.io.File

internal class MockContext(
    fontScale: Float,
    private val accessibilityManager: OGAccessibilityManager
) : Context() {
    private val mockResources = MockResources(fontScale)

    fun updateFontScale(newFontScale: Float) {
        mockResources.updateFontScale(newFontScale)
    }

    override fun getSystemService(name: String): Any {
        if (name == ACCESSIBILITY_SERVICE) {
            return accessibilityManager
        }
        throw UnsupportedOperationException()
    }

    override fun getContentResolver() = throw UnsupportedOperationException()
    override fun getResources(): Resources = mockResources
    override fun getAssets() = throw UnsupportedOperationException()
    override fun getTheme() = throw UnsupportedOperationException()
    override fun getSystemServiceName(serviceClass: Class<*>) =
        throw UnsupportedOperationException()

    override fun checkPermission(permission: String, pid: Int, uid: Int) =
        throw UnsupportedOperationException()

    override fun getPackageManager() = throw UnsupportedOperationException()
    override fun getMainLooper() = throw UnsupportedOperationException()
    override fun getApplicationContext() = throw UnsupportedOperationException()
    override fun setTheme(resid: Int) = Unit
    override fun getClassLoader() = throw UnsupportedOperationException()
    override fun getPackageName() = throw UnsupportedOperationException()
    override fun getApplicationInfo() = throw UnsupportedOperationException()
    override fun getSharedPreferences(name: String, mode: Int) =
        throw UnsupportedOperationException()

    override fun getPackageResourcePath() = throw UnsupportedOperationException()
    override fun getPackageCodePath() = throw UnsupportedOperationException()
    override fun moveSharedPreferencesFrom(sourceContext: Context, name: String) =
        throw UnsupportedOperationException()

    override fun deleteSharedPreferences(name: String) = throw UnsupportedOperationException()
    override fun openFileInput(name: String) = throw UnsupportedOperationException()
    override fun openFileOutput(name: String, mode: Int) = throw UnsupportedOperationException()
    override fun deleteFile(name: String) = throw UnsupportedOperationException()
    override fun getFileStreamPath(name: String) = throw UnsupportedOperationException()
    override fun getDataDir(): File = throw UnsupportedOperationException()
    override fun fileList() = throw UnsupportedOperationException()
    override fun getDir(name: String, mode: Int) = throw UnsupportedOperationException()
    override fun openOrCreateDatabase(
        name: String?,
        mode: Int,
        factory: SQLiteDatabase.CursorFactory?
    ): SQLiteDatabase = throw UnsupportedOperationException()

    override fun openOrCreateDatabase(
        name: String?,
        mode: Int,
        factory: SQLiteDatabase.CursorFactory?,
        errorHandler: DatabaseErrorHandler?
    ): SQLiteDatabase = throw UnsupportedOperationException()

    override fun moveDatabaseFrom(sourceContext: Context?, name: String?): Boolean {
        return false
    }

    override fun deleteDatabase(name: String?): Boolean {
        return false
    }

    override fun getDatabasePath(name: String) = throw UnsupportedOperationException()
    override fun databaseList() = throw UnsupportedOperationException()
    override fun getWallpaper() = throw UnsupportedOperationException()
    override fun peekWallpaper() = throw UnsupportedOperationException()
    override fun getWallpaperDesiredMinimumWidth() = throw UnsupportedOperationException()
    override fun getWallpaperDesiredMinimumHeight() = throw UnsupportedOperationException()
    override fun setWallpaper(bitmap: Bitmap) =
        throw UnsupportedOperationException()

    override fun setWallpaper(data: java.io.InputStream) = throw UnsupportedOperationException()
    override fun clearWallpaper() = throw UnsupportedOperationException()
    override fun startActivity(intent: Intent) = throw UnsupportedOperationException()
    override fun startActivity(intent: Intent, options: Bundle?) =
        throw UnsupportedOperationException()

    override fun startActivities(intents: Array<out Intent>) = throw UnsupportedOperationException()
    override fun startActivities(intents: Array<out Intent>, options: Bundle?) =
        throw UnsupportedOperationException()

    override fun startIntentSender(
        intent: IntentSender,
        fillInIntent: Intent?,
        flagsMask: Int,
        flagsValues: Int,
        extraFlags: Int
    ) = throw UnsupportedOperationException()

    override fun startIntentSender(
        intent: IntentSender,
        fillInIntent: Intent?,
        flagsMask: Int,
        flagsValues: Int,
        extraFlags: Int,
        options: Bundle?
    ) = throw UnsupportedOperationException()

    override fun sendBroadcast(intent: Intent) = throw UnsupportedOperationException()
    override fun sendBroadcast(intent: Intent, receiverPermission: String?) =
        throw UnsupportedOperationException()

    override fun sendOrderedBroadcast(intent: Intent, receiverPermission: String?) =
        throw UnsupportedOperationException()

    override fun sendOrderedBroadcast(
        intent: Intent,
        receiverPermission: String?,
        resultReceiver: BroadcastReceiver?,
        scheduler: Handler?,
        initialCode: Int,
        initialData: String?,
        initialExtras: Bundle?
    ) = throw UnsupportedOperationException()

    override fun sendBroadcastAsUser(intent: Intent, user: UserHandle) =
        throw UnsupportedOperationException()

    override fun sendBroadcastAsUser(
        intent: Intent,
        user: UserHandle,
        receiverPermission: String?
    ) = throw UnsupportedOperationException()

    override fun sendOrderedBroadcastAsUser(
        intent: Intent,
        user: UserHandle,
        receiverPermission: String?,
        resultReceiver: BroadcastReceiver?,
        scheduler: Handler?,
        initialCode: Int,
        initialData: String?,
        initialExtras: Bundle?
    ) = throw UnsupportedOperationException()

    override fun sendStickyBroadcast(intent: Intent) = throw UnsupportedOperationException()
    override fun sendStickyOrderedBroadcast(
        intent: Intent,
        resultReceiver: BroadcastReceiver?,
        scheduler: Handler?,
        initialCode: Int,
        initialData: String?,
        initialExtras: Bundle?
    ) = throw UnsupportedOperationException()

    override fun removeStickyBroadcast(intent: Intent) = throw UnsupportedOperationException()
    override fun sendStickyBroadcastAsUser(intent: Intent, user: UserHandle) =
        throw UnsupportedOperationException()

    override fun sendStickyOrderedBroadcastAsUser(
        intent: Intent,
        user: UserHandle,
        resultReceiver: BroadcastReceiver?,
        scheduler: Handler?,
        initialCode: Int,
        initialData: String?,
        initialExtras: Bundle?
    ) = throw UnsupportedOperationException()

    override fun removeStickyBroadcastAsUser(intent: Intent, user: UserHandle) =
        throw UnsupportedOperationException()

    override fun registerReceiver(receiver: BroadcastReceiver?, filter: IntentFilter?): Intent? {
        throw UnsupportedOperationException()
    }

    override fun registerReceiver(
        receiver: BroadcastReceiver?,
        filter: IntentFilter,
        flags: Int
    ) = throw UnsupportedOperationException()

    override fun registerReceiver(
        receiver: BroadcastReceiver?,
        filter: IntentFilter,
        receiverPermission: String?,
        scheduler: Handler?
    ) = throw UnsupportedOperationException()

    override fun registerReceiver(
        receiver: BroadcastReceiver?,
        filter: IntentFilter,
        receiverPermission: String?,
        scheduler: Handler?,
        flags: Int
    ) = throw UnsupportedOperationException()

    override fun unregisterReceiver(receiver: BroadcastReceiver) =
        throw UnsupportedOperationException()

    override fun startInstrumentation(
        className: ComponentName,
        profileFile: String?,
        arguments: Bundle?
    ): Boolean {
        return false
    }

    override fun startService(service: Intent) = throw UnsupportedOperationException()
    override fun startForegroundService(service: Intent?): ComponentName? {
        return null
    }

    override fun stopService(service: Intent) = throw UnsupportedOperationException()
    override fun bindService(service: Intent, conn: ServiceConnection, flags: Int): Boolean {
        return false
    }

    override fun unbindService(conn: ServiceConnection) = Unit

    override fun getOpPackageName() = throw UnsupportedOperationException()
    override fun getCacheDir() = throw UnsupportedOperationException()
    override fun getCodeCacheDir() = throw UnsupportedOperationException()
    override fun getExternalCacheDir() = throw UnsupportedOperationException()
    override fun getExternalCacheDirs() = throw UnsupportedOperationException()
    override fun getExternalFilesDir(type: String?) = throw UnsupportedOperationException()
    override fun getExternalFilesDirs(type: String?) = throw UnsupportedOperationException()
    override fun getExternalMediaDirs() = throw UnsupportedOperationException()
    override fun getObbDir() = throw UnsupportedOperationException()
    override fun getObbDirs() = throw UnsupportedOperationException()
    override fun getFilesDir() = throw UnsupportedOperationException()
    override fun getNoBackupFilesDir() = throw UnsupportedOperationException()
    override fun checkCallingOrSelfPermission(permission: String) =
        throw UnsupportedOperationException()

    override fun checkCallingOrSelfUriPermission(uri: Uri, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun checkCallingPermission(permission: String) = throw UnsupportedOperationException()
    override fun checkCallingUriPermission(uri: Uri, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun checkSelfPermission(permission: String) = throw UnsupportedOperationException()
    override fun checkUriPermission(uri: Uri, pid: Int, uid: Int, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun checkUriPermission(
        uri: Uri?,
        readPermission: String?,
        writePermission: String?,
        pid: Int,
        uid: Int,
        modeFlags: Int
    ) = throw UnsupportedOperationException()

    override fun enforceCallingOrSelfPermission(permission: String, message: String?) =
        throw UnsupportedOperationException()

    override fun enforceCallingOrSelfUriPermission(
        uri: Uri,
        modeFlags: Int,
        message: String?
    ) = throw UnsupportedOperationException()

    override fun enforceCallingPermission(permission: String, message: String?) =
        throw UnsupportedOperationException()

    override fun enforceCallingUriPermission(
        uri: Uri,
        modeFlags: Int,
        message: String?
    ) = throw UnsupportedOperationException()

    override fun enforcePermission(permission: String, pid: Int, uid: Int, message: String?) =
        throw UnsupportedOperationException()

    override fun enforceUriPermission(
        uri: Uri,
        pid: Int,
        uid: Int,
        modeFlags: Int,
        message: String?
    ) = throw UnsupportedOperationException()

    override fun enforceUriPermission(
        uri: Uri?,
        readPermission: String?,
        writePermission: String?,
        pid: Int,
        uid: Int,
        modeFlags: Int,
        message: String?
    ) = throw UnsupportedOperationException()

    override fun grantUriPermission(toPackage: String, uri: Uri, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun revokeUriPermission(uri: Uri, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun revokeUriPermission(toPackage: String, uri: Uri, modeFlags: Int) =
        throw UnsupportedOperationException()

    override fun createConfigurationContext(overrideConfiguration: Configuration) =
        throw UnsupportedOperationException()

    override fun createContextForSplit(splitName: String) = throw UnsupportedOperationException()
    override fun createDeviceProtectedStorageContext() = throw UnsupportedOperationException()
    override fun createDisplayContext(display: Display) =
        throw UnsupportedOperationException()

    override fun createPackageContext(packageName: String, flags: Int) =
        throw UnsupportedOperationException()

    override fun isDeviceProtectedStorage() = throw UnsupportedOperationException()
    override fun isRestricted() = throw UnsupportedOperationException()
}

internal class MockResources(fontScale: Float) : Resources(null, null, null) {
    private val mockConfiguration = Configuration().apply {
        this.fontScale = fontScale
        this.orientation = Configuration.ORIENTATION_PORTRAIT
    }

    fun updateFontScale(newFontScale: Float) {
        mockConfiguration.fontScale = newFontScale
    }

    fun updateOrientation(newOrientation: Int) {
        mockConfiguration.orientation = newOrientation
    }

    override fun getConfiguration(): Configuration = mockConfiguration
}
