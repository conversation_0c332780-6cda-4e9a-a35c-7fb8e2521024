package com.ottogroup.appkit.tracking.userproperty

import android.view.accessibility.AccessibilityManager
import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse

internal class ScreenReaderUserPropertyContributorTest {

    private lateinit var mockLifecycleProvider: MockLifecycleProvider

    @Before
    fun setup() {
        mockLifecycleProvider = MockLifecycleProvider()
    }

    @Test
    fun `should emit screen reader status when app is in foreground`() = runTest {
        // Given
        val accessibilityManager = createAccessibilityManager(true)
        val contributor = ScreenReaderUserPropertyContributor(
            mockLifecycleProvider,
            backgroundScope,
            accessibilityManager
        )
        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.ScreenReaderUserProperty(true), awaitItem())
        }
    }

    @Test
    fun `should not emit when app goes to background`() = runTest {
        // Given
        val accessibilityManager = createAccessibilityManager(false)
        val contributor = ScreenReaderUserPropertyContributor(
            mockLifecycleProvider,
            backgroundScope,
            accessibilityManager
        )

        // When/Then
        contributor.userProperty.test {
            assertFalse(awaitItem().value.toBoolean())
            accessibilityManager.setEnabled(true)
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            expectNoEvents()
        }
    }
}

internal fun createAccessibilityManager(initialEnabled: Boolean): OGAccessibilityManagerMock {
    return OGAccessibilityManagerMock(initialEnabled)
}

internal class OGAccessibilityManagerMock(initialEnabled: Boolean) : OGAccessibilityManager {
    private var _isEnabled = initialEnabled
    override val isEnabled: Boolean = _isEnabled

    override fun addAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener) =
        Unit

    override fun removeAccessibilityStateChangeListener(listener: AccessibilityManager.AccessibilityStateChangeListener) =
        Unit

    fun setEnabled(enabled: Boolean) {
        _isEnabled = enabled
    }
}
