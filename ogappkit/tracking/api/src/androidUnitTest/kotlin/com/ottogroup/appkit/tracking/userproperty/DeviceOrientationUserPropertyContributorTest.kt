package com.ottogroup.appkit.tracking.userproperty

import android.content.res.Configuration
import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

internal class DeviceOrientationUserPropertyContributorTest {

    private lateinit var mockLifecycleProvider: MockLifecycleProvider

    @Before
    fun setup() {
        mockLifecycleProvider = MockLifecycleProvider()
    }

    @Test
    fun `should emit portrait orientation when app is started`() = runTest {
        // Given
        val contributor = DeviceOrientationUserPropertyContributor(
            mockLifecycleProvider,
            createOrientationEventProviderMock(true),
            backgroundScope
        )

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.DeviceOrientationUserProperty.Portrait, awaitItem())
        }
    }

    @Test
    fun `should emit landscape orientation when orientation changes`() = runTest {
        // Given
        val orientationMock =
            createOrientationEventProviderMock(true, Configuration.ORIENTATION_PORTRAIT)
        val contributor = DeviceOrientationUserPropertyContributor(
            mockLifecycleProvider,
            orientationMock,
            backgroundScope
        )

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.DeviceOrientationUserProperty.Portrait, awaitItem())

            // Simulate orientation change
            orientationMock.onOrientationChanged(90)
            // Then
            assertEquals(UserProperty.DeviceOrientationUserProperty.Landscape, awaitItem())
        }
    }

    @Test
    fun `should not react to orientation changes when app is in background`() = runTest {
        // Given
        val orientationMock =
            createOrientationEventProviderMock(true, Configuration.ORIENTATION_PORTRAIT)
        val contributor = DeviceOrientationUserPropertyContributor(
            mockLifecycleProvider,
            orientationMock,
            backgroundScope
        )

        // When/Then
        contributor.userProperty.test {
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInBackground)

            // Simulate orientation change - should be ignored as app is in background
            orientationMock.onOrientationChanged(90)

            // Then - go to foreground and check the value is still portrait
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            assertEquals(UserProperty.DeviceOrientationUserProperty.Portrait, awaitItem())
        }
    }

    private fun createOrientationEventProviderMock(
        initialEnabled: Boolean,
        initialOrientation: Int = Configuration.ORIENTATION_PORTRAIT
    ): OrientationEventProviderMock {
        return OrientationEventProviderMock(initialEnabled, initialOrientation)
    }
}

private class OrientationEventProviderMock(
    initialEnabled: Boolean,
    initialOrientation: Int = Configuration.ORIENTATION_PORTRAIT
) : OGOrientationEventProvider {
    private var _orientation: MutableStateFlow<Int> = MutableStateFlow(initialOrientation)
    override val orientation: Flow<Int> = _orientation.asStateFlow()
    var isEnabled: Boolean = initialEnabled
    override fun onOrientationChanged(orientation: Int) {
        if (isEnabled) {
            _orientation.value = orientation
        }
    }

    override fun canDetectOrientation(): Boolean = true

    override fun enable() {
        if (canDetectOrientation()) {
            isEnabled = true
        }
    }

    override fun disable() {
        isEnabled = false
    }
}
