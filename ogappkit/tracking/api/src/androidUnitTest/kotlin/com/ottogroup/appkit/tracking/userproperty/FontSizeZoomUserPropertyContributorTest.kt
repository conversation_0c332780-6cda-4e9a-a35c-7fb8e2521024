package com.ottogroup.appkit.tracking.userproperty

import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

internal class FontSizeZoomUserPropertyContributorTest {

    private lateinit var mockLifecycleProvider: MockLifecycleProvider

    @Before
    fun setup() {
        mockLifecycleProvider = MockLifecycleProvider()
    }

    @Test
    fun `should emit font scale user property when app is in foreground`() = runTest {
        // Given
        val expectedFontScale = 1.5f
        val mockContext = MockContext(expectedFontScale, createAccessibilityManager(false))
        val contributor = FontSizeZoomUserPropertyContributor(mockContext, mockLifecycleProvider)
        contributor.userProperty.test {
            // When
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            // Then
            assertEquals(UserProperty.FontSizeZoomUserProperty(expectedFontScale), awaitItem())
        }
    }

    // Fertig
    @Test
    fun `should not emit font scale user property when app is in background`() = runTest {
        // Given
        val expectedFontScale = 1f
        val mockContext = MockContext(expectedFontScale, createAccessibilityManager(false))
        val contributor = FontSizeZoomUserPropertyContributor(mockContext, mockLifecycleProvider)
        contributor.userProperty.test {
            // When
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInBackground)
            // Then
            expectNoEvents()
        }
    }

    @Test
    fun `should emit updated font scale on configuration change`() = runTest {
        // Given
        val initialFontScale = 1.0f
        val mockContext = MockContext(initialFontScale, createAccessibilityManager(false))
        val contributor = FontSizeZoomUserPropertyContributor(mockContext, mockLifecycleProvider)
        contributor.userProperty.test {
            // When
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInBackground)
            // Then
            expectNoEvents()
            // when - font size change occurs
            val updatedFontScale = 2.0f
            mockContext.updateFontScale(updatedFontScale)
            mockLifecycleProvider.setLifecycleEvent(LifecycleEvent.AppInForeground)
            // then
            assertEquals(UserProperty.FontSizeZoomUserProperty(updatedFontScale), awaitItem())
        }
    }
}

internal class MockLifecycleProvider : ApplicationLifecycleProvider {
    private val _lifecycle: MutableStateFlow<LifecycleEvent> =
        MutableStateFlow(LifecycleEvent.AppInBackground)
    override val lifecycle: Flow<LifecycleEvent> = _lifecycle.asStateFlow()

    fun setLifecycleEvent(event: LifecycleEvent) {
        _lifecycle.value = event
    }
}
