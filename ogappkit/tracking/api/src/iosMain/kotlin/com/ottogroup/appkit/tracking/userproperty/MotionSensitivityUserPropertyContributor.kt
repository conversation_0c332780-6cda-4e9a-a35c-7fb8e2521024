package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import com.ottogroup.appkit.base.withInitialValue
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import platform.UIKit.UIAccessibilityIsReduceMotionEnabled

internal class MotionSensitivityUserPropertyContributor(applicationLifecycleProvider: ApplicationLifecycleProvider) :
    UserPropertyContributor {

    override val userProperty: Flow<UserProperty> =
        applicationLifecycleProvider.lifecycle
            .filterIsInstance<LifecycleEvent.AppInForeground>()
            .map { UserProperty.MotionSensitivityUserProperty(isActive()) }
            .withInitialValue(UserProperty.MotionSensitivityUserProperty(isActive()))
    private fun isActive(): Boolean {
        return UIAccessibilityIsReduceMotionEnabled()
    }
}
