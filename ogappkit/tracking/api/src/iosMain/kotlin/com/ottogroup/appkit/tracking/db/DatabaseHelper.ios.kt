package com.ottogroup.appkit.tracking.db

import androidx.room.Room
import androidx.room.RoomDatabase
import com.ottogroup.appkit.base.file.FileSystem

internal actual class DatabaseHelper(internal val fileSystem: FileSystem)

internal actual inline fun <reified T : RoomDatabase> DatabaseHelper.createDatabaseBuilder(fileName: String): RoomDatabase.Builder<T> {
    return Room.databaseBuilder(fileSystem.getDatabasePath(fileName).toString())
}

internal actual inline fun <reified T : RoomDatabase> DatabaseHelper.createInMemoryDatabaseBuilder(): RoomDatabase.Builder<T> {
    return Room.inMemoryDatabaseBuilder()
}
