package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import platform.Foundation.NSNotificationCenter
import platform.UIKit.UIAccessibilityIsVoiceOverRunning
import platform.UIKit.UIAccessibilityVoiceOverStatusDidChangeNotification

internal class ScreenReaderUserPropertyContributor(
    coroutineScope: CoroutineScope,
    lifeCycleContributor: ApplicationLifecycleProvider
) : UserPropertyContributor {

    private val _userProperty: MutableStateFlow<UserProperty> =
        MutableStateFlow(UserProperty.ScreenReaderUserProperty(UIAccessibilityIsVoiceOverRunning()))
    override val userProperty: StateFlow<UserProperty> = _userProperty.asStateFlow()

    private var observer: Any? = null

    init {
        lifeCycleContributor.lifecycle
            .distinctUntilChanged()
            .onEach { event ->
                when (event) {
                    LifecycleEvent.AppInForeground -> start()
                    LifecycleEvent.AppInBackground -> stop()
                }
            }
            .launchIn(coroutineScope)
    }

    private fun start() {
        observer = NSNotificationCenter.defaultCenter.addObserverForName(
            name = UIAccessibilityVoiceOverStatusDidChangeNotification,
            `object` = null,
            queue = null
        ) { _ ->
            _userProperty.value = UserProperty.ScreenReaderUserProperty(UIAccessibilityIsVoiceOverRunning())
        }
        _userProperty.value = UserProperty.ScreenReaderUserProperty(UIAccessibilityIsVoiceOverRunning())
    }

    private fun stop() {
        observer?.let { obs ->
            NSNotificationCenter.defaultCenter.removeObserver(obs)
            observer = null
        }
    }
}
