package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import platform.Foundation.NSNotificationCenter
import platform.UIKit.UIDevice
import platform.UIKit.UIDeviceOrientation
import platform.UIKit.UIDeviceOrientationDidChangeNotification

internal class DeviceOrientationUserPropertyContributor(
    lifeCycleContributor: ApplicationLifecycleProvider,
    coroutineScope: CoroutineScope
) : UserPropertyContributor {

    private val _userProperty: MutableStateFlow<UserProperty> =
        MutableStateFlow(getCurrentOrientation())
    override val userProperty: StateFlow<UserProperty> = _userProperty.asStateFlow()

    private var observer: Any? = null

    init {
        lifeCycleContributor.lifecycle
            .distinctUntilChanged()
            .onEach { event ->
                when (event) {
                    LifecycleEvent.AppInForeground -> start()
                    LifecycleEvent.AppInBackground -> stop()
                }
            }
            .launchIn(coroutineScope)
    }

    private fun start() {
        observer = NSNotificationCenter.defaultCenter.addObserverForName(
            name = UIDeviceOrientationDidChangeNotification,
            `object` = null,
            queue = null
        ) { _ ->
            _userProperty.value = getCurrentOrientation()
        }
        _userProperty.value = getCurrentOrientation()
    }

    private fun stop() {
        observer?.let { obs ->
            NSNotificationCenter.defaultCenter.removeObserver(obs)
            observer = null
        }
    }

    private fun getCurrentOrientation(): UserProperty {
        return when (UIDevice.currentDevice.orientation) {
            UIDeviceOrientation.UIDeviceOrientationPortrait,
            UIDeviceOrientation.UIDeviceOrientationPortraitUpsideDown ->
                UserProperty.DeviceOrientationUserProperty.Portrait
            UIDeviceOrientation.UIDeviceOrientationLandscapeLeft,
            UIDeviceOrientation.UIDeviceOrientationLandscapeRight ->
                UserProperty.DeviceOrientationUserProperty.Landscape
            else -> UserProperty.DeviceOrientationUserProperty.Portrait
        }
    }
}
