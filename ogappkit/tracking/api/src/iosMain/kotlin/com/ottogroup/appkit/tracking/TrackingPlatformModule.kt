package com.ottogroup.appkit.tracking

import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.tracking.db.DatabaseHelper
import com.ottogroup.appkit.tracking.userproperty.DeviceOrientationUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.FontSizeZoomUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.MotionSensitivityUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.ScreenReaderUserPropertyContributor
import com.ottogroup.appkit.tracking.userproperty.UserPropertyContributor
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.module

public actual fun trackingPlatformModule(): Module = module {
    single<DatabaseHelper> { DatabaseHelper(get()) }
    single {
        DeviceOrientationUserPropertyContributor(
            get(),
            getCoroutineScope(),
        )
    } bind UserPropertyContributor::class
    single {
        FontSizeZoomUserPropertyContributor(get())
    } bind UserPropertyContributor::class
    single {
        ScreenReaderUserPropertyContributor(
            getCoroutineScope(),
            get(),
        )
    } bind UserPropertyContributor::class
    single {
        MotionSensitivityUserPropertyContributor(get())
    } bind UserPropertyContributor::class
}
