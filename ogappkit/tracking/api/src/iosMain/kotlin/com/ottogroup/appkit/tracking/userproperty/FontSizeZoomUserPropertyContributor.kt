package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import com.ottogroup.appkit.base.withInitialValue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import platform.UIKit.UIApplication
import platform.UIKit.UIContentSizeCategoryExtraSmall
import platform.UIKit.UIContentSizeCategorySmall
import platform.UIKit.UIContentSizeCategoryMedium
import platform.UIKit.UIContentSizeCategoryLarge
import platform.UIKit.UIContentSizeCategoryExtraLarge
import platform.UIKit.UIContentSizeCategoryExtraExtraLarge
import platform.UIKit.UIContentSizeCategoryExtraExtraExtraLarge
import platform.UIKit.UIContentSizeCategoryAccessibilityMedium
import platform.UIKit.UIContentSizeCategoryAccessibilityLarge
import platform.UIKit.UIContentSizeCategoryAccessibilityExtraLarge
import platform.UIKit.UIContentSizeCategoryAccessibilityExtraExtraLarge
import platform.UIKit.UIContentSizeCategoryAccessibilityExtraExtraExtraLarge

internal class FontSizeZoomUserPropertyContributor(applicationLifecycleProvider: ApplicationLifecycleProvider) :
    UserPropertyContributor {

    override val userProperty: Flow<UserProperty> =
        applicationLifecycleProvider.lifecycle
            .filterIsInstance<LifecycleEvent.AppInForeground>()
            .map { UserProperty.FontSizeZoomUserProperty(getCurrentFontSizeZoom()) }
            .flowOn(Dispatchers.Main)
            .withInitialValue(UserProperty.FontSizeZoomUserProperty(getCurrentFontSizeZoom()))
    private fun getCurrentFontSizeZoom(): Float {
        val category = UIApplication.sharedApplication.preferredContentSizeCategory
        return when (category) {
            UIContentSizeCategoryExtraSmall -> 0.82f
            UIContentSizeCategorySmall -> 0.88f
            UIContentSizeCategoryMedium -> 0.95f
            UIContentSizeCategoryLarge -> 1.0f
            UIContentSizeCategoryExtraLarge -> 1.12f
            UIContentSizeCategoryExtraExtraLarge -> 1.23f
            UIContentSizeCategoryExtraExtraExtraLarge -> 1.35f
            UIContentSizeCategoryAccessibilityMedium -> 1.64f
            UIContentSizeCategoryAccessibilityLarge -> 1.95f
            UIContentSizeCategoryAccessibilityExtraLarge -> 2.35f
            UIContentSizeCategoryAccessibilityExtraExtraLarge -> 2.76f
            UIContentSizeCategoryAccessibilityExtraExtraExtraLarge -> 3.12f
            else -> 1.0f
        }
    }
}
