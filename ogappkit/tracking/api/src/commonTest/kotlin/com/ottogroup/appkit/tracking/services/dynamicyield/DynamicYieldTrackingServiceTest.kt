@file:OptIn(ExperimentalCoroutinesApi::class)

package com.ottogroup.appkit.tracking.services.dynamicyield

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.test.mockDataStore
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentImpl
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentLocalDataSource
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentState
import com.ottogroup.appkit.tracking.event.CustomParameter
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.services.dynamicyield.api.InternalDynamicYieldNetworkDataSource
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest

class DynamicYieldTrackingServiceTest {

    private val consentStore = OGTrackingConsentLocalDataSource(
        mockDataStore(OGTrackingConsentState(globalConsent = true))
    )
    private val dataSource = TestDataSource()

    @Test
    fun `native PDP view event is passed to DY API`() = runTest {
        val configFlow = MutableStateFlow(
            DynamicYieldConfig(
                trackPageViewUrl = "https://api.dynamicyield.com/track",
                apiKey = "secret",
                isEnabled = true,
                itemIdAlternativeParameter = "dy_sku"
            )
        )

        val consent = OGTrackingConsentImpl(backgroundScope, consentStore)
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            consent,
            backgroundScope
        )
        advanceTimeBy(1000)

        val dyTrackingService = DynamicYieldTrackingService(
            trackingDealer,
            configFlow,
            this.backgroundScope,
            dataSource,
            isInitiallyEnabled = true,
        )

        trackingDealer.trackEvent(
            View.ProductDetailViewItem(
                ECommerceItem(
                    name = "",
                    id = "id",
                    additionalParameters = mapOf("dy_sku" to CustomParameter("sku"))
                )
            )
        )
        advanceTimeBy(200)

        assertEquals(
            listOf(
                DynamicYieldEvent.PageView(
                    type = DynamicYieldEvent.PageView.Type.PRODUCT,
                    data = listOf("sku"),
                    location = "ProductDetail/sku"
                )
            ),
            dataSource.trackedPageViews
        )
    }
}

private class TestDataSource : InternalDynamicYieldNetworkDataSource {
    val trackedPageViews = mutableListOf<DynamicYieldEvent.PageView>()

    override suspend fun trackPageView(event: DynamicYieldEvent.PageView): Result<Unit> {
        return resultFor {
            trackedPageViews += event
        }
    }

    override suspend fun chooseVariations(
        event: DynamicYieldEvent.PageView,
        selectorNames: List<String>,
        selectorGroups: List<String>
    ): Result<List<Choice>> {
        return resultFor { emptyList() }
    }
}
