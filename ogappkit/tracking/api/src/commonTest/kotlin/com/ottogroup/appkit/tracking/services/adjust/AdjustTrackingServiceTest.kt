package com.ottogroup.appkit.tracking.services.adjust

import com.ottogroup.appkit.test.LocalDataStore
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentImpl
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentLocalDataSource
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentState
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.GenericEvent
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.event.View.Screen
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

@OptIn(ExperimentalCoroutinesApi::class)
class AdjustTrackingServiceTest {
    private val consentStore = OGTrackingConsentLocalDataSource(
        LocalDataStore(MutableStateFlow(OGTrackingConsentState()))
    )

    private val configFlow = MutableStateFlow(AdjustConfig(true))
    private lateinit var consent: OGTrackingConsent
    private lateinit var trackingDealer: OGTrackingDealer
    private val adjustAnalytics = AnalyticsMock()

    private fun setup(coroutineScope: CoroutineScope) {
        consent = OGTrackingConsentImpl(coroutineScope, consentStore)
        consent.setConsentsForServices(mapOf(OGTrackingServiceId.Adjust to true))
        trackingDealer = OGTrackingDealer(
            flowOf(true),
            emptyList(),
            consent,
            coroutineScope,
        )
    }

    private fun createAdjustTrackingService(
        coroutineScope: CoroutineScope,
        ogTrackingDealer: OGTrackingDealer = trackingDealer,
        confFlow: Flow<AdjustConfig> = configFlow,
        adjstAnalytics: AdjustAnalytics = adjustAnalytics,
    ) = AdjustTrackingService(
        trackingDealer,
        configFlow,
        coroutineScope,
        adjustAnalytics,
    )

    private val eventTokenMapping = mapOf(
        "screen.home" to "screen_home_token",
        "ProductDetailViewItem" to "view_item_token",
        "interactionpurch" to "purchase_token",
        // ambiguous mapping is invalid:
        "Screen.Login" to "login_token",
        "screenlogin" to "login_token2",
    )

    @Test
    fun `user consent will enable and disable third party sharing but not service`() = runTest {
        val adjustTrackingService = createAdjustTrackingService(backgroundScope)
        advanceTime()
        assertTrue(adjustTrackingService.isEnabled)
        assertTrue(adjustAnalytics.serviceEnabled)
        assertTrue(adjustAnalytics.thirdPartyEnabled)

        consent.setConsentsForServices(mapOf(OGTrackingServiceId.Adjust to false))
        advanceTime()
        assertTrue(adjustTrackingService.isEnabled)
        assertTrue(adjustAnalytics.serviceEnabled)
        assertFalse(adjustAnalytics.thirdPartyEnabled)
    }

    @Test
    fun `user consent will be ignored when config is disabled`() = runTest {
        configFlow.value = AdjustConfig(false)
        val adjustTrackingService = createAdjustTrackingService(backgroundScope)
        advanceTime()
        assertEquals(false, adjustTrackingService.isEnabled)
    }

    @Test
    fun `enabled state is being delegated to analytics service`() = runTest {
        createAdjustTrackingService(backgroundScope)
        advanceTime()

        assertEquals(true, adjustAnalytics.serviceEnabled)
    }

    @Test
    fun `tracking event is delegated to tracking service when enabled`() = runTest {
        configFlow.value = AdjustConfig(
            isEnabled = true,
            eventTokenMapping = eventTokenMapping,
        )
        createAdjustTrackingService(backgroundScope)
        advanceTime()

        trackingDealer.trackEvent(Screen.Home)
        advanceTime()
        assertEquals(
            AdjustEvent(
                token = "screen_home_token",
                partnerParameters = mapOf(
                    "name" to "Home",
                    "pageType" to "Homepage",
                )
            ),
            adjustAnalytics.loggedEvent
        )
    }

    @Test
    fun `tracking event properties are set in partner parameters`() = runTest {
        configFlow.value = AdjustConfig(
            isEnabled = true,
            eventTokenMapping = eventTokenMapping,
        )
        createAdjustTrackingService(backgroundScope)
        advanceTime()

        val item = ECommerceItem("name", "id")
        trackingDealer.trackEvent(View.ProductDetailViewItem(item))
        advanceTime()
        assertEquals(
            AdjustEvent(
                token = "view_item_token",
                partnerParameters = mapOf(
                    "name" to "ProductDetailViewItem",
                    "item" to item.toString(),
                    "item.name" to "name",
                    "item.id" to "id",
                )
            ),
            adjustAnalytics.loggedEvent
        )
    }

    @Test
    fun `tracking event is ignored when disabled`() = runTest {
        configFlow.value = AdjustConfig(
            isEnabled = false,
            eventTokenMapping = eventTokenMapping,
        )
        createAdjustTrackingService(backgroundScope)
        advanceTime()
        trackingDealer.trackEvent(Screen.Home)
        advanceTime()
        assertNull(adjustAnalytics.loggedEvent)
    }

    @Test
    fun `tracking event is ignored without token mapping`() = runTest {
        createAdjustTrackingService(backgroundScope)
        advanceTime()
        trackingDealer.trackEvent(Screen.Home)
        advanceTime()
        assertNull(adjustAnalytics.loggedEvent)
    }

    @Test
    fun `tracking event is ignored with ambiguous token mapping`() = runTest {
        createAdjustTrackingService(backgroundScope)
        advanceTime()
        trackingDealer.trackEvent(Screen.Login)
        advanceTime()
        assertNull(adjustAnalytics.loggedEvent)
    }

    @Test
    fun `global consent parameter are cleared when empty`() = runTest {
        val config = AdjustConfig(
            true,
            eventTokenMapping,
            listOf(
                JsonObject(
                    mapOf(
                        "data" to JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("value"),
                                "key2" to JsonPrimitive("value2")
                            )
                        )
                    )
                )
            )
        )
        configFlow.value = config
        val adjustTrackingService = createAdjustTrackingService(backgroundScope)

        val newConfig = AdjustConfig(true, eventTokenMapping, listOf(JsonObject(emptyMap())))
        configFlow.value = newConfig
        advanceTime()
        assertEquals(
            emptyMap(),
            adjustAnalytics.mockParams
        )
    }

    @Test
    fun `global consent parameter are removed for empty values`() = runTest {
        val config = AdjustConfig(
            true,
            eventTokenMapping,
            listOf(
                JsonObject(
                    mapOf(
                        "data" to JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("value"),
                                "key2" to JsonPrimitive("value2")
                            )
                        )
                    )
                )
            )
        )
        configFlow.value = config
        val adjustTrackingService = createAdjustTrackingService(backgroundScope)

        val newConfig = AdjustConfig(
            true,
            eventTokenMapping,
            listOf(
                JsonObject(
                    mapOf(
                        "data" to JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("value"),
                                "key2" to JsonNull
                            )
                        )
                    )
                )
            )
        )
        configFlow.value = newConfig
        advanceTime()
        assertEquals(
            mapOf("key" to "value"),
            adjustAnalytics.mockParams
        )
    }

    @Test
    fun `global consent parameters are updated when config updates`() = runTest {
        val config = AdjustConfig(
            true,
            eventTokenMapping,
            listOf(
                JsonObject(
                    mapOf(
                        "data" to JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("value")
                            )
                        )
                    )
                )
            )
        )
        configFlow.value = config
        createAdjustTrackingService(backgroundScope)

        val newConfig = AdjustConfig(
            true,
            eventTokenMapping,
            listOf(
                JsonObject(
                    mapOf(
                        "data" to JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("value2")
                            )
                        )
                    )
                )
            )
        )
        configFlow.value = newConfig
        advanceTime()
        assertEquals(
            mapOf("key" to "value2"),
            adjustAnalytics.mockParams
        )
    }

    @Test
    fun `user property are not emitted to service when config is disabled`() =
        testUserPropertyEmission()

    @Test
    fun `user property are not emitted to service when consent and config are disabled`() =
        testUserPropertyEmission(hasConsent = false)

    @Test
    fun `cached user property should be emitted after config switch`() =
        testUserPropertyEmission(configChangeToTrue = true)

    @Test
    fun `generic event should be tracked when found in config`() = runTest {
        consent.setConsentsForServices(mapOf(OGTrackingServiceId.Adjust to true))
        val token = "generic_event_token"
        val eventTokenMapping = mapOf(
            "GenericEventTestEvent" to token
        )
        configFlow.value = AdjustConfig(isEnabled = true, eventTokenMapping)
        createAdjustTrackingService(backgroundScope, confFlow = configFlow)
        advanceTime()
        trackingDealer.trackEvent(
            GenericEvent(
                name = "test_event",
                params = mapOf("key123" to "value123")
            )
        )
        advanceTime()
        assertEquals(
            token,
            adjustAnalytics.loggedEvent?.token
        )
    }

    @Test
    fun `generic event should not be tracked when not found in config`() = runTest {
        consent.setConsentsForServices(mapOf(OGTrackingServiceId.Adjust to true))
        val token = "generic_event_token"
        val eventTokenMapping = mapOf(
            "GenericEventTestEvent" to token
        )
        configFlow.value = AdjustConfig(isEnabled = true, eventTokenMapping)
        createAdjustTrackingService(backgroundScope, confFlow = configFlow)
        advanceTime()
        trackingDealer.trackEvent(
            GenericEvent(
                name = "app_start",
                params = mapOf("key123" to "value123")
            )
        )
        advanceTime()
        assertNull(adjustAnalytics.loggedEvent?.token)
    }

    private fun runTest(block: suspend TestScope.() -> Unit) {
        kotlinx.coroutines.test.runTest {
            setup(backgroundScope)
            block()
        }
    }

    private fun testUserPropertyEmission(
        configEnabled: Boolean = false,
        hasConsent: Boolean = true,
        configChangeToTrue: Boolean = false,
    ) = runTest {
        val key = "push_notification_opt_in"
        consent.setConsentsForServices(mapOf(OGTrackingServiceId.Adjust to hasConsent))
        if (!configEnabled) {
            configFlow.value = AdjustConfig(isEnabled = false)
            advanceTime()
        }
        createAdjustTrackingService(backgroundScope, confFlow = configFlow)
        trackingDealer.setUserProperty(UserProperty.PushNotificationOptInUserProperty(true))
        advanceTime()
        if (!configEnabled || !hasConsent) {
            assertFalse(adjustAnalytics.userProperties.containsKey(key))
        }
        if (configChangeToTrue) {
            configFlow.value = AdjustConfig(isEnabled = true)
            advanceTime()
            assertTrue(adjustAnalytics.userProperties.containsKey(key))
        }
    }

    private fun TestScope.advanceTime() = advanceTimeBy(1000)
}

private class AnalyticsMock : AdjustAnalytics {
    var serviceEnabled = false
    var thirdPartyEnabled = false
    var loggedEvent: AdjustEvent? = null
    val mockParams: MutableMap<String, String> = mutableMapOf()
    val userProperties = mutableMapOf<String, String?>()
    override fun logEvent(event: AdjustEvent) {
        loggedEvent = event
    }

    override fun setEnabled(enabled: Boolean) {
        serviceEnabled = enabled
    }

    override fun setThirdPartySharingEnabled(enabled: Boolean) {
        thirdPartyEnabled = enabled
    }

    override fun addGlobalPartnerParameter(key: String, value: String) {
        mockParams[key] = value
    }

    override fun removeGlobalPartnerParameter(key: String) {
        mockParams.remove(key)
    }

    override fun clearAllGlobalPartnerParameters() {
        mockParams.clear()
    }

    override fun setUserProperty(key: String, value: String?) {
        userProperties[key] = value
    }
}
