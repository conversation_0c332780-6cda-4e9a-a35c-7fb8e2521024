package com.ottogroup.appkit.tracking.config

import app.cash.turbine.test
import com.ottogroup.appkit.tracking.LogLevel
import com.ottogroup.appkit.tracking.OGTrackingConfig
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceConfig
import com.ottogroup.appkit.tracking.services.adjust.AdjustConfig
import com.ottogroup.appkit.tracking.services.firebase.FirebaseConfig
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class OGConfigDispatcherTest {

    @Test
    fun `updateConfig should update base config`() = runTest {
        val initialConfig = OGTrackingConfig(
            trackingEnabled = false,
            logLevel = LogLevel.Debug,
            viewEventMapping = emptyMap()
        )
        val updatedConfig = OGTrackingConfig(
            trackingEnabled = true,
            logLevel = LogLevel.Info,
            viewEventMapping = emptyMap()
        )
        val dispatcher = OGConfigDispatcher(initialConfig)

        dispatcher.updateConfig(updatedConfig)

        val currentConfig = dispatcher.configState.value.baseConfig
        assertEquals(updatedConfig, currentConfig)
    }

    @Test
    fun `updateConfig should update service config`() = runTest {
        val initialConfig = OGTrackingConfig(
            trackingEnabled = false,
            logLevel = LogLevel.Debug,
            viewEventMapping = emptyMap()
        )
        val dispatcher = OGConfigDispatcher(initialConfig)
        val serviceId = OGTrackingServiceId.Adjust
        val serviceConfig: ServiceConfig = object : ServiceConfig() {
            override val isEnabled: Boolean
                get() = true

            override fun baseCopy(
                isEnabled: Boolean,
                globalContext: List<JsonObject>
            ): ServiceConfig {
                return this
            }
        }

        dispatcher.updateConfig(serviceId, serviceConfig)

        val currentServiceConfig = dispatcher.configState.value.serviceConfigs[serviceId]
        assertEquals(serviceConfig, currentServiceConfig)
    }

    @Test
    fun `configForService should emit service config`() = runTest {
        val initialConfig = OGTrackingConfig(
            trackingEnabled = false,
            logLevel = LogLevel.Debug,
            viewEventMapping = emptyMap()
        )
        val dispatcher = OGConfigDispatcher(initialConfig)
        val serviceId = OGTrackingServiceId.Adjust
        val serviceConfig: ServiceConfig = AdjustConfig(true)
        dispatcher.updateConfig(serviceId, serviceConfig)

        val emittedServiceConfig = dispatcher.configForService<AdjustConfig>(serviceId).first()

        assertEquals(serviceConfig, emittedServiceConfig)
    }

    @Test
    fun `configForService should emit service config on context update`() = runTest {
        val initialConfig = OGTrackingConfig(
            trackingEnabled = false,
            logLevel = LogLevel.Debug,
            viewEventMapping = emptyMap()
        )
        val dispatcher = OGConfigDispatcher(initialConfig)
        val serviceId = OGTrackingServiceId.Adjust

        dispatcher.configForService<AdjustConfig>(serviceId).test {
            dispatcher.updateConfig(serviceId, AdjustConfig(true))
            val serviceConfig = AdjustConfig(
                true,
                eventTokenMapping = emptyMap(),
                listOf(
                    JsonObject(
                        mapOf(
                            "data" to JsonObject(
                                mapOf(
                                    "test" to JsonPrimitive("")
                                )
                            )
                        )
                    )
                )
            )

            dispatcher.updateConfig(serviceId, serviceConfig)
            val default = awaitItem()
            val updated = awaitItem()
            assertEquals(serviceConfig, updated)
            assertEquals(serviceConfig.globalContext, updated.globalContext)
            assertTrue(updated.globalContext.isNotEmpty())
        }
    }

    @Test
    fun `configForService should throw exception when invalid type`() = runTest {
        val initialConfig = OGTrackingConfig(
            trackingEnabled = false,
            logLevel = LogLevel.Debug,
            viewEventMapping = emptyMap()
        )
        val dispatcher = OGConfigDispatcher(initialConfig)
        val serviceId = OGTrackingServiceId.Adjust
        val serviceConfig: ServiceConfig = AdjustConfig(true)
        dispatcher.updateConfig(serviceId, serviceConfig)

        assertFailsWith<ClassCastException> {
            val config: FirebaseConfig? =
                dispatcher.configForService<FirebaseConfig>(serviceId).firstOrNull()
        }
    }
}
