package com.ottogroup.appkit.tracking.consent

import com.ottogroup.appkit.test.LocalDataStore
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class OGTrackingConsentLocalDataSourceTest {

    private val dataStore = LocalDataStore(MutableStateFlow(OGTrackingConsentState()))
    private val dataSource = OGTrackingConsentLocalDataSource(dataStore)

    @Test
    fun `setGlobalConsent should update global consent in dataStore`() = runTest {
        dataSource.setGlobalConsent(true)

        val state = dataSource.trackingConsentState.first()
        assertTrue(state.globalConsent)
    }

    @Test
    fun `setConsentsForServices should update service consents in dataStore`() = runTest {
        val consents = mapOf(OGTrackingServiceId.Adjust to true)

        dataSource.setConsentsForServices(consents)

        val state = dataSource.trackingConsentState.first()
        assertEquals(consents, state.serviceConsents)
    }

    @Test
    fun `setConsentsForServices should replace existing service consents in dataStore when replacePrevious is true`() = runTest {
        val initialConsents = mapOf(OGTrackingServiceId.Adjust to false)
        val newConsents = mapOf(OGTrackingServiceId.Adjust to true)

        dataSource.setConsentsForServices(initialConsents)
        dataSource.setConsentsForServices(newConsents, replacePrevious = true)

        val state = dataSource.trackingConsentState.first()
        assertEquals(newConsents, state.serviceConsents)
    }
}
