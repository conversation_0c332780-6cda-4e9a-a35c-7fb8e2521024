package com.ottogroup.appkit.tracking.services

import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.JsonObject

class TrackingServiceTest {

    @Test
    fun `concurrent modifications to cachedUserProperties are thread-safe`() {
        // not using runTest to get realistic scheduling/concurrency behavior
        runBlocking {
            // required to fail this test when an exception happens in a different coroutine scope
            var exceptionThrownInCoroutineScope: Throwable? = null
            val handler = CoroutineExceptionHandler { _, t ->
                exceptionThrownInCoroutineScope = t
            }

            val trackingDealer = OGTrackingDealer(
                enabledFlow = flowOf(true),
                userPropertyContributors = emptyList(),
                consent = FakeOGTrackingConsent(),
                coroutineScope = CoroutineScope(EmptyCoroutineContext + handler),
            )
            val configFlow = MutableSharedFlow<TestServiceConfig>(replay = 1)
            val trackingService = SlowTestService(
                configFlow = configFlow,
                trackingDealer = trackingDealer,
                coroutineScope = CoroutineScope(EmptyCoroutineContext + handler),
            )

            // set some user properties while the service is disabled to fill the cache
            val properties = listOf(
                UserProperty.MotionSensitivityUserProperty(true),
                UserProperty.FontSizeZoomUserProperty(10.0f),
                UserProperty.DeviceOrientationUserProperty.Portrait,
                UserProperty.ScreenReaderUserProperty(true),
            )
            for (property in properties) {
                trackingDealer.setUserProperty(property)
            }

            // enable the service to start looping over the cached properties
            launch {
                configFlow.emit(TestServiceConfig())
            }
            val finalProperty = UserProperty.PushNotificationOptInUserProperty(true)
            launch {
                /* Config emission handling in the service has 500ms delay baked in.
                 * Add some on top to ensure we are emitting this property while the config change is being processed
                 * and the service currently loops over the cached properties to trip a potential
                 * ConcurrentModificationException.
                 */
                delay(700)
                trackingDealer.setUserProperty(finalProperty)
            }

            // allow enough time for the config emission + property processing to finish
            delay(1300)

            // if there was an exception in the coroutines, rethrow it here to fail the test
            exceptionThrownInCoroutineScope?.let { throw it }

            assertEquals<Map<String, String?>>(
                (properties + finalProperty).associate { it.key to it.value },
                trackingService.receivedUserProperties,
            )
        }
    }
}

private class TestServiceConfig : ServiceConfig() {
    override val isEnabled: Boolean = true

    override fun baseCopy(
        isEnabled: Boolean,
        globalContext: List<JsonObject>
    ): ServiceConfig {
        TODO("Not used in test")
    }
}

private class TestServiceEvent : ServiceEvent {
    override fun describe(): Map<String, Any?> {
        TODO("Not used in test")
    }
}

private class SlowTestService(
    configFlow: Flow<TestServiceConfig>,
    trackingDealer: OGTrackingDealer,
    private val coroutineScope: CoroutineScope
) : OGTrackingService<TestServiceConfig, TestServiceEvent>(
    trackingDealer = trackingDealer,
    isInitiallyEnabled = false,
    configFlow = configFlow,
    serviceId = OGTrackingServiceId.Adjust,
    coroutineScope = coroutineScope,
) {
    override val config: StateFlow<TestServiceConfig> =
        configFlow.stateIn(coroutineScope, SharingStarted.Eagerly, TestServiceConfig())

    override fun mapEvent(trackingEvent: OGEvent): TestServiceEvent {
        return TestServiceEvent()
    }

    val receivedUserProperties = mutableMapOf<String, String?>()
    override fun setUserProperty(key: String, value: String?) {
        // simulate very slow service handling user property updates
        runBlocking { delay(100) }
        receivedUserProperties[key] = value
    }
}

private class FakeOGTrackingConsent : OGTrackingConsent {
    private val globalConsent = MutableStateFlow<Boolean>(true)
    private val consents = MutableStateFlow<Map<OGTrackingServiceId, Boolean>>(emptyMap())

    override fun consentForService(serviceId: OGTrackingServiceId): Flow<Boolean> {
        return combine(globalConsent, consents) { global, serviceConsents ->
            serviceConsents[serviceId] ?: global
        }
    }

    override fun setGlobalConsent(consent: Boolean) {
        globalConsent.value = consent
    }

    override fun setConsentsForServices(consents: Map<OGTrackingServiceId, Boolean>, replacePrevious: Boolean) {
        this.consents.value = if (replacePrevious) {
            consents
        } else {
            this.consents.value + consents
        }
    }
}
