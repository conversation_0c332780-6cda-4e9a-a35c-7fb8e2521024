package com.ottogroup.appkit.tracking.json

import kotlin.test.Test
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonArray

class JsonPrimitiveTest {

    @Test
    fun `converts JsonNull to null`() {
        val jsonNull = JsonNull
        assertNull(jsonNull.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive string to Kotlin String`() {
        val jsonString = JsonPrimitive("testString")
        assertEquals("testString", jsonString.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive boolean to Kotlin Boolean`() {
        val jsonBoolean = JsonPrimitive(true)
        assertEquals(true, jsonBoolean.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive int to Kotlin Int`() {
        val jsonInt = JsonPrimitive(2147483647)
        assertEquals(2147483647, jsonInt.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive long to Kotlin Long`() {
        val jsonLong = JsonPrimitive(2147483648)
        assertEquals(2147483648, jsonLong.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive float to Kotlin Double`() {
        val jsonFloat = JsonPrimitive(42.99999f)
        val actualValue = jsonFloat.toPrimitive()
        assertEquals(true, actualValue is Double)
        assertEquals(42.99999, actualValue)
    }

    @Test
    fun `converts JsonPrimitive double to Kotlin Double`() {
        val double2 = 42.999999999999990
        val jsonDouble = JsonPrimitive(double2)
        val actualValue = jsonDouble.toPrimitive()
        assertEquals(true, actualValue is Double)
        assertEquals(double2, actualValue as Double, 0.0000000001)
    }

    @Test
    fun `converts JsonPrimitive with non-numeric string to Kotlin String`() {
        val jsonString = JsonPrimitive("nonNumeric")
        assertEquals("nonNumeric", jsonString.toPrimitive())
    }

    @Test
    fun `converts JsonPrimitive with numeric string to Kotlin String`() {
        val jsonString = JsonPrimitive("42")
        assertEquals("42", jsonString.toPrimitive())
    }

    @Test
    fun `converts serialized list to correct ttype`() {
        val json =
            Json.parseToJsonElement("[null,\"string\",true, 2147483647,2147483648,42.0000,42.999999999999990, \"42\"]")
        val actual = json.jsonArray.map { it.toPrimitive() }
        val expected = listOf(
            null,
            "string",
            true,
            2147483647,
            2147483648L,
            42.0000,
            42.999999999999990,
            "42"
        )
        assertContentEquals(expected, actual)
    }
}
