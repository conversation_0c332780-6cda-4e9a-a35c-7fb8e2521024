package com.ottogroup.appkit.tracking

import app.cash.turbine.test
import com.ottogroup.appkit.base.uri.UrlMatcher
import com.ottogroup.appkit.test.mockDataStore
import com.ottogroup.appkit.tracking.config.OGConfigDispatcher
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentImpl
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentLocalDataSource
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentState
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.adjust.AdjustConfig
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

@OptIn(ExperimentalCoroutinesApi::class)
class OGTrackingImplTest {

    @Test
    fun `configure updates configDispatcher`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)
        val config = OGTrackingConfig(false, LogLevel.Error, emptyMap())
        ogTracking.configure(config)
        assertEquals(config, configDispatcher.configState.value.baseConfig)
    }

    @Test
    fun `onDidLoad tracks event when URL matches`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(
                OGTrackingConfig(
                    true,
                    LogLevel.Debug,
                    mapOf(
                        View.Screen.Home to listOf(
                            UrlMatcher("host:example.com")
                        )
                    )
                )
            )
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                true,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)

        val url = "http://example.com"
        var actualEvent: OGEvent? = null
        advanceTimeBy(100)
        trackingDealer.events.onEach { actualEvent = it }.launchIn(backgroundScope)
        ogTracking.onDidLoad(url)
        advanceTimeBy(100)
        assertEquals(View.Screen.Home, actualEvent)
    }

    @Test
    fun `onDidLoad does not track event when URL does not match`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)

        val url = "http://other.com"
        val config = OGTrackingConfig(
            true,
            LogLevel.Debug,
            mapOf(View.Screen.Home to listOf(UrlMatcher("host:example.com")))
        )
        ogTracking.configure(config)
        var actualEvent: OGEvent? = null
        advanceTimeBy(100)
        trackingDealer.events.onEach { actualEvent = it }.launchIn(backgroundScope)
        ogTracking.onDidLoad(url)
        advanceTimeBy(100)
        assertEquals(null, actualEvent)
    }

    @Test
    fun `onDidLoad can handle malformed URLs`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(
                OGTrackingConfig(
                    true,
                    LogLevel.Debug,
                    mapOf(
                        View.Screen.Home to listOf(
                            UrlMatcher("host:example.com")
                        )
                    )
                )
            )
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                true,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)

        advanceTimeBy(100)
        ogTracking.onDidLoad("https://example.com?foo=bar baz")
    }

    @Test
    fun `track sends event to trackingDealer`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)
        val event = View.Screen.Home
        var actualEvent: OGEvent? = null
        advanceTimeBy(100)
        trackingDealer.events.onEach { actualEvent = it }.launchIn(backgroundScope)
        ogTracking.track(event)
        advanceTimeBy(100)
        assertEquals(event, actualEvent)
    }

    @Test
    fun `setConsent updates consent for specific service`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)

        ogTracking.setConsent(OGTrackingServiceId.Adjust, true)
        advanceTimeBy(100)
        assertEquals(true, consentFlow.value.serviceConsents[OGTrackingServiceId.Adjust])
    }

    @Test
    fun `setGlobalConsent updates global consent`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)
        ogTracking.setGlobalConsent(true)
        advanceTimeBy(100)
        assertTrue(consentFlow.value.globalConsent)
    }

    @Test
    fun `updateGlobalContext is updating config for specific service`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        configDispatcher.updateConfig(OGTrackingServiceId.Adjust, AdjustConfig(false))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)
        val contextParameters =
            listOf(JsonObject(mapOf("key" to JsonPrimitive("value"))), mapOf("key2" to "value2"))

        ogTracking.updateGlobalContext(OGTrackingServiceId.Adjust, contextParameters)
        configDispatcher.configState.test {
            advanceTimeBy(100)
            assertEquals(
                listOf(
                    JsonObject(mapOf("key" to JsonPrimitive("value"))),
                    JsonObject(mapOf("key2" to JsonPrimitive("value2")))
                ),
                awaitItem().serviceConfigs[OGTrackingServiceId.Adjust]?.globalContext
            )
        }
    }

    @Test
    fun `onUpdateConfig updates config for specific service`() = runTest {
        val configDispatcher =
            OGConfigDispatcher(OGTrackingConfig(true, LogLevel.Debug, emptyMap()))
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val trackingConsent = OGTrackingConsentImpl(
            backgroundScope,
            dataStore
        )
        val trackingDealer = OGTrackingDealer(
            MutableStateFlow(true),
            emptyList(),
            trackingConsent,
            backgroundScope
        )
        val ogTracking = OGTrackingImpl(trackingConsent, trackingDealer, configDispatcher)
        val config = AdjustConfig(true)

        ogTracking.onUpdateConfig(OGTrackingServiceId.Adjust, config)
        assertEquals(
            config,
            configDispatcher.configState.value.serviceConfigs[OGTrackingServiceId.Adjust]
        )
    }
}
