package com.ottogroup.appkit.tracking

import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import com.ottogroup.appkit.test.mockDataStore
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentImpl
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentLocalDataSource
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentState
import com.ottogroup.appkit.tracking.event.View.Screen
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import com.ottogroup.appkit.tracking.userproperty.UserPropertyContributor
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class OGTrackingDealerTest {

    @Test
    fun `events are dispatched when tracking enabled `() = runTest {
        // given
        val enabled = MutableStateFlow(true)
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val consent = OGTrackingConsentImpl(backgroundScope, dataStore)
        val dealer = OGTrackingDealer(enabled, emptyList(), consent, backgroundScope)

        // when
        dealer.events.test {
            advanceTimeBy(100)
            dealer.trackEvent(Screen.Home)

            // then
            assertEquals(Screen.Home, awaitItem())
        }
    }

    @Test
    fun `events are ignored when tracking disabled `() = runTest {
        // given
        val enabled = MutableStateFlow(false)
        val consentFlow = MutableStateFlow(
            OGTrackingConsentState(
                false,
                emptyMap()
            )
        )
        val jsonDataStore = mockDataStore(
            consentFlow
        )
        val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
        val consent = OGTrackingConsentImpl(backgroundScope, dataStore)
        val dealer = OGTrackingDealer(enabled, emptyList(), consent, backgroundScope)

        // when
        dealer.events.test {
            advanceTimeBy(100)
            dealer.trackEvent(Screen.Home)
            // then
            expectNoEvents()
        }
    }

    @Test
    fun `userProperty is emitted when setUserProperty is called and tracking is enabled`() =
        runTest {
            // given
            val dealer = createDealer(isEnabled = flowOf(true), this)
            val userProperty = UserProperty.PushNotificationOptInUserProperty(true)

            // when
            dealer.userProperty.test {
                advanceTimeBy(100)
                dealer.setUserProperty(userProperty)

                // then
                assertEquals(userProperty, awaitItem())
            }
        }

    @Test
    fun `userProperty is ignored when setUserProperty is called and tracking is disabled`() =
        runTest {
            // given
            val dealer = createDealer(isEnabled = flowOf(false), this)
            val userProperty = UserProperty.PushNotificationOptInUserProperty(true)

            // when
            dealer.userProperty.test {
                advanceTimeBy(100)
                dealer.setUserProperty(userProperty)

                // then
                expectNoEvents()
            }
        }

    @Test
    fun `lifecycle aware userProperty is ignored when app is in background and emitted on foreground`() =
        runTest {
            // given
            val lifecycleFlow: MutableStateFlow<LifecycleEvent> =
                MutableStateFlow(LifecycleEvent.AppInBackground)
            val lifecycleProvider = object : ApplicationLifecycleProvider {
                override val lifecycle = lifecycleFlow
            }
            val userPropertyContributor = UserPropertyContributorMock(lifecycleProvider)
            val dealer =
                createDealer(isEnabled = flowOf(true), this, listOf(userPropertyContributor))
            // when
            dealer.userProperty.test {
                lifecycleFlow.emit(LifecycleEvent.AppInBackground)
                advanceTimeBy(100)
                // then
                expectNoEvents()
                lifecycleFlow.emit(LifecycleEvent.AppInForeground)
                advanceTimeBy(100)
                assertIs<UserProperty.PushNotificationOptInUserProperty>(awaitItem())
            }
        }

    @Test
    fun `userProperty flow emits no initial values when cache is empty`() = runTest {
        // given
        val dealer = createDealer(isEnabled = flowOf(true), this)

        // when
        dealer.userProperty.test {
            advanceTimeBy(100)
            // then
            expectNoEvents()
        }
    }

    @Test
    fun `userProperty flow emits initial value when single property is cached`() = runTest {
        // given
        val dealer = createDealer(isEnabled = flowOf(true), this)
        val userProperty = UserProperty.PushNotificationOptInUserProperty(true)

        // Cache a property first
        dealer.setUserProperty(userProperty)
        assertEquals(userProperty, dealer.userPropertyCache.value.values.firstOrNull())
        // when - new collector observes the flow
        dealer.userProperty.test {
            // then - initial cached value is immediately emitted
            assertEquals(userProperty, awaitItem())
            expectNoEvents()
        }
    }

    @Test
    fun `userProperty flow emits all initial values when multiple properties are cached`() =
        runTest {
            // given
            val dealer = createDealer(isEnabled = flowOf(true), this)
            val pushProperty = UserProperty.PushNotificationOptInUserProperty(false)
            val orientationProperty = UserProperty.DeviceOrientationUserProperty.Portrait
            val fontSizeProperty = UserProperty.FontSizeZoomUserProperty(1.5f)

            // Cache multiple properties
            dealer.setUserProperty(pushProperty)
            dealer.setUserProperty(orientationProperty)
            dealer.setUserProperty(fontSizeProperty)

            // when - new collector observes the flow
            dealer.userProperty.test {
                assertEquals(pushProperty, awaitItem())
                assertEquals(orientationProperty, awaitItem())
                assertEquals(fontSizeProperty, awaitItem())

                expectNoEvents()
            }
        }

    @Test
    fun `userProperty flow emits initial values even when tracking is disabled`() = runTest {
        // given
        val dealer = createDealer(isEnabled = flowOf(true), this)
        val userProperty = UserProperty.ScreenReaderUserProperty(true)

        // Cache property while tracking is enabled
        dealer.setUserProperty(userProperty)

        // Create new dealer with tracking disabled but same cached state
        val disabledDealer = createDealer(
            isEnabled = flowOf(false),
            scope = this,
            cachedProperties = mapOf(userProperty.key to userProperty)
        )

        // when - collector observes disabled dealer's flow
        disabledDealer.userProperty.test {
            // then - cached value is still emitted as initial value
            assertEquals(userProperty, awaitItem())
            expectNoEvents()
        }
    }

    @Test
    fun `userProperty flow emits initial values followed by new property updates`() = runTest {
        // given
        val dealer = createDealer(flowOf(true), this)
        val cachedProperty = UserProperty.MotionSensitivityUserProperty(false)
        val newProperty = UserProperty.PushNotificationOptInUserProperty(true)

        advanceTimeBy(100)

        // Cache a property first
        dealer.setUserProperty(cachedProperty)
        assertEquals(cachedProperty, dealer.userPropertyCache.value.values.firstOrNull())

        // when - new collector observes and new property is set
        dealer.userProperty.test {
            // then - initial cached value is emitted first
            assertEquals(cachedProperty, awaitItem())

            // when - new property is set
            dealer.setUserProperty(newProperty)
            // then - new property is emitted after initial values
            assertEquals(cachedProperty, awaitItem())
            assertEquals(newProperty, awaitItem())
            expectNoEvents()
        }
    }

    @Test
    fun `userProperty flow only emits changed properties as initial values`() = runTest {
        // given
        val dealer = createDealer(isEnabled = flowOf(true), this)
        val userProperty1 = UserProperty.FontSizeZoomUserProperty(1.0f)
        val userProperty2 = UserProperty.FontSizeZoomUserProperty(2.0f)

        // Set same property multiple times
        dealer.setUserProperty(userProperty1)
        dealer.setUserProperty(userProperty2) // Same value, should not cause duplicate cache

        // when - new collector observes the flow
        dealer.userProperty.test {
            // then - only one initial value is emitted (no duplicates)
            assertEquals(userProperty2, awaitItem())
            expectNoEvents()
        }
    }
}

private fun createDealer(
    isEnabled: Flow<Boolean>,
    scope: TestScope,
    userPropertyContributors: List<UserPropertyContributor> = emptyList(),
    cachedProperties: Map<String, UserProperty> = emptyMap(),
): OGTrackingDealer {
    val consentFlow = MutableStateFlow(
        OGTrackingConsentState(
            false,
            emptyMap()
        )
    )
    val jsonDataStore = mockDataStore(consentFlow)
    val dataStore = OGTrackingConsentLocalDataSource(jsonDataStore)
    val consent = OGTrackingConsentImpl(scope.backgroundScope, dataStore)

    return OGTrackingDealer(
        isEnabled,
        userPropertyContributors,
        consent,
        scope.backgroundScope
    ).apply {
        userPropertyCache.value = cachedProperties
    }
}

private class UserPropertyContributorMock(
    lifeCycleProvider: ApplicationLifecycleProvider
) : UserPropertyContributor {
    override val userProperty: Flow<UserProperty> = lifeCycleProvider.lifecycle
        .filter { it == LifecycleEvent.AppInForeground }
        .map { UserProperty.PushNotificationOptInUserProperty(false) }
}
