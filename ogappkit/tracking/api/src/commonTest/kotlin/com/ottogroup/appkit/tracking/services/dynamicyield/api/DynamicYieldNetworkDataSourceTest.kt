package com.ottogroup.appkit.tracking.services.dynamicyield.api

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.http.CookiesBridge
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldConfig
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent
import com.ottogroup.appkit.tracking.services.dynamicyield.api.request.toJsonArray
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.ProductData
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Variation
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json

class DynamicYieldNetworkDataSourceTest {

    private val cookiesBridge = TestCookiesBridge()
    private val configFlow = MutableStateFlow(
        DynamicYieldConfig(
            trackPageViewUrl = "https://api.dynamicyield.com/track",
            apiKey = "secret",
            cookiesDomain = COOKIES_DOMAIN,
            cookiesBridge = cookiesBridge,
            isEnabled = true
        )
    )

    private fun createDataSource(mockEngine: MockEngine) = DynamicYieldNetworkDataSourceImpl(
        configFlow,
        dynamicYieldHttpClient(customEngine = mockEngine)
    )

    @Test
    fun `trackPageView sends well-formed request`() = runTest {
        cookiesBridge.cookies = mapOf(
            "_dyid" to "12345678",
            "_dyid_server" to "12345678",
            "_dyjsession" to "abcdefgh"
        )

        var error: Throwable? = null
        val dataSource = createDataSource(
            MockEngine { request ->
                try {
                    assertEquals(
                        configFlow.value.trackPageViewUrl,
                        request.url.toString(),
                    )

                    assertEquals(
                        configFlow.value.apiKey,
                        request.headers["dy-api-key"]
                    )

                    assertEquals(
                        Json.parseToJsonElement(
                            goldenDYTrackingRequest(
                                "12345678",
                                "abcdefgh",
                                "sku",
                                "location"
                            )
                        ),
                        Json.parseToJsonElement(request.body.toByteArray().decodeToString())
                    )
                } catch (e: Throwable) {
                    // need to propagate the error onto the main thread
                    error = e
                }

                respond(content = ByteReadChannel(""))
            }
        )
        dataSource.trackPageView(
            DynamicYieldEvent.PageView(
                type = DynamicYieldEvent.PageView.Type.PRODUCT,
                data = listOf("sku"),
                location = "location"
            )
        )

        error?.let { throw it }
    }

    @Test
    fun `trackPageView updates returned DY identifiers`() = runTest {
        val dataSource = createDataSource(
            MockEngine { request ->
                respond(
                    content = ByteReadChannel(dummyTrackingResponse("12345678", "abcdefgh")),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        )
        dataSource.trackPageView(
            DynamicYieldEvent.PageView(
                type = DynamicYieldEvent.PageView.Type.PRODUCT,
                data = listOf("sku"),
                location = "location"
            )
        )

        assertEquals(
            listOf(
                COOKIES_DOMAIN to "_dyid_server=12345678; Max-Age=31556926",
                COOKIES_DOMAIN to "_dyjsession=abcdefgh; Max-Age=1800"
            ),
            cookiesBridge.setCookies
        )
    }

    @Test
    fun `chooseVariations sends well-formed request`() = runTest {
        cookiesBridge.cookies = mapOf(
            "_dyid" to "12345678",
            "_dyid_server" to "12345678",
            "_dyjsession" to "abcdefgh"
        )

        var error: Throwable? = null
        val dataSource = createDataSource(
            MockEngine { request ->
                try {
                    assertEquals(
                        configFlow.value.trackPageViewUrl,
                        request.url.toString(),
                    )

                    assertEquals(
                        configFlow.value.apiKey,
                        request.headers["dy-api-key"]
                    )

                    assertEquals(
                        Json.parseToJsonElement(
                            goldenDYChooseRequest(
                                "12345678",
                                "abcdefgh",
                                "sku",
                                "location",
                                listOf("selector1", "selector2"),
                                listOf("group"),
                            )
                        ),
                        Json.parseToJsonElement(request.body.toByteArray().decodeToString())
                    )
                } catch (e: Throwable) {
                    // need to propagate the error onto the main thread
                    error = e
                }

                respond(content = ByteReadChannel(""))
            }
        )
        dataSource.chooseVariations(
            DynamicYieldEvent.PageView(
                type = DynamicYieldEvent.PageView.Type.PRODUCT,
                data = listOf("sku"),
                location = "location"
            ),
            listOf("selector1", "selector2"),
            listOf("group")
        )

        error?.let { throw it }
    }

    @Test
    fun `chooseVariations does not update DY identifiers`() = runTest {
        cookiesBridge.cookies = mapOf(
            "_dyid" to "12345678",
            "_dyid_server" to "12345678",
            "_dyjsession" to "abcdefgh"
        )

        val dataSource = createDataSource(
            MockEngine { request ->
                respond(content = ByteReadChannel(dummyChooseResponse()))
            }
        )
        dataSource.chooseVariations(
            DynamicYieldEvent.PageView(
                type = DynamicYieldEvent.PageView.Type.PRODUCT,
                data = listOf("sku"),
                location = "location"
            ),
            emptyList(),
            listOf("App")
        )

        assertEquals(
            emptyList(),
            cookiesBridge.setCookies
        )
    }

    @Test
    fun `chooseVariations returns parsed choices`() = runTest {
        val dataSource = createDataSource(
            MockEngine { request ->
                respond(
                    content = ByteReadChannel(dummyChooseResponse()),
                    headers = headersOf(HttpHeaders.ContentType, "application/json")
                )
            }
        )
        val result = dataSource.chooseVariations(
            DynamicYieldEvent.PageView(
                type = DynamicYieldEvent.PageView.Type.PRODUCT,
                data = listOf("sku"),
                location = "location"
            ),
            emptyList(),
            listOf("App")
        )

        assertEquals(
            Result.Success(
                listOf(
                    Choice.RecommendationsChoice(
                        name = "app zuletzt gesehen",
                        variations = listOf(
                            Variation<Variation.Payload.RecommendationsPayload>(
                                id = "102632354",
                                payload = Variation.Payload.RecommendationsPayload(
                                    data = Variation.Payload.SlotData(
                                        slots = listOf(
                                            Variation.Payload.SlotData.Slot(
                                                sku = "67286322_48",
                                                slotId = "mIOkdHlwZadQUk9EVUNUqGxvY2F0aW9uulByb2R1Y3REZXRhaWwvOTM1MTM3NTlfMTAwpGRhdGGRrDkzNTEzNzU5XzEwMM4AAaEmzgAC1P/DAKs2NzI4NjMyMl80OA4A*nLM4MTQwNTcyOTY3MjkwNzE4NzU0zgAbK4GpMTAzNjY4NjE5AZCRzgYeC6KlMTk5MTnA2SBjYWNkMDRlODA0MzFmZTZlMjgwNjhiZTlhZWViZjEyNrQtMTg5NzYxMTg0NzcyODc2NTk3OcCqU01BUlRQSE9ORQ==",
                                                productData = ProductData(
                                                    brand = "LASCANA",
                                                    name = "Strickkleid",
                                                    url = "https://www.lascana.de/strickkleid-lascana-1710785248.html?variantId=1710785651",
                                                    imageUrl = "https://bilder.lascana.de/styles/479x684/lascana-strickkleid-beige-meliert-560233140.jpg",
                                                    price = 83.99f,
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            ),
            result
        )
    }
}

private fun dummyTrackingResponse(dyId: String, dyJSession: String) = """
    {
      "choices": [],
      "cookies": [
        {
          "name": "_dyid_server",
          "value": "$dyId",
          "maxAge": "31556926"
        },
        {
          "name": "_dyjsession",
          "value": "$dyJSession",
          "maxAge": "1800"
        }
      ]
    }
""".trimIndent()

private fun goldenDYTrackingRequest(dyId: String, dyJSession: String, sku: String, location: String) = """
    {
      "user": {
        "active_consent_accepted": true,
        "dyid": "$dyId",
        "dyid_server": "$dyId"
      },
      "session": {
        "dy": "$dyJSession"
      },
      "context": {
        "page": {
          "type": "PRODUCT",
          "location": "$location",
          "data": [
            "$sku"
          ]
        },
        "device": {
          "type": "SMARTPHONE"
        },
        "channel": "APP"
      },
      "options": {
        "isImplicitPageview": true
      }
    }
""".trimIndent()

private fun goldenDYChooseRequest(
    dyId: String,
    dyJSession: String,
    sku: String,
    location: String,
    selectorNames: List<String>,
    groupNames: List<String>,
) = """
    {
      "user": {
        "active_consent_accepted": true,
        "dyid": "$dyId",
        "dyid_server": "$dyId"
      },
      "session": {
        "dy": "$dyJSession"
      },
      "context": {
        "page": {
          "type": "PRODUCT",
          "location": "$location",
          "data": ["$sku"]
        },
        "device": {
          "type": "SMARTPHONE"
        },
        "channel": "APP"
      },
      "selector": {
        "names": ${selectorNames.toJsonArray()},
        "groups": ${groupNames.toJsonArray()}
      },
      "options": {
        "isImplicitImpressionMode": true,
        "recsProductData": {
          "fieldFilter": ["brand", "name", "image_url", "price", "url"]
        }
      }
    }
""".trimIndent()

private fun dummyChooseResponse() = """
    {
      "choices": [
        {
          "id": 1329578,
          "name": "app zuletzt gesehen",
          "type": "RECS_DECISION",
          "variations": [
            {
              "id": 102632354,
              "payload": {
                "data": {
                  "slots": [
                    {
                      "sku": "67286322_48",
                      "productData": {
                        "brand": "LASCANA",
                        "name": "Strickkleid",
                        "url": "https://www.lascana.de/strickkleid-lascana-1710785248.html?variantId=1710785651",
                        "image_url": "https://bilder.lascana.de/styles/479x684/lascana-strickkleid-beige-meliert-560233140.jpg",
                        "price": 83.99
                      },
                      "slotId": "mIOkdHlwZadQUk9EVUNUqGxvY2F0aW9uulByb2R1Y3REZXRhaWwvOTM1MTM3NTlfMTAwpGRhdGGRrDkzNTEzNzU5XzEwMM4AAaEmzgAC1P/DAKs2NzI4NjMyMl80OA4A*nLM4MTQwNTcyOTY3MjkwNzE4NzU0zgAbK4GpMTAzNjY4NjE5AZCRzgYeC6KlMTk5MTnA2SBjYWNkMDRlODA0MzFmZTZlMjgwNjhiZTlhZWViZjEyNrQtMTg5NzYxMTg0NzcyODc2NTk3OcCqU01BUlRQSE9ORQ=="
                    }
                  ]
                },
                "type": "RECS"
              }
            }
          ],
          "groups": [
            "App"
          ],
          "decisionId": "nLM4MTQwNTcyOTY3MjkwNzE4NzU0zgAbK4GpMTAzNjY4NjE5AZCRzgYeC6KlMTk5MTnA2SBjYWNkMDRlODA0MzFmZTZlMjgwNjhiZTlhZWViZjEyNrQtMTg5NzYxMTg0NzcyODc2NTk3OcCqU01BUlRQSE9ORQ=="
        }
      ],
      "cookies": [
        {
          "name": "_dyid_server",
          "value": "6624796011377269104",
          "maxAge": "31556926"
        },
        {
          "name": "_dyjsession",
          "value": "61fjcer7kn8urlgqdz2f9ijh6m12lar1",
          "maxAge": "1800"
        }
      ]
    }
""".trimIndent()

private class TestCookiesBridge : CookiesBridge {
    var cookies: Map<String, String> = emptyMap()
    override suspend fun getCookies(url: String): Map<String, String> {
        return cookies
    }

    private val _setCookies = mutableListOf<Pair<String, String>>()
    val setCookies: List<Pair<String, String>> = _setCookies
    override suspend fun setCookie(url: String, cookie: String) {
        _setCookies += url to cookie
    }
}

private const val COOKIES_DOMAIN = "https://www.lascana.de"
