package com.ottogroup.appkit.tracking.consent

import com.ottogroup.appkit.test.LocalDataStore
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class OGTrackingConsentTest {

    private val dataStore = LocalDataStore(MutableStateFlow(OGTrackingConsentState()))
    private val dataSource = OGTrackingConsentLocalDataSource(dataStore)

    @Test
    fun `setGlobalConsent should update global consent in dataStore`() = runTest {
        val trackingConsent = OGTrackingConsentImpl(this, dataSource)

        trackingConsent.setGlobalConsent(true)

        val state = dataSource.trackingConsentState.first()
        assertTrue(state.globalConsent)
    }

    @Test
    fun `setConsentsForServices should update service consents in dataStore`() = runTest {
        val trackingConsent = OGTrackingConsentImpl(this, dataSource)
        val consents = mapOf(OGTrackingServiceId.Adjust to true)

        trackingConsent.setConsentsForServices(consents)

        val state = dataSource.trackingConsentState.first()
        assertEquals(consents, state.serviceConsents)
    }

    @Test
    fun `setConsentsForServices should replace existing service consents in dataStore when replacePrevious is true`() = runTest {
        val trackingConsent = OGTrackingConsentImpl(this, dataSource)
        val initialConsents = mapOf(OGTrackingServiceId.Adjust to false)
        val newConsents = mapOf(OGTrackingServiceId.Adjust to true)

        trackingConsent.setConsentsForServices(initialConsents)
        trackingConsent.setConsentsForServices(newConsents, replacePrevious = true)

        val state = dataSource.trackingConsentState.first()
        assertEquals(newConsents, state.serviceConsents)
    }

    @Test
    fun `consentForService should return the correct consent for a service`() = runTest {
        val trackingConsent = OGTrackingConsentImpl(this, dataSource)
        val consents = mapOf(OGTrackingServiceId.Adjust to true)

        trackingConsent.setConsentsForServices(consents)

        val consent = trackingConsent.consentForService(OGTrackingServiceId.Adjust).first()
        assertTrue(consent)
    }

    @Test
    fun `consentForService should return global consent if no individual consent is set`() = runTest {
        val trackingConsent = OGTrackingConsentImpl(this, dataSource)
        trackingConsent.setGlobalConsent(true)

        val consent = trackingConsent.consentForService(OGTrackingServiceId.Adjust).first()
        assertTrue(consent)
    }
}
