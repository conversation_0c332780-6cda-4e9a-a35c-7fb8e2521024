package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity(
    indices = [Index(value = ["timestamp"])]
)
internal data class LogEntity(
    @PrimaryKey(autoGenerate = true)
    val logId: Long = 0,
    val timestamp: Long,
    val message: String,
)

internal data class LogWithNetOrEventOrSysOrCustom(
    @Embedded
    val logEntity: LogEntity,

    @Relation(
        entity = NetDataEntity::class,
        parentColumn = "logId",
        entityColumn = "netId",
        associateBy = Junction(
            value = LogToNetMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "netIdRef"
        )
    )
    val netEntity: NetDataEntity?,

    @Relation(
        entity = SysDataEntity::class,
        parentColumn = "logId",
        entityColumn = "sysId",
        associateBy = Junction(
            value = LogToSysMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "sysIdRef"
        )
    )
    val sysEntity: SysDataEntity?,

    @Relation(
        entity = EventEntity::class,
        parentColumn = "logId",
        entityColumn = "eventId",
        associateBy = Junction(
            value = LogToEventMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "eventIdRef"
        )
    )
    val eventEntity: EventWithTrackingServices?,

)
