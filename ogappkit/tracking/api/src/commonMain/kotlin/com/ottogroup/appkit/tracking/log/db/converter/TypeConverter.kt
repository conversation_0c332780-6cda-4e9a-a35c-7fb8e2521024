package com.ottogroup.appkit.tracking.log.db.converter

import androidx.room.ProvidedTypeConverter
import androidx.room.TypeConverter
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toJsonElement
import com.ottogroup.appkit.tracking.json.toPrimitive
import com.ottogroup.appkit.tracking.log.db.schema.Parameter
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive

@ProvidedTypeConverter
internal class TypeConverter {
    @TypeConverter
    fun stringToMapStringAny(string: String?): Map<String, Any?>? {
        val value = Json.decodeFromString<JsonObject>(string ?: return emptyMap())
        return value.entries.associate { it.key to it.value.toPrimitive() }
    }

    @TypeConverter
    fun mapStringAnyToString(example: Map<String, Any?>?): String? {
        return Json.encodeToString(example.toJsonElement())
    }

    @TypeConverter
    fun stringToListParameters(string: String?): List<Parameter>? {
        val value = Json.decodeFromString<JsonArray>(string ?: return emptyList())
        return value.map {
            Parameter(
                it.jsonObjectOrNull?.get("schema")?.jsonPrimitive?.contentOrNull,
                it.jsonObjectOrNull?.entries?.associate { it.key to it.value.toPrimitive() } ?: emptyMap()
            )
        }
    }

    @TypeConverter
    fun listParametersToString(example: List<Parameter>?): String? {
        return Json.encodeToString(example.toJsonElement())
    }
}
