package com.ottogroup.appkit.tracking.userproperty

import com.ottogroup.appkit.tracking.userproperty.UserProperty.UserPropertyKey.DEVICE_ORIENTATION
import com.ottogroup.appkit.tracking.userproperty.UserProperty.UserPropertyKey.FONT_SIZE_ZOOM
import com.ottogroup.appkit.tracking.userproperty.UserProperty.UserPropertyKey.MOTION_SENSITIVITY
import com.ottogroup.appkit.tracking.userproperty.UserProperty.UserPropertyKey.PUSH_NOTIFICATION_OPT_IN
import com.ottogroup.appkit.tracking.userproperty.UserProperty.UserPropertyKey.SCREEN_READER
import kotlinx.serialization.Serializable

@Serializable
public sealed class UserProperty {
    public abstract val key: String
    public abstract val value: String

    public data class PushNotificationOptInUserProperty(private val optIn: Boolean) :
        UserProperty() {
        override val key: String = PUSH_NOTIFICATION_OPT_IN
        override val value: String = optIn.toString()
    }

    public sealed class DeviceOrientationUserProperty {
        public data object Landscape : UserProperty() {
            override val key: String = DEVICE_ORIENTATION
            override val value: String = "landscape"
        }

        public data object Portrait : UserProperty() {
            override val key: String = DEVICE_ORIENTATION
            override val value: String = "portrait"
        }
    }

    public data class FontSizeZoomUserProperty(private val zoomLevel: Float) : UserProperty() {
        override val key: String = FONT_SIZE_ZOOM
        override val value: String = zoomLevel.toString()
    }

    public data class ScreenReaderUserProperty(private val isActive: Boolean) : UserProperty() {
        override val key: String = SCREEN_READER
        override val value: String = isActive.toString()
    }

    public data class MotionSensitivityUserProperty(private val isActive: Boolean) :
        UserProperty() {
        override val key: String = MOTION_SENSITIVITY
        override val value: String = isActive.toString()
    }

    private object UserPropertyKey {
        const val PUSH_NOTIFICATION_OPT_IN = "push_notification_opt_in"
        const val DEVICE_ORIENTATION = "device_orientation"
        const val FONT_SIZE_ZOOM = "font_size_zoom"
        const val SCREEN_READER = "screen_reader"
        const val MOTION_SENSITIVITY = "motion_sensitivity"
    }
}
