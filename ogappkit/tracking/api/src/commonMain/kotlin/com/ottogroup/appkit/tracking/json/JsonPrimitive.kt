package com.ottogroup.appkit.tracking.json

import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.doubleOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.longOrNull

/**
 * Converts a [JsonElement] to a primitive type.
 * with the exception of [JsonNull] which is converted to `null`.
 * and floating point numbers which are converted to [Double].
 * @return the primitive value of the [JsonElement]
 */
internal fun JsonElement.toPrimitive(): Any? {
    return when (this) {
        is JsonNull -> null
        is JsonPrimitive -> {
            when {
                isString -> content
                booleanOrNull != null -> booleanOrNull
                intOrNull != null -> intOrNull
                longOrNull != null -> longOrNull
                doubleOrNull != null -> doubleOrNull
                else -> content
            }
        }
        else -> toString()
    }
}
