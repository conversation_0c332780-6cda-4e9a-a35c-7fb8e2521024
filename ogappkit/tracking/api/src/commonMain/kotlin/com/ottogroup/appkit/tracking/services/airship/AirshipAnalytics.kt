package com.ottogroup.appkit.tracking.services.airship

public interface AirshipAnalytics {
    public fun logEvent(event: AirshipEvent)
    public fun setAnalyticsCollection(enabled: Boolean)
    public fun setDefaultParameters(parameters: Map<String, Any?>)

    /**
     * Provides a key value pair based on a user property.
     * @param key the name of the user property
     * @param value nullable value of the user property
     */
    public fun setUserProperty(key: String, value: String?) {}
}
