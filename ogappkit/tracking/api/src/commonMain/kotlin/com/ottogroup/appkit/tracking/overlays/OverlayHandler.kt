package com.ottogroup.appkit.tracking.overlays

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

public class OverlayHandler(private val coroutineScope: CoroutineScope) {
    private val _events: MutableSharedFlow<OverlayEvent> = MutableSharedFlow()
    internal val event = _events.asSharedFlow()

    public fun onShowOverlay(overlayId: String) {
        coroutineScope.launch {
            _events.emit(OverlayEvent.Show(overlayId))
        }
    }

    public fun onDismissOverlay(overlayId: String) {
        coroutineScope.launch {
            _events.emit(OverlayEvent.Dismiss(overlayId))
        }
    }

    public companion object
}

internal sealed interface OverlayEvent {
    val overlayId: String

    data class Show(override val overlayId: String) : OverlayEvent
    data class Dismiss(override val overlayId: String) : OverlayEvent
}
