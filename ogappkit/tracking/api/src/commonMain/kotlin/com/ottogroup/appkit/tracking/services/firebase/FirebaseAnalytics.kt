package com.ottogroup.appkit.tracking.services.firebase

public interface FirebaseAnalytics {
    /**
     * Parameter values are only of type String, Long, Double, Boolean, or null.
     */
    public fun logEvent(event: FirebaseEvent)
    public fun setAnalyticsCollection(enabled: Boolean)
    public fun setAnalyticsConsent(consentSettings: Map<ConsentType, ConsentStatus>)

    /**
     * Parameter values are only of type String, Long, Double, Boolean, or null.
     */
    public fun setDefaultParameters(parameters: Map<String, Any?>)

    /**
     * Provides a key value pair based on a user property.
     * @param key the name of the user property
     * @param value nullable value of the user property
     */
    public fun setUserProperty(key: String, value: String?)
}

public sealed interface ConsentStatus {
    public data object DENIED : ConsentStatus
    public data object GRANTED : ConsentStatus
}

public enum class ConsentType {
    AD_STORAGE,
    ANALYTICS_STORAGE,
    AD_USER_DATA,
    AD_PERSONALIZATION,
}
