package com.ottogroup.appkit.tracking

import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.base.uri.UrlMatcher
import com.ottogroup.appkit.base.uri.matchesUrl
import com.ottogroup.appkit.base.uri.toUrl
import com.ottogroup.appkit.tracking.config.OGConfigDispatcher
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.View.Screen
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toJsonArray
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceConfig
import com.ottogroup.appkit.tracking.services.adjust.AdjustAnalytics
import com.ottogroup.appkit.tracking.services.adjust.AdjustTrackingService
import com.ottogroup.appkit.tracking.services.airship.AirshipAnalytics
import com.ottogroup.appkit.tracking.services.airship.AirshipTrackingService
import com.ottogroup.appkit.tracking.services.firebase.FirebaseAnalytics
import com.ottogroup.appkit.tracking.services.firebase.FirebaseTrackingService
import com.ottogroup.appkit.tracking.services.snowplow.SnowplowAnalytics
import com.ottogroup.appkit.tracking.services.snowplow.SnowplowTrackingService
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import org.koin.dsl.bind
import org.koin.dsl.module

/**
 * The tracking module is responsible for tracking events and interactions.
 *
 * It is configured with a [OGTrackingConfig] object to map URLs to view events.
 * The module is capable of handling user consents on a per TrackingService basis.
 *
 */
public interface OGTracking {
    /**
     * Configure the tracking module with the given configuration.
     */
    public fun configure(config: OGTrackingConfig)

    /**
     * Call this when a URL has been loaded. It will automatically track a preconfigured view event if it has been configured previously via #OGTracking.configure().
     */
    public fun onDidLoad(url: String)

    /**
     * Track an event.
     * @see [Interaction]
     * @see [View.Screen]
     */
    public fun track(event: OGEvent)

    /**
     * Set the consent for a specific tracking service.
     */
    public fun setConsent(serviceId: OGTrackingServiceId, consent: Boolean)

    /**
     * Set the global consent for all tracking services.
     */
    public fun setGlobalConsent(globalConsent: Boolean)

    /**
     * Provide a [AirshipAnalytics] instance to configure the Airship tracking service.
     */
    public fun configureAirshipAnalytics(airshipAnalytics: AirshipAnalytics)

    /**
     * Provide a [AdjustAnalytics] instance to configure the Adjust tracking service.
     */
    public fun configureAdjustAnalytics(adjustAnalytics: AdjustAnalytics)

    /**
     * Provide a [FirebaseAnalytics] instance to configure the Firebase tracking service.
     */
    public fun configureFirebaseAnalytics(firebaseAnalytics: FirebaseAnalytics)

    /**
     * Provide a [SnowplowAnalytics] instance to configure the Snowplow tracking service.
     */
    public fun configureSnowplow(snowplowAnalytics: SnowplowAnalytics)

    /**
     * Update the configuration for a specific tracking service.
     * @param id The service id of the tracking service.
     */
    public fun onUpdateConfig(id: OGTrackingServiceId, config: ServiceConfig)

    public fun updateGlobalContext(id: OGTrackingServiceId, contextParameters: ContextParameter)

    /**
     * Sets a key-value based UserProperty object.
     * @param userProperty [UserProperty]
     */
    public fun setUserProperty(userProperty: UserProperty)
}

internal class OGTrackingImpl(
    private val consent: OGTrackingConsent,
    private val trackingDealer: OGTrackingDealer,
    private val configDispatcher: OGConfigDispatcher
) : OGTracking, InternalKoinComponent {

    override fun configure(config: OGTrackingConfig) {
        configDispatcher.updateConfig(config)
    }

    override fun onDidLoad(url: String) {
        configDispatcher.configState.value.baseConfig.viewEventMapping.mapToViewEvent(url)
            ?.let { track(it) }
    }

    override fun track(event: OGEvent) {
        trackingDealer.trackEvent(event)
    }

    override fun setConsent(serviceId: OGTrackingServiceId, consent: Boolean) {
        this.consent.setConsentsForServices(mapOf(serviceId to consent))
    }

    override fun setGlobalConsent(globalConsent: Boolean) {
        this.consent.setGlobalConsent(globalConsent)
    }

    override fun configureAdjustAnalytics(
        adjustAnalytics: AdjustAnalytics,
    ) {
        if (getKoin().getOrNull<AdjustTrackingService>() != null) return
        getKoin().loadModules(
            listOf(
                module {
                    single<AdjustAnalytics> { adjustAnalytics }
                    single<AdjustTrackingService>(createdAtStart = true) {
                        AdjustTrackingService(
                            get(),
                            configDispatcher.configForService(OGTrackingServiceId.Adjust),
                            getCoroutineScope(),
                            get()
                        )
                    } bind OGTrackingService::class
                }
            ),
            createEagerInstances = true
        )
    }

    override fun configureAirshipAnalytics(
        airshipAnalytics: AirshipAnalytics,
    ) {
        if (getKoin().getOrNull<AirshipTrackingService>() != null) return
        getKoin().loadModules(
            listOf(
                module {
                    single<AirshipAnalytics> { airshipAnalytics }
                    single<AirshipTrackingService>(createdAtStart = true) {
                        AirshipTrackingService(
                            get(),
                            configDispatcher.configForService(OGTrackingServiceId.Airship),
                            getCoroutineScope(),
                            get()
                        )
                    } bind OGTrackingService::class
                }
            ),
            createEagerInstances = true
        )
    }

    override fun configureSnowplow(
        snowplowAnalytics: SnowplowAnalytics,
    ) {
        if (getKoin().getOrNull<SnowplowTrackingService>() != null) return
        getKoin().loadModules(
            listOf(
                module {
                    single<SnowplowAnalytics> { snowplowAnalytics }
                    single(createdAtStart = true) {
                        SnowplowTrackingService(
                            get(),
                            configDispatcher.configForService(OGTrackingServiceId.Snowplow),
                            getCoroutineScope(),
                            get(),
                        )
                    } bind OGTrackingService::class
                }
            ),
            createEagerInstances = true
        )
    }

    override fun onUpdateConfig(id: OGTrackingServiceId, config: ServiceConfig) {
        configDispatcher.updateConfig(id, config)
    }

    override fun updateGlobalContext(
        id: OGTrackingServiceId,
        contextParameters: ContextParameter
    ) {
        val config = configDispatcher.configState.value
        val parameters = contextParameters.toJsonArray().mapNotNull { it.jsonObjectOrNull }
        val serviceConfig = config.serviceConfigs[id]
        serviceConfig?.let {
            configDispatcher.updateConfig(id, it.baseCopy(globalContext = parameters))
        }
    }

    override fun setUserProperty(userProperty: UserProperty) {
        trackingDealer.setUserProperty(userProperty)
    }

    override fun configureFirebaseAnalytics(
        firebaseAnalytics: FirebaseAnalytics,
    ) {
        if (getKoin().getOrNull<FirebaseTrackingService>() != null) return
        getKoin().loadModules(
            listOf(
                module {
                    single<FirebaseAnalytics> { firebaseAnalytics }
                    single<FirebaseTrackingService>(createdAtStart = true) {
                        FirebaseTrackingService(
                            get(),
                            configDispatcher.configForService(OGTrackingServiceId.Firebase),
                            getCoroutineScope(),
                            get()
                        )
                    } bind OGTrackingService::class
                }
            ),
            createEagerInstances = true
        )
    }
}

public typealias ContextParameter = List<Map<String, Any>>

internal fun Map<Screen, List<UrlMatcher>>.mapToViewEvent(uri: String): Screen? {
    val eventKey: Screen? = firstNotNullOfOrNull {
        val match = it.value.any { it.matchesUrl(uri.toUrl()) }
        if (match) it.key else null
    }
    return eventKey
}
