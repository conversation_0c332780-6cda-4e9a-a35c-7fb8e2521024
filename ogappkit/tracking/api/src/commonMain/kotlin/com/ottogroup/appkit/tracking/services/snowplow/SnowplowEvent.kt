package com.ottogroup.appkit.tracking.services.snowplow

import com.ottogroup.appkit.tracking.services.ServiceEvent

public sealed interface SnowplowEvent : ServiceEvent {
    public data class ScreenView(val name: String, val type: String?) : SnowplowEvent
    public data class SelfDescribing(val schemaUri: String, val data: Map<String, Any?>) :
        SnowplowEvent

    override fun describe(): Map<String, Any?> {
        return when (this) {
            is ScreenView -> mapOf("name" to name, "type" to type)
            is SelfDescribing -> mapOf("schema" to schemaUri, "data" to data)
        }
    }
}
