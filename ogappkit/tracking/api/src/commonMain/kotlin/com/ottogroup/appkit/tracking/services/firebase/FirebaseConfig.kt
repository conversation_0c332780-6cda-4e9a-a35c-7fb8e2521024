package com.ottogroup.appkit.tracking.services.firebase

import com.ottogroup.appkit.tracking.services.ServiceConfig
import kotlinx.serialization.json.JsonObject

public data class FirebaseConfig(
    val deniedConsentTypes: List<ConsentType>,
    override val isEnabled: <PERSON>olean,
    override val globalContext: List<JsonObject> = emptyList()
) : ServiceConfig() {
    internal override fun baseCopy(isEnabled: Boolean, globalContext: List<JsonObject>): ServiceConfig {
        return copy(isEnabled = isEnabled, globalContext = globalContext)
    }
}
