package com.ottogroup.appkit.tracking.services.dynamicyield

import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.PageType
import com.ottogroup.appkit.tracking.event.View.ProductDetailViewItem
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceEvent
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent.PageView.Type
import com.ottogroup.appkit.tracking.services.dynamicyield.api.InternalDynamicYieldNetworkDataSource
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

internal class DynamicYieldTrackingService(
    trackingDealer: OGTrackingDealer,
    configFlow: Flow<DynamicYieldConfig>,
    private val coroutineScope: CoroutineScope,
    private val dataSource: InternalDynamicYieldNetworkDataSource,
    isInitiallyEnabled: Boolean = false,
) : OGTrackingService<DynamicYieldConfig, DynamicYieldEvent>(
    trackingDealer,
    isInitiallyEnabled = isInitiallyEnabled,
    serviceId = OGTrackingServiceId.DynamicYield,
    configFlow = configFlow,
    coroutineScope = coroutineScope
) {
    override val config: StateFlow<DynamicYieldConfig> =
        configFlow.stateIn(coroutineScope, SharingStarted.Eagerly, DynamicYieldConfig(isEnabled = isInitiallyEnabled))

    override fun mapEvent(trackingEvent: OGEvent): DynamicYieldEvent? {
        return when (trackingEvent) {
            is ProductDetailViewItem -> DynamicYieldEvent.from(trackingEvent, config.value.itemIdAlternativeParameter)

            else -> null
        }
    }

    override fun performTrackEvent(trackingEvent: DynamicYieldEvent) {
        coroutineScope.launch {
            when (trackingEvent) {
                is DynamicYieldEvent.PageView -> dataSource.trackPageView(trackingEvent)
            }
        }
    }

    override fun setUserProperty(key: String, value: String?) {}
}

public sealed interface DynamicYieldEvent : ServiceEvent {
    public data class PageView(
        val type: Type,
        val data: List<String>,
        val location: String,
    ) : DynamicYieldEvent {
        public enum class Type {
            HOMEPAGE,
            CATEGORY,
            PRODUCT,
            CART,
            OTHER,
        }

        override fun describe(): Map<String, Any?> {
            return mapOf(
                "type" to type,
                "data" to data,
                "location" to location,
            )
        }
    }

    public companion object
}

public fun DynamicYieldEvent.Companion.from(
    event: ProductDetailViewItem,
    itemIdAlternativeParameter: String? = null
): DynamicYieldEvent.PageView {
    val id = itemIdAlternativeParameter?.let {
        event.item.additionalParameters[it]?.value?.toString()
    } ?: event.item.id
    return DynamicYieldEvent.PageView(
        type = Type.PRODUCT,
        data = listOf(id),
        location = "${PageType.ProductDetail.value}/$id"
    )
}
