package com.ottogroup.appkit.tracking.log

import com.ottogroup.appkit.base.di.InternalKoinContext
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.db.DatabaseHelper

import com.ottogroup.appkit.tracking.db.createDatabase
import com.ottogroup.appkit.tracking.log.db.LogDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import org.koin.dsl.bind
import org.koin.dsl.module

internal val logModule = module {
    single<EventLogger> { EventLoggerImpl(get()) }
    single<EventLogRepository> { EventLogRepoImpl(get()) } bind LogRepository::class
    single<LogDatabase> { get<DatabaseHelper>().createDatabase("LogDatabase.db") }
    single(createdAtStart = true) {
        WriteEventLogsToRepository(
            get(),
            get(),
            getCoroutineScope(Dispatchers.IO)
        )
    }
}

public fun OGTracking.enableDebug(debug: Boolean) {
    if (debug) {
        InternalKoinContext.loadKoinModules(logModule, createEagerInstances = true)
    } else {
        InternalKoinContext.unloadKoinModules(logModule)
    }
}

public fun OGTracking.eventLog(): EventLogRepository {
    return InternalKoinContext.get().get()
}

public fun OGTracking.log(): LogRepository {
    return InternalKoinContext.get().get()
}
