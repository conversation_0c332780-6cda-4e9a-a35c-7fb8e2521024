package com.ottogroup.appkit.tracking.event

import kotlinx.serialization.Serializable

/**
 * Definition of an e-commerce item, as defined by
 * [Google Analytics](https://developers.google.com/analytics/devguides/collection/ga4/reference/events?sjid=4766200166563936318-EU&client_type=gtag#add_to_cart_item).
 *
 * To be used in certain GA-inspired tracking events.
 */
@Serializable
public data class ECommerceItem(
    val name: String,
    val id: String,
    val price: Float? = null,
    val currency: String? = null,
    val coupon: String? = null,
    val discount: Float? = null,
    val affiliation: String? = null,
    val brand: String? = null,
    val category: String? = null,
    val category2: String? = null,
    val category3: String? = null,
    val category4: String? = null,
    val category5: String? = null,
    val variant: String? = null,
    val listName: String? = null,
    val listId: String? = null,
    val locationId: String? = null,
    val index: Int? = null,
    val quantity: Int? = null,

    // custom properties not part of GA definition
    /** Item's product has more than one color. */
    val hasColors: Boolean? = null,
    /** Item's product has more than one size. */
    val hasSizes: Boolean? = null,

    val additionalParameters: Map<String, CustomParameter> = emptyMap(),
)
