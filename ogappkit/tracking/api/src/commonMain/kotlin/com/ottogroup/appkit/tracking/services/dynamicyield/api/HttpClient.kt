package com.ottogroup.appkit.tracking.services.dynamicyield.api

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.base.lenientJson
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.ExperimentalSerializationApi
import io.ktor.client.plugins.logging.Logger as KtorLogger

@OptIn(ExperimentalSerializationApi::class)
/**
 * Provides a ktor HttpClient and allows overriding the engine for testing
 * purposes.
 */
internal fun dynamicYieldHttpClient(customEngine: HttpClientEngine? = null): HttpClient {
    val config: HttpClientConfig<*>.() -> Unit = {
        install(Logging) {
            logger = object : KtorLogger {
                override fun log(message: String) {
                    Logger.d(DY_TAG) { message }
                }
            }
            level = LogLevel.INFO
        }
        install(ContentNegotiation) {
            json(lenientJson)
        }
    }

    return if (customEngine != null) {
        HttpClient(customEngine, config)
    } else {
        // let ktor pick the right engine for the platform
        HttpClient(config)
    }
}
