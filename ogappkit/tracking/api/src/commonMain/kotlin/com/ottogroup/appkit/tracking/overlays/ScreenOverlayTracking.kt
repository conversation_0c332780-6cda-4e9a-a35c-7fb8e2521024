package com.ottogroup.appkit.tracking.overlays

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.event.View
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * Tracks the screen events and overlays to retrigger the screen events after overlay dismiss.
 */
internal class ScreenOverlayTracking(
    overlayHandler: OverlayHandler,
    enabledFlow: Flow<Boolean>,
    private val trackingDealer: OGTrackingDealer,
    private val overlayObserver: OverlayObserver?,
    private val coroutineScope: CoroutineScope
) {
    private val enabledState = enabledFlow.stateIn(coroutineScope, SharingStarted.Eagerly, false)
    private val overlayStack: MutableList<OverlayStackEntry> = mutableListOf()
    private var currentOverlay: String? = null
    private var retriggerJob: Job? = null

    init {
        overlayHandler.event.onEach {
            when (it) {
                is OverlayEvent.Dismiss -> retriggerScreenAfterOverlay(it.overlayId)
                is OverlayEvent.Show -> setCurrentOverlay(it.overlayId)
            }
        }.launchIn(coroutineScope)

        trackingDealer.events.filterIsInstance<View.Screen>()
            .onEach { insertScreenEventIntoOverlayStack(it) }
            .launchIn(coroutineScope)

        enabledState.onEach {
            if (it) {
                overlayObserver?.startObserving()
            } else {
                overlayObserver?.stopObserving()
            }
        }.launchIn(coroutineScope)
    }

    private fun insertScreenEventIntoOverlayStack(screenEvent: View.Screen) {
        if (!enabledState.value) return
        retriggerJob?.cancel("new screen event, cancel current retrigger job")
        val lastRecentOverlayEntry = overlayStack.lastOrNull()
        when {
            overlayStack.size == 1 && currentOverlay == null -> {
                overlayStack[0] = OverlayStackEntry(null, screenEvent)
                Logger.d("set root event $screenEvent")
            }

            lastRecentOverlayEntry?.event != screenEvent ||
                lastRecentOverlayEntry.overlayId != currentOverlay -> {
                overlayStack.add(OverlayStackEntry(currentOverlay, screenEvent))
                Logger.d("added $screenEvent to stack: + stack: $overlayStack")
            }

            else -> {
                Logger.d("ignore $screenEvent as equal with last event")
            }
        }
    }

    private fun setCurrentOverlay(overlayId: String) {
        if (!enabledState.value) return
        currentOverlay = overlayId
        Logger.d("set $overlayId as current overlay: $overlayStack")
    }

    private fun retriggerScreenAfterOverlay(overlayId: String) {
        if (!enabledState.value) return
        val (dismissedItems, newStack) = overlayStack.partition { it.overlayId == overlayId }
        overlayStack.clear()
        overlayStack.addAll(newStack)

        if (dismissedItems.isEmpty()) return
        overlayStack.lastOrNull()?.let {
            Logger.d("retrigger last event ($it) before $overlayId")
            currentOverlay = it.overlayId

            retriggerJob = coroutineScope.launch {
                delay(1000)
                retriggerEvent(it.event)
                retriggerJob = null
            }
        }
    }

    private fun retriggerEvent(event: View.Screen) {
        if (!enabledState.value) return
        trackingDealer.trackEvent(event)
    }

    data class OverlayStackEntry(
        val overlayId: String?,
        val event: View.Screen
    )
}
