package com.ottogroup.appkit.tracking.log.db

import EventDao
import LogDao
import NetDao
import SysDao
import androidx.room.ConstructedBy
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.RoomDatabaseConstructor
import androidx.room.TypeConverters
import com.ottogroup.appkit.tracking.log.db.converter.TypeConverter
import com.ottogroup.appkit.tracking.log.db.schema.EventEntity
import com.ottogroup.appkit.tracking.log.db.schema.EventToTrackingServiceMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToEventMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToNetMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToSysMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.NetDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.ServiceEventEntity
import com.ottogroup.appkit.tracking.log.db.schema.SysDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceToServiceEventMappingEntity

@Database(
    entities = [
        LogEntity::class,
        EventEntity::class,
        LogToEventMappingEntity::class,
        EventToTrackingServiceMappingEntity::class,
        ServiceEventEntity::class,
        TrackingServiceEntity::class,
        TrackingServiceToServiceEventMappingEntity::class,
        NetDataEntity::class,
        LogToNetMappingEntity::class,
        SysDataEntity::class,
        LogToSysMappingEntity::class
    ],
    version = 1
)
@TypeConverters(TypeConverter::class)
@ConstructedBy(LogDatabaseConstructor::class)
internal abstract class LogDatabase : RoomDatabase() {
    internal abstract fun logDao(): LogDao
    internal abstract fun eventDao(): EventDao
    internal abstract fun netDao(): NetDao
    internal abstract fun sysDao(): SysDao
}

// The Room compiler generates the `actual` implementations.
@Suppress("NO_ACTUAL_FOR_EXPECT", "EXPECT_ACTUAL_CLASSIFIERS_ARE_IN_BETA_WARNING")
internal expect object LogDatabaseConstructor : RoomDatabaseConstructor<LogDatabase> {
    override fun initialize(): LogDatabase
}
