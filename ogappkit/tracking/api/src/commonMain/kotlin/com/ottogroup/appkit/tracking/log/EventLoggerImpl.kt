package com.ottogroup.appkit.tracking.log

import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.base.di.InternalKoinContext
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.collectEvents
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toPrimitive
import com.ottogroup.appkit.tracking.log.domain.Event
import com.ottogroup.appkit.tracking.log.domain.EventLog
import com.ottogroup.appkit.tracking.log.domain.LogEntry
import com.ottogroup.appkit.tracking.log.domain.Parameter
import com.ottogroup.appkit.tracking.log.domain.ServiceEvent
import com.ottogroup.appkit.tracking.log.domain.TrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.ServiceEvent as OGServiceEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.datetime.Clock
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonPrimitive

internal class EventLoggerImpl(
    private val trackingDealer: OGTrackingDealer,
    private val clock: Clock = Clock.System
) : EventLogger, InternalKoinComponent {
    private val _events = MutableSharedFlow<EventLog>()
    override val events = _events.asSharedFlow()

    private val trackingServices: List<OGTrackingService<*, *>>
        get() = InternalKoinContext.get().getAll()

    init {
        trackingDealer.collectEvents {
            _events.emit(
                EventLog(
                    message = "Tracking event Received: $it",
                    event = it.toEntity(),
                    timestamp = clock.now().toEpochMilliseconds(),
                    trackingServices = trackingServices.map { service ->
                        TrackingService(
                            serviceName = service.serviceId.name,
                            isEnabled = service.isEnabled,
                            contextParameter = service.config.value.globalContext.toParameters(),
                            serviceEvent = service.mapEvent(it)?.toEntity()
                        )
                    }.toCollection(ArrayList())
                )
            )
        }
    }
}

private fun List<JsonObject>.toParameters(): List<Parameter> {
    return mapNotNull {
        try {
            val schema = it.get("schema")?.jsonPrimitive?.content
            val data =
                it.get("data")?.jsonObjectOrNull?.entries?.associate { it.key to it.value.toPrimitive() }
                    ?: emptyMap()
            Parameter(schema, HashMap(data))
        } catch (iae: IllegalArgumentException) {
            null
        }
    }
}

public interface EventLogger {
    public val events: Flow<LogEntry>
}

internal fun OGServiceEvent.toEntity(): ServiceEvent {
    val content = describe()
    return ServiceEvent(
        (content["name"] as? String) ?: this::class.simpleName ?: "",
        HashMap(content)
    )
}

internal fun OGEvent.toEntity(): Event {
    return when (this) {
        is Interaction -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "category" to category,
                "scenario" to scenario,
                "detail" to detail,
                "label" to label,
            )
        )

        is View.Overlay -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "category" to category,
                "scenario" to scenario,
                "detail" to detail,
            )
        )

        is View.Screen -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "pageType" to pageType,
                "pageTitle" to pageTitle,
            )
        )

        is Interaction.Purchase -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "currency" to currency,
                "value" to value,
                "additionalParams" to additionalParams,
            )
        )

        is Interaction.Search.QuerySubmit -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "searchTerm" to searchTerm,
                "suggestionType" to suggestionType,
            )
        )

        is Interaction.Search -> Event(
            this::class.simpleName ?: "",
            hashMapOf(
                "suggestionType" to suggestionType,
            )
        )

        else -> Event(
            this::class.simpleName ?: "",
            hashMapOf()
        )
    }
}
