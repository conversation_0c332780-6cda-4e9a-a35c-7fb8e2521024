package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity(
    indices = [Index(value = ["serviceName"])]
)
internal data class TrackingServiceEntity(
    @PrimaryKey(autoGenerate = true)
    val serviceId: Long = 0,
    val serviceName: String,
    val isEnabled: Boolean,
    val contextParameter: List<Parameter>,
)

internal data class Parameter(
    val schema: String?,
    val data: Map<String, Any?>
)

@Entity(
    primaryKeys = ["serviceIdRef", "serviceEventIdRef"],
    foreignKeys = [
        ForeignKey(
            entity = TrackingServiceEntity::class,
            parentColumns = ["serviceId"],
            childColumns = ["serviceIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        ),
        ForeignKey(
            entity = ServiceEventEntity::class,
            parentColumns = ["serviceEventId"],
            childColumns = ["serviceEventIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        )
    ],
    indices = [Index(value = ["serviceIdRef"]), Index(value = ["serviceEventIdRef"])]
)
internal data class TrackingServiceToServiceEventMappingEntity(
    val serviceIdRef: Long,
    val serviceEventIdRef: Long
)

internal data class TrackingServiceWithServiceEvent(
    @Embedded
    val service: TrackingServiceEntity,
    @Relation(
        entity = ServiceEventEntity::class,
        parentColumn = "serviceId",
        entityColumn = "serviceEventId",
        associateBy = Junction(
            value = TrackingServiceToServiceEventMappingEntity::class,
            parentColumn = "serviceIdRef",
            entityColumn = "serviceEventIdRef"
        )
    )
    val serviceEvent: ServiceEventEntity
)
