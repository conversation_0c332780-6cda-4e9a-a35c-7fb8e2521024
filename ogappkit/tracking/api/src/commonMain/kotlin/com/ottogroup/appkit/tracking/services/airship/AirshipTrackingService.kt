package com.ottogroup.appkit.tracking.services.airship

import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toPrimitive
import com.ottogroup.appkit.tracking.services.ServiceEventWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.JsonObject

internal class AirshipTrackingService(
    trackingDealer: OGTrackingDealer,
    configFlow: Flow<AirshipConfig>,
    coroutineScope: CoroutineScope,
    private val airshipAnalytics: AirshipAnalytics,
    isInitiallyEnabled: Boolean = false,
) : OGTrackingService<AirshipConfig, AirshipEvent>(
    trackingDealer,
    isInitiallyEnabled = isInitiallyEnabled,
    serviceId = OGTrackingServiceId.Airship,
    configFlow = configFlow,
    coroutineScope = coroutineScope
) {
    override val config: StateFlow<AirshipConfig> =
        configFlow.stateIn(coroutineScope, SharingStarted.Eagerly, AirshipConfig(isInitiallyEnabled))

    override fun onIsEnabledChanged(enabled: Boolean) {
        airshipAnalytics.setAnalyticsCollection(enabled)
        super.onIsEnabledChanged(enabled)
    }

    override fun mapEvent(trackingEvent: OGEvent): AirshipEvent = ServiceEventWrapper(trackingEvent)

    override fun performTrackEvent(trackingEvent: AirshipEvent) {
        airshipAnalytics.logEvent(trackingEvent)
    }

    override fun setDefaultParameters(parameters: List<JsonObject>) {
        val actualParameters =
            parameters.mapNotNull { it["data"]?.jsonObjectOrNull }
                .flatMap { it.entries }
                .associate { it.key to it.value.toPrimitive().takeIf { it != "null" } }
        airshipAnalytics.setDefaultParameters(actualParameters)
    }

    override fun setUserProperty(key: String, value: String?) {
        airshipAnalytics.setUserProperty(key, value)
    }
}

public typealias AirshipEvent = ServiceEventWrapper
