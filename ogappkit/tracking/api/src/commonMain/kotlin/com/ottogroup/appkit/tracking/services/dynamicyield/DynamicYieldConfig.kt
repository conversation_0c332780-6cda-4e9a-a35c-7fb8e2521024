package com.ottogroup.appkit.tracking.services.dynamicyield

import com.ottogroup.appkit.base.http.CookiesBridge
import com.ottogroup.appkit.base.http.NoOpCookiesBridge
import com.ottogroup.appkit.tracking.services.ServiceConfig
import kotlinx.serialization.json.JsonObject

public data class DynamicYieldConfig(
    val trackPageViewUrl: String = "",
    val apiKey: String = "",
    val cookiesDomain: String = "",
    val cookiesBridge: CookiesBridge = NoOpCookiesBridge,
    val itemIdAlternativeParameter: String? = null,
    override val isEnabled: Boolean,
    override val globalContext: List<JsonObject> = emptyList()
) : ServiceConfig() {

    override fun baseCopy(
        isEnabled: Boolean,
        globalContext: List<JsonObject>
    ): ServiceConfig {
        return copy(isEnabled = isEnabled, globalContext = globalContext)
    }
}
