package com.ottogroup.appkit.tracking.services.adjust

import com.ottogroup.appkit.tracking.services.ServiceConfig
import kotlinx.serialization.json.JsonObject

public data class AdjustConfig(
    override val isEnabled: Boolean,
    val eventTokenMapping: Map<String, String> = emptyMap(),
    override val globalContext: List<JsonObject> = emptyList()
) : ServiceConfig() {
    internal override fun baseCopy(isEnabled: Boolean, globalContext: List<JsonObject>): ServiceConfig {
        return copy(isEnabled = isEnabled, globalContext = globalContext)
    }
}
