package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity(
    indices = [Index(value = ["name"])]
)
internal data class EventEntity(
    @PrimaryKey(autoGenerate = true)
    val eventId: Long = 0,
    val name: String,
    val properties: Map<String, Any?>
)

@Entity(
    primaryKeys = ["logIdRef", "eventIdRef"],
    foreignKeys = [
        ForeignKey(
            entity = LogEntity::class,
            parentColumns = ["logId"],
            childColumns = ["logIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        ),
        ForeignKey(
            entity = EventEntity::class,
            parentColumns = ["eventId"],
            childColumns = ["eventIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        )
    ],
    indices = [Index(value = ["logIdRef"]), Index(value = ["eventIdRef"])]
)
internal data class LogToEventMappingEntity(
    val logIdRef: Long,
    val eventIdRef: Long
)

internal data class LogWithEvent(
    @Embedded
    val logEntity: LogEntity,
    @Relation(
        entity = EventEntity::class,
        parentColumn = "logId",
        entityColumn = "eventId",
        associateBy = Junction(
            value = LogToEventMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "eventIdRef"
        )
    )
    val eventEntity: EventEntity
)

internal data class LogWithEventAndServices(
    @Embedded
    val logEntity: LogEntity,
    @Relation(
        entity = EventEntity::class,
        parentColumn = "logId",
        entityColumn = "eventId",
        associateBy = Junction(
            value = LogToEventMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "eventIdRef"
        )
    )
    val eventEntity: EventWithTrackingServices
)
