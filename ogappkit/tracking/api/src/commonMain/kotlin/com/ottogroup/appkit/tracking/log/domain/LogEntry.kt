package com.ottogroup.appkit.tracking.log.domain

public sealed interface LogEntry {
    public val message: String
    public val timestamp: Long
}

public data class EventLog(
    override val timestamp: Long,
    override val message: String,
    val event: Event,
    val trackingServices: List<TrackingService>
) : LogEntry

public data class NetLog(
    override val timestamp: Long,
    override val message: String,
    val method: String,
    val url: String,
    val httpStatus: Int,
    val responseTime: Int = 650,
    val requestHeader: String,
    val requestBody: String,
    val responseHeader: String,
    val responseBody: String,
    val responseSize: Long,
) : LogEntry

public data class SysLog(
    override val timestamp: Long,
    override val message: String,
    val priority: Int,
    val tag: String?,
    val stacktrace: String?,
    val fileName: String?,
    val methodName: String?,
    val lineNumber: Int?,
) : LogEntry

public data class CustomLog(
    override val timestamp: Long,
    override val message: String,
    val type: String,
    val data: Map<String, Any?>
) : LogEntry

public data class BaseLog(
    override val timestamp: Long,
    override val message: String
) : LogEntry
