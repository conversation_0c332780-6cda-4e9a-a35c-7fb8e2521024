package com.ottogroup.appkit.tracking.event

import kotlinx.serialization.Serializable

/**
 * The base interface representing a tracking event that can be tracked by the [OGTrackingDealer].
 * All tracking events implement this interface.
 */
@Serializable
public sealed interface OGEvent {
    public fun describe(): Map<String, Any?> = mapOf("name" to this::class.simpleName)
}

public enum class PageType(public val value: String) {
    Account("Account"),
    CountryOverview("CountryOverview"),
    BraFittingGuide("BraFittingGuide"),
    CatalogScanner("CatalogScanner"),
    CategoryOverview("CategoryOverview"),
    Deals("Deals"),
    Error("Error"),
    Inbox("Inbox"),
    Search("Search"),
    StoreLocator("StoreLocator"),
    Onboarding("Onboarding"),
    Cart("Cart"),
    Checkout("Checkout"),
    Homepage("Homepage"),
    Login("Login"),
    OrderConfirmation("OrderConfirmation"),
    ProductDetail("ProductDetail"),
    ProductListing("ProductListing"),
    SearchResult("SearchResult"),
    CustomerRegistration("CustomerRegistration"),
    Wishlist("Wishlist")
}
