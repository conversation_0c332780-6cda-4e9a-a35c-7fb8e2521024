package com.ottogroup.appkit.tracking

import com.ottogroup.appkit.base.datastore.jsonDataStore
import com.ottogroup.appkit.base.di.InternalKoinContext
import com.ottogroup.appkit.base.di.getAllDistinct
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.tracking.config.OGConfigDispatcher
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentImpl
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentLocalDataSource
import com.ottogroup.appkit.tracking.consent.OGTrackingConsentState
import com.ottogroup.appkit.tracking.log.ConfigureLogging
import com.ottogroup.appkit.tracking.overlays.OverlayHandler
import com.ottogroup.appkit.tracking.overlays.ScreenOverlayTracking
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldTrackingService
import com.ottogroup.appkit.tracking.services.dynamicyield.api.DynamicYieldNetworkDataSource
import com.ottogroup.appkit.tracking.services.dynamicyield.api.DynamicYieldNetworkDataSourceImpl
import com.ottogroup.appkit.tracking.services.dynamicyield.api.InternalDynamicYieldNetworkDataSource
import kotlinx.coroutines.flow.map
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.module

public val trackingModule: Module = module {
    val defaultConfig = OGTrackingConfig(
        trackingEnabled = false,
        logLevel = LogLevel.Assert,
        viewEventMapping = emptyMap(),
        observeOverlaysForScreenEvents = true
    )
    single<OGTrackingConsent> { OGTrackingConsentImpl(getCoroutineScope(), get()) }
    single<OGTrackingDealer> {
        OGTrackingDealer(
            get<OGConfigDispatcher>().configState.map { it.baseConfig.trackingEnabled },
            getAllDistinct(),
            get(),
            getCoroutineScope()
        )
    }
    single<OGTracking> { OGTrackingImpl(get(), get(), get()) }
    single<OGConfigDispatcher> {
        OGConfigDispatcher(
            defaultConfig
        )
    }
    single<OGTrackingConsentLocalDataSource> {
        OGTrackingConsentLocalDataSource(
            jsonDataStore(
                default = OGTrackingConsentState(),
                fileName = "tracking_consent.json",
                fileSystem = get()
            )
        )
    }

    single(createdAtStart = true) {
        ConfigureLogging(
            getCoroutineScope(),
            get<OGConfigDispatcher>().configState.map { it.baseConfig.logLevel },
        )
    }
    single { OverlayHandler(getCoroutineScope()) }
    single(createdAtStart = true) {
        ScreenOverlayTracking(
            get(),
            get<OGConfigDispatcher>().configState.map { it.baseConfig.observeOverlaysForScreenEvents },
            get(),
            getOrNull(),
            getCoroutineScope()
        )
    }

    single<InternalDynamicYieldNetworkDataSource> {
        DynamicYieldNetworkDataSourceImpl(
            get<OGConfigDispatcher>().configForService(OGTrackingServiceId.DynamicYield),
        )
    } bind DynamicYieldNetworkDataSource::class
    single<DynamicYieldTrackingService>(createdAtStart = true) {
        DynamicYieldTrackingService(
            get(),
            get<OGConfigDispatcher>().configForService(OGTrackingServiceId.DynamicYield),
            getCoroutineScope(),
            get()
        )
    } bind OGTrackingService::class
}

public fun OverlayHandler.Companion.shared(): OverlayHandler {
    return InternalKoinContext.get().get()
}
