package com.ottogroup.appkit.tracking.services.adjust

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.event.GenericEvent
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toPrimitive
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.properties.Properties
import kotlinx.serialization.properties.encodeToStringMap

internal class AdjustTrackingService(
    trackingDealer: OGTrackingDealer,
    configFlow: Flow<AdjustConfig>,
    coroutineScope: CoroutineScope,
    private val adjustAnalytics: AdjustAnalytics,
    isInitiallyEnabled: Boolean = false,
) : OGTrackingService<AdjustConfig, AdjustEvent>(
    trackingDealer,
    isInitiallyEnabled = isInitiallyEnabled,
    serviceId = OGTrackingServiceId.Adjust,
    configFlow = configFlow,
    coroutineScope = coroutineScope
) {

    override val config: StateFlow<AdjustConfig> =
        configFlow.stateIn(coroutineScope, SharingStarted.Eagerly, AdjustConfig(isInitiallyEnabled))

    override fun evaluateEnabledState(
        consent: Boolean,
        config: AdjustConfig
    ): Boolean {
        adjustAnalytics.setThirdPartySharingEnabled(consent)
        return config.isEnabled
    }

    override fun onIsEnabledChanged(enabled: Boolean) {
        adjustAnalytics.setEnabled(enabled)
        super.onIsEnabledChanged(enabled)
    }

    @OptIn(ExperimentalSerializationApi::class)
    override fun mapEvent(trackingEvent: OGEvent): AdjustEvent? {
        val token = findToken(trackingEvent) ?: return null

        val revenue = when (trackingEvent) {
            is Interaction.Purchase -> AdjustEvent.Revenue(
                amount = trackingEvent.value.toDouble(),
                currency = trackingEvent.currency,
            )

            else -> null
        }

        /* Get event properties, resolving nested objects into dot.notation top level keys. Remove the default property
         * "type" that is created by kotlinx.serialization for polymorphic types.
         * Because kotlinx.serialization does not serialize super type properties for objects (which most of our events
         * are), merge in the description of the event to get all properties. This might create a duplicate of the
         * properties, but the description will take precedence.
         * E.g. an event like `Event(val item: ECommerceItem)` will yield the parameters
         * `item = ECommerceItem(..)`, `item.name = "itemName"`, `item.price = 1.99`, etc.
         */
        val parameters = Properties.encodeToStringMap(trackingEvent).filterKeys { it != "type" } +
            trackingEvent.describe().filterValues { it != null }.mapValues { it.value.toString() }

        return AdjustEvent(
            token = token,
            revenue = revenue,
            partnerParameters = parameters,
        )
    }

    private fun findToken(trackingEvent: OGEvent): String? {
        val eventName = trackingEvent::class.qualifiedName?.removeUnsafeCharacters()
            .extendForGenericEvent(trackingEvent) ?: run {
            Logger.w("Could not get qualified name for tracking event $trackingEvent. No token mapping possible.")
            return null
        }

        val tokenCandidates = config.value.eventTokenMapping.entries.filter { (key, value) ->
            eventName.contains(key.removeUnsafeCharacters(), ignoreCase = true)
        }
        if (tokenCandidates.size > 1) {
            Logger.w("Found multiple possible token mappings for event $trackingEvent: ${tokenCandidates.joinToString()}")
            return null
        }
        return tokenCandidates.singleOrNull()?.value
    }

    override fun performTrackEvent(trackingEvent: AdjustEvent) {
        adjustAnalytics.logEvent(trackingEvent)
    }

    override fun setDefaultParameters(parameters: List<JsonObject>) {
        val globalParameters: Map<String, String> =
            parameters.mapNotNull { it["data"]?.jsonObjectOrNull }
                .flatMap { it.entries }
                .associate { it.key to it.value.toPrimitive().toString().takeIf { it != "null" } }
                .filterValues { it != null }
                .mapValues { it.value.orEmpty() }

        adjustAnalytics.clearAllGlobalPartnerParameters()
        globalParameters.forEach {
            adjustAnalytics.addGlobalPartnerParameter(it.key, it.value)
        }
    }

    override fun setUserProperty(key: String, value: String?) {
        adjustAnalytics.setUserProperty(key, value)
    }

    private fun String?.extendForGenericEvent(trackingEvent: OGEvent): String? {
        return when (trackingEvent) {
            is GenericEvent -> {
                // assuming that generic event names could contain underscores,
                // e.g. app_start, webview_error
                // TODO check if this works for iOS as well
                this.plus(trackingEvent.name.removeUnsafeCharacters())
            }

            else -> this
        }
    }
}

public data class AdjustEvent(
    val token: String,
    val revenue: Revenue? = null,
    val partnerParameters: Map<String, String> = emptyMap(),
) : ServiceEvent {

    public data class Revenue(
        val amount: Double,
        val currency: String,
    )

    override fun describe(): Map<String, Any?> = mapOf("token" to token) + partnerParameters
}

private val unsafeCharacters = Regex("[^a-zA-Z0-9]")
private fun String.removeUnsafeCharacters(): String = replace(unsafeCharacters, "")
