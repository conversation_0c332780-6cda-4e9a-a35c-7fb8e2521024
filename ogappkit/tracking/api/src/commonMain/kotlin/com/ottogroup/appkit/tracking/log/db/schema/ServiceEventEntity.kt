package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity(
    indices = [Index(value = ["name"])]
)
internal data class ServiceEventEntity(
    @PrimaryKey(autoGenerate = true)
    val serviceEventId: Long = 0,
    val name: String,
    val properties: Map<String, Any?>
)

@Entity(
    primaryKeys = ["eventIdRef", "serviceIdRef"],
    foreignKeys = [
        ForeignKey(
            entity = EventEntity::class,
            parentColumns = ["eventId"],
            childColumns = ["eventIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        ),
        ForeignKey(
            entity = TrackingServiceEntity::class,
            parentColumns = ["serviceId"],
            childColumns = ["serviceIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        )
    ],
    indices = [Index(value = ["eventIdRef"]), Index(value = ["serviceIdRef"])]

)
internal data class EventToTrackingServiceMappingEntity(
    val eventIdRef: Long,
    val serviceIdRef: Long
)

internal data class EventWithTrackingServices(
    @Embedded
    val event: EventEntity,
    @Relation(
        entity = TrackingServiceEntity::class,
        parentColumn = "eventId",
        entityColumn = "serviceId",
        associateBy = Junction(
            value = EventToTrackingServiceMappingEntity::class,
            parentColumn = "eventIdRef",
            entityColumn = "serviceIdRef"
        )
    )
    val trackingServices: List<TrackingServiceWithServiceEvent>
)
