package com.ottogroup.appkit.tracking.config

import com.ottogroup.appkit.tracking.OGTrackingConfig
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceConfig
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map

internal class OGConfigDispatcher(default: OGTrackingConfig) {
    private val _configState = MutableStateFlow(OGConfig(default, emptyMap()))
    val configState: StateFlow<OGConfig> = _configState.asStateFlow()

    fun <T : ServiceConfig> configForService(serviceId: OGTrackingServiceId): Flow<T> {
        return _configState.map { it.serviceConfigs[serviceId] as? T }.filterNotNull()
            .distinctUntilChanged()
    }

    fun updateConfig(newConfig: OGTrackingConfig) {
        _configState.value = _configState.value.copy(baseConfig = newConfig)
    }

    fun updateConfig(serviceId: OGTrackingServiceId, config: ServiceConfig) {
        val ogConfig = _configState.value
        _configState.value =
            ogConfig.copy(serviceConfigs = ogConfig.serviceConfigs + (serviceId to config))
    }

    data class OGConfig(
        val baseConfig: OGTrackingConfig,
        val serviceConfigs: Map<OGTrackingServiceId, ServiceConfig>
    )
}
