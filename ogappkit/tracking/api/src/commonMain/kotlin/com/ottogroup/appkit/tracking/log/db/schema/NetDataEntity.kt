package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity()
public data class NetDataEntity(
    @PrimaryKey(autoGenerate = true)
    val netId: Long = 0,
    val method: String,
    val url: String,
    val httpStatus: Int,
    val responseTime: Int,
    val requestHeader: String,
    val requestBody: String,
    val responseHeader: String,
    val responseBody: String,
    val responseSize: Long,
)

@Entity(
    primaryKeys = ["logIdRef", "netIdRef"],
    foreignKeys = [
        ForeignKey(
            entity = NetDataEntity::class,
            parentColumns = ["netId"],
            childColumns = ["netIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        ),
        ForeignKey(
            entity = LogEntity::class,
            parentColumns = ["logId"],
            childColumns = ["logIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        )
    ],
    indices = [Index(value = ["logIdRef"]), Index(value = ["netIdRef"])]
)
internal data class LogToNetMappingEntity(
    val logIdRef: Long,
    val netIdRef: Long
)

internal data class LogWithNet(
    @Embedded
    val logEntity: LogEntity,
    @Relation(
        entity = EventEntity::class,
        parentColumn = "logId",
        entityColumn = "netId",
        associateBy = Junction(
            value = LogToNetMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "netIdRef"
        )
    )
    val eventEntity: NetDataEntity
)
