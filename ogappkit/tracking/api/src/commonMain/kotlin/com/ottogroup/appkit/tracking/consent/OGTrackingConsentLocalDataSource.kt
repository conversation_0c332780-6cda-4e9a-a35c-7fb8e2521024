package com.ottogroup.appkit.tracking.consent

import kotlinx.coroutines.flow.debounce
import androidx.datastore.core.DataStore
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.FlowPreview

internal class OGTrackingConsentLocalDataSource(
    private val dataStore: DataStore<OGTrackingConsentState>
) {
    @OptIn(FlowPreview::class)
    val trackingConsentState = dataStore.data.debounce(100)

    suspend fun setGlobalConsent(consent: Boolean) {
        dataStore.updateData {
            it.copy(globalConsent = consent)
        }
    }

    suspend fun setConsentsForServices(
        consents: Map<OGTrackingServiceId, Boolean>,
        replacePrevious: Boolean = false
    ) {
        dataStore.updateData {
            val newConsents = if (replacePrevious) consents else it.serviceConsents + consents
            it.copy(serviceConsents = newConsents)
        }
    }
}
