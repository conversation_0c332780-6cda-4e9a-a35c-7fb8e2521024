package com.ottogroup.appkit.tracking

import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import com.ottogroup.appkit.tracking.userproperty.UserPropertyContributor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.onSubscription

public class OGTrackingDealer(
    enabledFlow: Flow<Boolean>,
    userPropertyContributors: List<UserPropertyContributor>,
    public val consent: OGTrackingConsent,
    public val coroutineScope: CoroutineScope
) {

    private val enabledState = enabledFlow.stateIn(coroutineScope, SharingStarted.Eagerly, false)
    private val _events: MutableSharedFlow<OGEvent> = MutableSharedFlow()
    internal val userPropertyCache: MutableStateFlow<Map<String, UserProperty>> =
        MutableStateFlow(emptyMap())
    private val _userProperty: MutableSharedFlow<UserProperty> = MutableSharedFlow()

    /**
     * A flow of all tracking events as they are tracked.
     */
    public val events: Flow<OGEvent> = _events.asSharedFlow()

    public val userProperty: Flow<UserProperty> = _userProperty.asSharedFlow()
        .onSubscription {
            // Emit all current cached values first when a new subscriber joins
            userPropertyCache.value.values.forEach { emit(it) }
        }

    init {
        userPropertyContributors.forEach { contributor ->
            contributor.userProperty
                .combine(enabledState) { userProp, _ ->
                    setUserProperty(userProp)
                }
                .launchIn(coroutineScope)
        }
    }

    /**
     * Tracks a [trackingEvent] by emitting it to the [events] flow.
     */
    public fun trackEvent(trackingEvent: OGEvent) {
        if (!enabledState.value) return
        coroutineScope.launch {
            _events.emit(trackingEvent)
        }
    }

    /**
     * Caches a user property by its key, always storing the latest value.
     * Returns true if the property was actually changed (new or different value).
     */
    private fun cacheUserProperty(userProperty: UserProperty): Boolean {
        val currentProperties = userPropertyCache.value
        val existingProperty = currentProperties[userProperty.key]

        return if (existingProperty?.value != userProperty.value) {
            userPropertyCache.value = currentProperties + (userProperty.key to userProperty)
            true
        } else {
            false
        }
    }

    /**
     * Emits a [userProperty] to the [_userProperty] flow.
     */
    internal fun setUserProperty(userProperty: UserProperty) {
        val wasChanged = cacheUserProperty(userProperty)
        // Only emit to flow when tracking is enabled AND the property actually changed
        if (!enabledState.value || !wasChanged) return
        coroutineScope.launch {
            _userProperty.emit(userProperty)
        }
    }
}

/**
 * Convenience function to collect tracking events from the [OGTrackingDealer]'s coroutine scope so
 * that the caller does not need to provide one of their own.
 */
public fun OGTrackingDealer.collectEvents(onEvent: suspend (OGEvent) -> Unit) {
    coroutineScope.launch { events.collect(onEvent) }
}

/**
 * Convenience function to collect user property from the [OGTrackingDealer]'s coroutine scope so
 * that the caller does not need to provide one of their own.
 */
internal fun OGTrackingDealer.collectUserProperty(onUserProperty: suspend (UserProperty) -> Unit) {
    coroutineScope.launch { userProperty.collect(onUserProperty) }
}
