package com.ottogroup.appkit.tracking.services.adjust

public interface AdjustAnalytics {
    public fun logEvent(event: AdjustEvent)
    public fun setEnabled(enabled: Boolean)
    public fun setThirdPartySharingEnabled(enabled: Boolean)
    public fun addGlobalPartnerParameter(key: String, value: String)
    public fun removeGlobalPartnerParameter(key: String)
    public fun clearAllGlobalPartnerParameters()

    /**
     * Provides a key value pair based on a user property.
     * @param key the name of the user property
     * @param value nullable value of the user property
     */
    public fun setUserProperty(key: String, value: String?) {}

    /**
     * Called when the app is about to open a URI by deep link.
     * @param uri the URI that the app is about to open
     */
    public fun openDeepLinkUri(uri: String) {}
}
