package com.ottogroup.appkit.tracking.event

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * A custom event parameter.
 *
 * Only primitive values are supported for serialization.
 */
@Serializable(with = CustomParameterSerializer::class)
public data class CustomParameter(
    val value: Any
)

private class CustomParameterSerializer : KSerializer<CustomParameter> {

    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(
        "com.ottogroup.appkit.tracking.event.CustomParameterSerializer",
        PrimitiveKind.STRING
    )

    override fun serialize(encoder: Encoder, value: CustomParameter) {
        encoder.encodeString(value.toString())
    }

    override fun deserialize(decoder: Decoder): CustomParameter {
        val string = decoder.decodeString()
        val value =
            string.toIntOrNull() ?: string.toLongOrNull() ?: string.toBooleanStrictOrNull() ?: string.toFloatOrNull()
                ?: string.toDoubleOrNull() ?: string
        return CustomParameter(value)
    }
}
