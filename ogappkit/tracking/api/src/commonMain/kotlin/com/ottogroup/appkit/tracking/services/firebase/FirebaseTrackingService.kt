package com.ottogroup.appkit.tracking.services.firebase

import com.ottogroup.appkit.tracking.event.CustomParameter
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.GenericEvent
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.View.Overlay
import com.ottogroup.appkit.tracking.event.PageType
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.event.View.Screen
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import com.ottogroup.appkit.tracking.json.toPrimitive
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.JsonObject

internal class FirebaseTrackingService(
    trackingDealer: com.ottogroup.appkit.tracking.OGTrackingDealer,
    configFlow: Flow<FirebaseConfig>,
    coroutineScope: CoroutineScope,
    private val firebaseAnalytics: FirebaseAnalytics,
    isInitiallyEnabled: Boolean = false,
) : OGTrackingService<FirebaseConfig, FirebaseEvent>(
    trackingDealer,
    isInitiallyEnabled = isInitiallyEnabled,
    serviceId = OGTrackingServiceId.Firebase,
    configFlow = configFlow,
    coroutineScope = coroutineScope
) {

    override val config: StateFlow<FirebaseConfig> = configFlow.stateIn(
        coroutineScope,
        SharingStarted.Eagerly,
        FirebaseConfig(emptyList(), isInitiallyEnabled)
    )

    override fun onIsEnabledChanged(enabled: Boolean) {
        firebaseAnalytics.setAnalyticsCollection(enabled)
        updateConsent(enabled, config.value.deniedConsentTypes)
        super.onIsEnabledChanged(enabled)
    }

    private fun updateConsent(enabled: Boolean, forceDeniedConsentTypes: List<ConsentType>) {
        val consents = ConsentType.entries.associateWith {
            val isForciblyDenied = forceDeniedConsentTypes.contains(it)
            when {
                enabled && isForciblyDenied -> ConsentStatus.DENIED
                enabled -> ConsentStatus.GRANTED
                // Even if the analytics feature is not enabled, the ANALYTICS_STORAGE
                // consent is granted unless it is forcibly denied.
                !enabled && !isForciblyDenied && it == ConsentType.ANALYTICS_STORAGE -> ConsentStatus.GRANTED

                else -> ConsentStatus.DENIED
            }
        }
        firebaseAnalytics.setAnalyticsConsent(consents)
    }

    override fun setDefaultParameters(parameters: List<JsonObject>) {
        val actualParameters =
            parameters.mapNotNull { it["data"]?.jsonObjectOrNull }
                .flatMap { it.entries }
                .associate { it.key to it.value.toPrimitive().takeIf { it != "null" } }
        firebaseAnalytics.setDefaultParameters(actualParameters)
    }

    override fun mapEvent(trackingEvent: OGEvent): FirebaseEvent? = trackingEvent.toFirebaseEvent()

    override fun performTrackEvent(trackingEvent: FirebaseEvent) {
        firebaseAnalytics.logEvent(trackingEvent)
    }

    override fun setUserProperty(key: String, value: String?) {
        firebaseAnalytics.setUserProperty(key, value)
    }
}

private object Parameter {
    const val SCREEN_NAME = "screen_name"
    const val CATEGORY = "category"
    const val LABEL = "label"
    const val DETAIL = "detail"
    const val SCENARIO = "scenario"
    const val SEARCH_TERM = "search_term"
    const val SUGGESTION_TYPE = "suggestion_type"
    const val METHOD = "method"
    const val CURRENCY = "currency"
    const val VALUE = "value"
    const val CONTENT_TYPE = "content_type"
    const val ITEMS = "items"
    const val CREATIVE_NAME = "creative_name"
    const val CREATIVE_SLOT = "creative_slot"
    const val PROMOTION_ID = "promotion_id"
    const val PROMOTION_NAME = "promotion_name"
}

private object Event {
    const val SELECT_EVENT = "select"
    const val SELECT_ITEM = "select_item"
    const val SELECT_PROMOTION = "select_promotion"
    const val SCREEN_VIEW = "screen_view"
    const val SEARCH = "search"
    const val LOGIN = "login"
    const val LOGOUT = "logout"
    const val PURCHASE = "purchase"
    const val ADD_TO_CART = "add_to_cart"
    const val ADD_TO_WISHLIST = "add_to_wishlist"
    const val SHARE = "share"
    const val VIEW_ITEM = "view_item"
    const val VIEW_PROMOTION = "view_promotion"
}

private fun OGEvent.toFirebaseEvent(): FirebaseEvent? {
    return when (this) {
        is Interaction -> FirebaseEvent(
            Event.SELECT_EVENT,
            interactionParameters(
                category,
                scenario,
                detail,
                label
            )
        )

        is Overlay -> FirebaseEvent(
            Event.SCREEN_VIEW,
            overlayParameters(category, detail, scenario)
        )

        is Screen -> FirebaseEvent(Event.SCREEN_VIEW, screenParameters(pageType, pageTitle))
        is View.ProductDetailViewItem -> FirebaseEvent(
            Event.VIEW_ITEM,
            eCommerceItemsParameters(listOf(item)) + additionalParameters(additionalParameters)
        )

        is View.ProductDetailPromotion -> FirebaseEvent(
            Event.VIEW_PROMOTION,
            eCommerceItemsParameters(listOf(item)) + promotionParameters(
                creativeName = creativeName,
                creativeSlot = creativeSlot,
                promotionId = promotionId,
                promotionName = promotionName,
            ) + additionalParameters(additionalParameters)
        )

        Interaction.AddToCart -> FirebaseEvent(Event.ADD_TO_CART, emptyMap())
        is Interaction.AddItemToCart ->
            FirebaseEvent(
                Event.ADD_TO_CART,
                eCommerceItemsParameters(listOf(item)) + additionalParameters(additionalParameters)
            )

        Interaction.AddToWishlist -> FirebaseEvent(Event.ADD_TO_WISHLIST, emptyMap())
        is Interaction.AddItemToWishlist ->
            FirebaseEvent(
                Event.ADD_TO_WISHLIST,
                eCommerceItemsParameters(listOf(item)) + additionalParameters(additionalParameters)
            )

        Interaction.LoggedIn -> FirebaseEvent(Event.LOGIN, mapOf(Parameter.METHOD to "Web"))
        Interaction.LoggedOut -> FirebaseEvent(Event.LOGOUT, emptyMap())
        is Interaction.Purchase -> FirebaseEvent(
            Event.PURCHASE,
            purchaseParameters(currency, value)
        )

        is Interaction.Search.QuerySubmit -> FirebaseEvent(
            Event.SEARCH,
            searchParameters(
                searchTerm,
                suggestionType
            )
        )

        is Interaction.Search -> FirebaseEvent(Event.SEARCH, searchParameters(null, suggestionType))
        Interaction.ShareProduct -> FirebaseEvent(
            Event.SHARE,
            mapOf(Parameter.CONTENT_TYPE to "product")
        )

        is Interaction.ProductDetailSelectPromotion -> FirebaseEvent(
            Event.SELECT_PROMOTION,
            eCommerceItemsParameters(listOf(item)) + promotionParameters(
                creativeName = creativeName,
                creativeSlot = creativeSlot,
                promotionId = promotionId,
                promotionName = promotionName,
            ) + additionalParameters(additionalParameters)
        )

        is Interaction.ProductDetailSelectVariant -> FirebaseEvent(
            Event.SELECT_ITEM,
            eCommerceItemsParameters(listOf(item)) + mapOf(
                "item_list_id" to listId,
            )
        )

        is GenericEvent -> FirebaseEvent(name, params)
    }
}

private fun screenParameters(
    pageType: PageType,
    pageTitle: String? = null
): Map<String, Any?> {
    return mapOf(Parameter.SCREEN_NAME to "${pageType.value}${pageTitle?.let { "_$it" } ?: ""}")
}

private fun searchParameters(
    searchTerm: String? = null,
    suggestionType: String? = null
): Map<String, Any?> {
    return mapOf(Parameter.SEARCH_TERM to searchTerm, Parameter.SUGGESTION_TYPE to suggestionType)
}

private fun purchaseParameters(
    currency: String,
    orderValue: Float
): Map<String, Any?> {
    return mapOf(Parameter.CURRENCY to currency, Parameter.VALUE to orderValue)
}

private fun overlayParameters(
    category: String? = null,
    detail: String,
    scenario: String? = null
): Map<String, Any?> {
    return mapOf(Parameter.SCREEN_NAME to "${detail}${scenario?.let { "_$it" } ?: ""}")
}

private fun interactionParameters(
    category: String? = null,
    scenario: String? = null,
    detail: String? = null,
    label: String? = null
): Map<String, Any?> {
    return mapOf(
        Parameter.CATEGORY to category,
        Parameter.SCENARIO to scenario,
        Parameter.DETAIL to detail,
        Parameter.LABEL to label,
    )
}

private fun promotionParameters(
    creativeName: String? = null,
    creativeSlot: String? = null,
    promotionId: String? = null,
    promotionName: String? = null,
): Map<String, Any?> {
    return mapOf(
        Parameter.CREATIVE_NAME to creativeName,
        Parameter.CREATIVE_SLOT to creativeSlot,
        Parameter.PROMOTION_ID to promotionId,
        Parameter.PROMOTION_NAME to promotionName,
    ).filterValues { it != null }
}

private fun eCommerceItemsParameters(items: List<ECommerceItem>): Map<String, Any?> {
    return mapOf(
        Parameter.ITEMS to items.map {
            val primaryParameters = mapOf(
                "item_name" to it.name,
                "item_id" to it.id,
                "price" to it.price,
                "coupon" to it.coupon,
                "discount" to it.discount,
                "currency" to it.currency,
                "affiliation" to it.affiliation,
                "item_brand" to it.brand,
                "item_category" to it.category,
                "item_category2" to it.category2,
                "item_category3" to it.category3,
                "item_category4" to it.category4,
                "item_category5" to it.category5,
                "item_variant" to it.variant,
                "item_list_name" to it.listName,
                "item_list_id" to it.listId,
                "location_id" to it.locationId,
                "index" to it.index,
                "quantity" to it.quantity,

                // non-standard parameters
                "has_colors" to it.hasColors,
                "has_sizes" to it.hasSizes,
            )
            val allParameters = primaryParameters + additionalParameters(it.additionalParameters)
            allParameters.filterValues { it != null }
        }
    )
}

private fun additionalParameters(map: Map<String, CustomParameter>) =
    map.mapValues { it.value.value }
