package com.ottogroup.appkit.tracking.log

import com.ottogroup.appkit.tracking.log.db.LogDatabase
import com.ottogroup.appkit.tracking.log.db.schema.EventEntity
import com.ottogroup.appkit.tracking.log.db.schema.EventWithTrackingServices
import com.ottogroup.appkit.tracking.log.db.schema.LogEntity
import com.ottogroup.appkit.tracking.log.db.schema.NetDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.ServiceEventEntity
import com.ottogroup.appkit.tracking.log.db.schema.SysDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceEntity
import com.ottogroup.appkit.tracking.log.domain.ServiceEvent
import com.ottogroup.appkit.tracking.log.domain.TrackingService
import com.ottogroup.appkit.tracking.log.domain.toDomain
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceWithServiceEvent
import com.ottogroup.appkit.tracking.log.domain.BaseLog
import com.ottogroup.appkit.tracking.log.domain.CustomLog
import com.ottogroup.appkit.tracking.log.domain.Event
import com.ottogroup.appkit.tracking.log.domain.EventLog
import com.ottogroup.appkit.tracking.log.domain.LogEntry
import com.ottogroup.appkit.tracking.log.domain.NetLog
import com.ottogroup.appkit.tracking.log.db.schema.Parameter as ParameterSchema
import com.ottogroup.appkit.tracking.log.domain.Parameter
import com.ottogroup.appkit.tracking.log.domain.SysLog
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

public interface EventLogRepository : LogRepository {
    public suspend fun addEventLogEntry(eventLog: EventLog)
    public fun getAllEventLogEntries(): Flow<List<EventLog>>
}

public interface LogRepository {
    public fun getAllLogEntries(): Flow<List<LogEntry>>
    public suspend fun clearAllLogs()
    public suspend fun addLogEntry(logEntry: LogEntry)
}

internal class EventLogRepoImpl(
    private val logDatabase: LogDatabase
) : EventLogRepository {

    override suspend fun addEventLogEntry(eventLog: EventLog) {
        addLogEntry(eventLog)
    }

    override fun getAllEventLogEntries(): Flow<List<EventLog>> {
        return logDatabase.eventDao().getAllLogsWithEventsAndServices().map { list ->
            list.map {
                EventLog(
                    message = it.logEntity.message,
                    event = it.eventEntity.event.toDomain(),
                    timestamp = it.logEntity.timestamp,
                    trackingServices = it.eventEntity.trackingServices.map { it.toDomain() }
                )
            }
        }
    }

    override fun getAllLogEntries(): Flow<List<LogEntry>> {
        return logDatabase.logDao().getAllLogs().map { list ->
            list.map { entry ->
                entry.eventEntity?.let {
                    return@map it.toDomain(entry.logEntity)
                }
                entry.sysEntity?.let {
                    return@map it.toDomain(entry.logEntity)
                }
                entry.netEntity?.let {
                    return@map it.toDomain(entry.logEntity)
                }
                return@map BaseLog(entry.logEntity.timestamp, entry.logEntity.message)
            }
        }
    }

    override suspend fun addLogEntry(logEntry: LogEntry) {
        val logId = addLog(LogEntity(message = logEntry.message, timestamp = logEntry.timestamp))
        when (logEntry) {
            is EventLog -> addEventLog(
                logId,
                logEntry.event.toEntity(),
                logEntry.trackingServices.map { it.toEntity() }
            )

            is NetLog -> addNetLog(logId, logEntry.toEntity())

            is SysLog -> addSysLog(logId, logEntry.toEntity())

            is BaseLog,
            is CustomLog -> Unit
        }
    }

    private suspend fun addLog(logEntity: LogEntity): Long {
        return logDatabase.logDao().insert(logEntity)
    }

    private suspend fun addEventLog(
        logId: Long,
        eventEntity: EventEntity,
        trackingServiceEntities: List<TrackingServiceWithServiceEvent>
    ) {
        val eventDao = logDatabase.eventDao()
        eventDao.insert(logId, eventEntity, trackingServiceEntities)
    }

    private suspend fun addNetLog(logId: Long, netDataEntity: NetDataEntity) {
        logDatabase.netDao().insert(logId, netDataEntity)
    }

    private suspend fun addSysLog(logId: Long, sysDataEntity: SysDataEntity) {
        logDatabase.sysDao().insert(logId, sysDataEntity)
    }

    override suspend fun clearAllLogs() {
        logDatabase.logDao().clearAllLogs()
    }
}

private fun SysLog.toEntity(): SysDataEntity {
    return SysDataEntity(
        priority = priority,
        tag = tag,
        stacktrace = stacktrace,
        fileName = fileName,
        methodName = methodName,
        lineNumber = lineNumber
    )
}

private fun NetDataEntity.toDomain(entry: LogEntity): LogEntry {
    return NetLog(
        entry.timestamp,
        entry.message,
        method,
        url,
        httpStatus,
        responseTime,
        requestHeader,
        requestBody,
        responseHeader,
        responseBody,
        responseSize
    )
}

private fun SysDataEntity.toDomain(entry: LogEntity): SysLog {
    return SysLog(
        entry.timestamp,
        entry.message,
        priority,
        tag,
        stacktrace,
        fileName,
        methodName,
        lineNumber
    )
}

private fun EventWithTrackingServices.toDomain(entry: LogEntity): EventLog {
    return EventLog(
        entry.timestamp,
        entry.message,
        event.toDomain(),
        trackingServices.map { it.toDomain() }
    )
}

private fun Event.toEntity(): EventEntity {
    return EventEntity(name = name, properties = properties)
}

private fun Parameter.toEntity(): com.ottogroup.appkit.tracking.log.db.schema.Parameter {
    return com.ottogroup.appkit.tracking.log.db.schema.Parameter(schema = schema, data = data)
}

private fun TrackingService.toEntity(): TrackingServiceWithServiceEvent {
    return TrackingServiceWithServiceEvent(
        service = TrackingServiceEntity(
            serviceName = serviceName,
            isEnabled = isEnabled,
            contextParameter = contextParameter.map { it.toEntity() },
        ),
        serviceEvent = ServiceEventEntity(
            name = serviceEvent?.name ?: "",
            properties = serviceEvent?.properties ?: emptyMap()
        )
    )
}

private fun NetLog.toEntity(): NetDataEntity {
    return NetDataEntity(
        method = method,
        url = url,
        httpStatus = httpStatus,
        responseTime = responseTime,
        requestHeader = requestHeader,
        requestBody = requestBody,
        responseHeader = responseHeader,
        responseBody = responseBody,
        responseSize = responseSize
    )
}

private fun TrackingServiceWithServiceEvent.toDomain(): TrackingService {
    return TrackingService(
        serviceName = service.serviceName,
        isEnabled = service.isEnabled,
        contextParameter = service.contextParameter.map { it.toDomain() },
        serviceEvent = ServiceEvent(
            name = serviceEvent.name,
            properties = serviceEvent.properties
        )
    )
}

private fun ParameterSchema.toDomain(): Parameter {
    return Parameter(
        schema = schema,
        data = data
    )
}
