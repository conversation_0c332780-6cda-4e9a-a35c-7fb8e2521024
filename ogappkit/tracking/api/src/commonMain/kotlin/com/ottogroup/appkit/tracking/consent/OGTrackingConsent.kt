package com.ottogroup.appkit.tracking.consent

import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

/**
 * Consent handling for tracking services.
 *
 * There are two types of consent:
 * - individual consent for each tracking service identified by a service
 *   name
 * - a global consent that applies when no individual consent is set
 *
 * The service name used to set individual service consent must match the
 * name(s) defined by the tracking service implementation. If information
 * about an individual service is present – whether true or false – the
 * global consent is not taken into account.
 *
 * Tracking consent is persisted to local storage.
 */
public interface OGTrackingConsent {
    public fun consentForService(serviceId: OGTrackingServiceId): Flow<Boolean>
    public fun setGlobalConsent(consent: Boolean)
    public fun setConsentsForServices(consents: Map<OGTrackingServiceId, Boolean>, replacePrevious: Boolean = false)
}

internal class OGTrackingConsentImpl internal constructor(
    internal val coroutineScope: CoroutineScope,
    private val trackingConsentLocalDataSource: OGTrackingConsentLocalDataSource,
) : OGTrackingConsent {

    /**
     * A flow of the current tracking consent state for the specified
     * [serviceId].
     */
    override fun consentForService(serviceId: OGTrackingServiceId): Flow<Boolean> {
        return trackingConsentLocalDataSource.trackingConsentState.map { (globalConsent, serviceConsents) ->
            serviceConsents[serviceId] ?: globalConsent
        }.distinctUntilChanged()
    }

    /**
     * Sets the global consent and persists it to storage.
     */
    override fun setGlobalConsent(consent: Boolean) {
        coroutineScope.launch { trackingConsentLocalDataSource.setGlobalConsent(consent) }
    }

    /**
     * Sets the consent for multiple services and persists them to storage. If
     * [replacePrevious] is true, all previous consents are replaced with the
     * new ones. If it is false, only consent values contained in [consents]
     * will be updated.
     */
    override fun setConsentsForServices(consents: Map<OGTrackingServiceId, Boolean>, replacePrevious: Boolean) {
        coroutineScope.launch {
            trackingConsentLocalDataSource.setConsentsForServices(
                consents,
                replacePrevious
            )
        }
    }
}
