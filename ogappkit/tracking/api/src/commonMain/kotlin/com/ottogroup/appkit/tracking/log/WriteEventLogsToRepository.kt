package com.ottogroup.appkit.tracking.log

import co.touchlab.kermit.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

internal class WriteEventLogsToRepository(
    eventLogger: EventLogger,
    eventLogRepo: EventLogRepository,
    coroutineScope: CoroutineScope
) {

    init {
        eventLogger.events.onEach {
            eventLogRepo.addLogEntry(it)
        }.catch { Logger.e("Failed inserting event", it) }.launchIn(coroutineScope)
    }
}
