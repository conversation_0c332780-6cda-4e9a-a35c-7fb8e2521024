package com.ottogroup.appkit.tracking.log

import co.touchlab.kermit.Logger
import co.touchlab.kermit.Severity
import com.ottogroup.appkit.tracking.LogLevel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

internal class ConfigureLogging(coroutineScope: CoroutineScope, logLevel: Flow<LogLevel>) {
    init {
        logLevel
            .onEach { Logger.setMinSeverity(it.toKermitLogLevel()) }
            .launchIn(coroutineScope)
    }

    private fun LogLevel.toKermitLogLevel(): Severity {
        return when (this) {
            LogLevel.Verbose -> Severity.Verbose
            LogLevel.Debug -> Severity.Debug
            LogLevel.Info -> Severity.Info
            LogLevel.Warn -> Severity.Warn
            LogLevel.Error -> Severity.Error
            LogLevel.Assert -> Severity.Assert
        }
    }
}
