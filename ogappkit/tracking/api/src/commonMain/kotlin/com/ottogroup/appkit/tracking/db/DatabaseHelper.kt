package com.ottogroup.appkit.tracking.db

import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import com.ottogroup.appkit.tracking.log.db.converter.TypeConverter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO

internal expect class DatabaseHelper

internal expect inline fun <reified T : RoomDatabase> DatabaseHelper.createDatabaseBuilder(fileName: String): RoomDatabase.Builder<T>

internal expect inline fun <reified T : RoomDatabase> DatabaseHelper.createInMemoryDatabaseBuilder(): RoomDatabase.Builder<T>

internal inline fun <reified T : RoomDatabase> DatabaseHelper.createDatabase(
    fileName: String,
    vararg migrations: Migration
): T {
    return createDatabaseBuilder<T>(fileName)
        .addMigrations(*migrations)
        .addTypeConverter(TypeConverter())
        .fallbackToDestructiveMigrationOnDowngrade(true)
        .setDriver(BundledSQLiteDriver())
        .setQueryCoroutineContext(Dispatchers.IO)
        .build()
}

internal inline fun <reified T : RoomDatabase> DatabaseHelper.createMemoryDatabase(
    vararg migrations: Migration
): T {
    return createInMemoryDatabaseBuilder<T>()
        .addMigrations(*migrations)
        .addTypeConverter(TypeConverter())
        .fallbackToDestructiveMigrationOnDowngrade(true)
        .setDriver(BundledSQLiteDriver())
        .setQueryCoroutineContext(Dispatchers.IO)
        .build()
}
