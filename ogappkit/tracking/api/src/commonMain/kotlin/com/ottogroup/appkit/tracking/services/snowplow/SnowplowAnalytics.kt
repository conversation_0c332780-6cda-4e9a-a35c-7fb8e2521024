package com.ottogroup.appkit.tracking.services.snowplow

public interface SnowplowAnalytics {
    public fun logEvent(event: SnowplowEvent)
    public fun setAnalyticsCollection(enabled: Boolean)
    public fun setGlobalContext(contexts: List<GlobalContextParameter>)

    /**
     * Provides a key value pair based on a user property.
     * @param key the name of the user property
     * @param value nullable value of the user property
     */
    public fun setUserProperty(key: String, value: String?) {}
}

public data class GlobalContextParameter(val schema: String, val data: Map<String, Any?>)
