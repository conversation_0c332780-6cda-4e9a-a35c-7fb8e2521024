package com.ottogroup.appkit.tracking.services.dynamicyield.api.response

import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice.Type
import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonContentPolymorphicSerializer
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

@Serializable
internal data class ChooseResponse(
    val choices: List<Choice>,
    override val cookies: List<Cookie>,
) : BaseResponse

@Serializable(with = ChoiceSerializer::class)
public sealed interface Choice {
    public val type: Type
    public val name: String

    public enum class Type {
        DECISION,
        RECS_DECISION,
        SORT_DECISION,
        NO_DECISION,
    }

    @Serializable
    public data class CustomChoice(
        override val name: String,
        public val variations: List<Variation<Variation.Payload.CustomJsonPayload>>
    ) : Choice {
        override val type: Type = Type.DECISION
    }

    @Serializable
    public data class RecommendationsChoice(
        override val name: String,
        public val variations: List<Variation<Variation.Payload.RecommendationsPayload>>
    ) : Choice {
        override val type: Type = Type.RECS_DECISION
    }

    @Serializable
    public data class SortChoice(
        override val name: String,
        public val variations: List<Variation<Variation.Payload.SortPayload>>
    ) : Choice {
        override val type: Type = Type.SORT_DECISION
    }

    @Serializable
    public data class NoChoice(override val name: String) : Choice {
        override val type: Type = Type.NO_DECISION
    }
}

@Serializable
public data class Variation<P : Variation.Payload<*>>(
    val id: String,
    val payload: P,
) {
    public sealed interface Payload<Data> {
        public val type: Type
        public val data: Data

        public enum class Type {
            CUSTOM_JSON,
            RECS,
            SORTING,
        }

        @Serializable
        public data class CustomJsonPayload(
            override val data: JsonObject
        ) : Payload<JsonObject> {
            override val type: Type = Type.CUSTOM_JSON
        }

        @Serializable
        public data class RecommendationsPayload(
            override val data: SlotData
        ) : Payload<SlotData> {
            override val type: Type = Type.RECS
        }

        @Serializable
        public data class SortPayload(
            override val data: SlotData
        ) : Payload<SlotData> {
            override val type: Type = Type.SORTING
        }

        @Serializable
        public data class SlotData(
            val slots: List<Slot>,
        ) {
            @Serializable
            public data class Slot(
                val sku: String,
                val slotId: String,
                val productData: ProductData,
            )
        }
    }
}

@Serializable
public data class ProductData(
    val brand: String,
    val name: String,
    val url: String,
    @SerialName("image_url")
    val imageUrl: String,
    val price: Float,
)

private object ChoiceSerializer : JsonContentPolymorphicSerializer<Choice>(Choice::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<Choice> {
        val type = element.jsonObject["type"]?.jsonPrimitive?.content?.let {
            Type.valueOf(it)
        }
        return when (type) {
            Type.DECISION -> Choice.CustomChoice.serializer()
            Type.RECS_DECISION -> Choice.RecommendationsChoice.serializer()
            Type.SORT_DECISION -> Choice.SortChoice.serializer()
            Type.NO_DECISION -> Choice.NoChoice.serializer()
            null -> throw IllegalArgumentException("Unknown choice type: ${element.jsonObject["type"]?.jsonPrimitive?.content}")
        }
    }
}
