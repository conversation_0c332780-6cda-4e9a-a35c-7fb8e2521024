package com.ottogroup.appkit.tracking.services.airship

import com.ottogroup.appkit.tracking.services.ServiceConfig
import kotlinx.serialization.json.JsonObject

public data class AirshipConfig(
    override val isEnabled: Boolean,
    override val globalContext: List<JsonObject> = emptyList()
) : ServiceConfig() {
    internal override fun baseCopy(isEnabled: Boolean, globalContext: List<JsonObject>): ServiceConfig {
        return copy(isEnabled = isEnabled, globalContext = globalContext)
    }
}
