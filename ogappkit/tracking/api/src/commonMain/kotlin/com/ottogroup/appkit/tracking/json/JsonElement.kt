package com.ottogroup.appkit.tracking.json

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonObject

public val JsonElement.jsonObjectOrNull: JsonObject?
    get() = try {
        jsonObject
    } catch (_: IllegalArgumentException) {
        null
    }

internal fun Any?.toJsonElement(): JsonElement = when (this) {
    is Number -> JsonPrimitive(this)
    is Boolean -> JsonPrimitive(this)
    is String -> JsonPrimitive(this)
    is Array<*> -> this.toJsonArray()
    is List<*> -> this.toJsonArray()
    is Map<*, *> -> this.toJsonObject()
    is JsonElement -> this
    else -> JsonNull
}

internal fun Array<*>.toJsonArray(): JsonArray = JsonArray(map { it.toJsonElement() })
internal fun Iterable<*>.toJsonArray(): JsonArray = JsonArray(map { it.toJsonElement() })
internal fun Map<*, *>.toJsonObject(): JsonObject =
    JsonObject(mapKeys { it.key.toString() }.mapValues { it.value.toJsonElement() })
