package com.ottogroup.appkit.tracking.log.db.schema

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.Index
import androidx.room.Junction
import androidx.room.PrimaryKey
import androidx.room.Relation

@Entity()
public data class SysDataEntity(
    @PrimaryKey(autoGenerate = true)
    val sysId: Long = 0,
    val priority: Int,
    val tag: String?,
    val stacktrace: String?,
    val fileName: String?,
    val methodName: String?,
    val lineNumber: Int?,
)

@Entity(
    primaryKeys = ["logIdRef", "sysIdRef"],
    foreignKeys = [
        ForeignKey(
            entity = SysDataEntity::class,
            parentColumns = ["sysId"],
            childColumns = ["sysIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        ),
        ForeignKey(
            entity = LogEntity::class,
            parentColumns = ["logId"],
            childColumns = ["logIdRef"],
            onDelete = CASCADE,
            onUpdate = CASCADE
        )
    ],
    indices = [Index(value = ["logIdRef"]), Index(value = ["sysIdRef"])]
)
internal data class LogToSysMappingEntity(
    val logIdRef: Long,
    val sysIdRef: Long
)

internal data class LogWithSys(
    @Embedded
    val logEntity: LogEntity,
    @Relation(
        entity = EventEntity::class,
        parentColumn = "logId",
        entityColumn = "sysId",
        associateBy = Junction(
            value = LogToSysMappingEntity::class,
            parentColumn = "logIdRef",
            entityColumn = "sysIdRef"
        )
    )
    val eventEntity: SysDataEntity
)
