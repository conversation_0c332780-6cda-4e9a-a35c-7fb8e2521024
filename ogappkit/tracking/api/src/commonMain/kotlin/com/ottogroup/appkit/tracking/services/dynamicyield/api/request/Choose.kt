package com.ottogroup.appkit.tracking.services.dynamicyield.api.request

import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent.PageView

internal fun chooseVariationsPayload(
    event: PageView,
    selectorNames: List<String>,
    selectorGroups: List<String>,
    dyid: String,
    dyidServer: String,
    dyjsession: String,
): String = """
    {
      "user": {
        "active_consent_accepted": true,
        "dyid": "$dyid",
        "dyid_server": "$dyidServer"
      },
      "session": {
        "dy": "$dyjsession"
      },
      "context": {
        "page": {
          "type": "${event.type.name}",
          "location": "${event.location}",
          "data": ${event.data.toJsonArray()}
        },
        "device": {
          "type": "SMARTPHONE"
        },
        "channel": "APP"
      },
      "selector": {
        "names": ${selectorNames.toJsonArray()},
        "groups": ${selectorGroups.toJsonArray()}
      },
      "options": {
        "isImplicitImpressionMode": true,
        "recsProductData": {
          "fieldFilter": [
            "brand",
            "name",
            "image_url",
            "price",
            "url"
          ]
        }
      }
    }
""".trimIndent()
