package com.ottogroup.appkit.tracking.services.snowplow

import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.services.OGTrackingService
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.json.jsonObjectOrNull
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonPrimitive

internal class SnowplowTrackingService(
    trackingDealer: OGTrackingDealer,
    configFlow: Flow<SnowplowConfig>,
    coroutineScope: CoroutineScope,
    private val snowplowAnalytics: SnowplowAnalytics,
    isInitiallyEnabled: Boolean = true,
) : OGTrackingService<SnowplowConfig, SnowplowEvent>(
    trackingDealer,
    isInitiallyEnabled = isInitiallyEnabled,
    configFlow,
    serviceId = OGTrackingServiceId.Snowplow,
    coroutineScope
) {
    override val config: StateFlow<SnowplowConfig> =
        configFlow.stateIn(coroutineScope, SharingStarted.Eagerly, SnowplowConfig(isInitiallyEnabled))

    override fun mapEvent(trackingEvent: OGEvent): SnowplowEvent? = trackingEvent.toSnowplow()

    override fun onIsEnabledChanged(enabled: Boolean) {
        snowplowAnalytics.setAnalyticsCollection(enabled)
        super.onIsEnabledChanged(enabled)
    }

    override fun performTrackEvent(trackingEvent: SnowplowEvent) {
        snowplowAnalytics.logEvent(trackingEvent)
    }

    override fun setDefaultParameters(parameters: List<JsonObject>) {
        val actualParameters = parameters.mapNotNull {
            try {
                val schema = it.get("schema")?.jsonPrimitive?.content ?: return@mapNotNull null
                val data = it.get("data")?.jsonObjectOrNull ?: emptyMap()
                GlobalContextParameter(schema, data)
            } catch (iae: IllegalArgumentException) {
                null
            }
        }
        snowplowAnalytics.setGlobalContext(actualParameters)
    }

    private fun OGEvent.toSnowplow(): SnowplowEvent? {
        return when (this) {
            View.Screen.Basket,
            View.Screen.Checkout,
            View.Screen.Home,
            View.Screen.Login,
            View.Screen.OrderConfirmation,
            View.Screen.ProductDetailPage,
            View.Screen.ProductList,
            View.Screen.ProductListOfSearchResult,
            View.Screen.Registration,
            View.Screen.Wishlist -> null // we want to ignore all web related events
            View.Screen.CategoryMenu,
            is Interaction.CategorySubMenuEntry,
            is Interaction.CategoryMenuEntry,
            is Interaction.CategoryBannerEntry -> null // we want to ignore category events
            is View.Screen -> SnowplowEvent.ScreenView(pageType.value, pageTitle)
            is View.Overlay -> SnowplowEvent.SelfDescribing(
                OVERLAY_SCHEMA,
                mapOf(
                    "category" to this.category,
                    "label" to null,
                    "detail" to this.detail,
                    "placement" to "Overlay"
                )
            )

            is Interaction -> SnowplowEvent.SelfDescribing(
                INTERACTION_SCHEMA,
                mapOf(
                    "category" to this.category,
                    "label" to this.label,
                    "detail" to this.detail,
                    "placement" to this.scenario,
                    "target" to null
                )
            )

            else -> null
        }
    }

    companion object {
        private const val INTERACTION_SCHEMA =
            "iglu:eu.witt.gruppe/nativeUserInteraction/jsonschema/1-0-0"
        private const val OVERLAY_SCHEMA =
            "iglu:eu.witt.gruppe/displayOverlay/jsonschema/1-0-0"
    }
}
