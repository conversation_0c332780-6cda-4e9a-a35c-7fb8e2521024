package com.ottogroup.appkit.tracking.services

import kotlinx.serialization.json.JsonObject

/**
 * Base Configuration for a tracking service.
 */
public abstract class ServiceConfig {
    public open val isEnabled: Boolean = false
    public open val globalContext: List<JsonObject> = emptyList()

    internal abstract fun baseCopy(
        isEnabled: Boolean = this.isEnabled,
        globalContext: List<JsonObject> = this.globalContext
    ): ServiceConfig
}
