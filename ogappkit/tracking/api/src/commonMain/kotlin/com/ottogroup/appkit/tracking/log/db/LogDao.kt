import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.ottogroup.appkit.tracking.log.db.schema.EventEntity
import com.ottogroup.appkit.tracking.log.db.schema.EventToTrackingServiceMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToEventMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToNetMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogToSysMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.LogWithEventAndServices
import com.ottogroup.appkit.tracking.log.db.schema.LogWithNetOrEventOrSysOrCustom
import com.ottogroup.appkit.tracking.log.db.schema.NetDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.ServiceEventEntity
import com.ottogroup.appkit.tracking.log.db.schema.SysDataEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceToServiceEventMappingEntity
import com.ottogroup.appkit.tracking.log.db.schema.TrackingServiceWithServiceEvent
import kotlinx.coroutines.flow.Flow

@Dao
internal interface LogDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertAll(vararg logs: LogEntity): List<Long>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insert(log: LogEntity): Long

    @Query("SELECT * FROM LogEntity")
    fun getAll(): Flow<List<LogEntity>>

    @Transaction
    @Query("SELECT * FROM LogEntity")
    fun getAllLogs(): Flow<List<LogWithNetOrEventOrSysOrCustom>>

    @Transaction
    @Query("DELETE FROM LogEntity")
    suspend fun clearAllLogs()
}

@Dao
internal interface EventDao {
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insert(eventEntity: EventEntity): Long

    @Transaction
    @Query("SELECT * FROM LogEntity WHERE logId IN (SELECT logIdRef FROM LogToEventMappingEntity)")
    fun getAllLogsWithEventsAndServices(): Flow<List<LogWithEventAndServices>>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addEvent(event: EventEntity): Long

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addEventToLog(logToEventMapping: LogToEventMappingEntity)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addTrackingService(trackingService: TrackingServiceEntity): Long

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addTrackingServiceToEvent(eventToTrackingService: EventToTrackingServiceMappingEntity)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addServiceEvent(serviceEvent: ServiceEventEntity): Long

    @Transaction
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addServiceEventToTrackingService(serviceToServiceEventMappingEntity: TrackingServiceToServiceEventMappingEntity): Long

    @Transaction
    suspend fun addEventToLog(logId: Long, eventEntity: EventEntity): Long {
        val eventId = addEvent(eventEntity)
        addEventToLog(LogToEventMappingEntity(logId, eventId))
        return eventId
    }

    @Transaction
    suspend fun addTrackingServiceToEvent(
        eventId: Long,
        serviceEntity: TrackingServiceEntity
    ): Long {
        val serviceId = addTrackingService(serviceEntity)
        addTrackingServiceToEvent(
            EventToTrackingServiceMappingEntity(
                eventId,
                serviceId
            )
        )
        return serviceId
    }

    @Transaction
    suspend fun addServiceEventToTrackingService(
        trackingServiceId: Long,
        serviceEvent: ServiceEventEntity
    ): Long {
        val serviceEventId = addServiceEvent(serviceEvent)
        addServiceEventToTrackingService(
            TrackingServiceToServiceEventMappingEntity(
                trackingServiceId,
                serviceEventId
            )
        )
        return serviceEventId
    }

    @Transaction
    suspend fun insert(
        logId: Long,
        eventEntity: EventEntity,
        trackingServiceEntities: List<TrackingServiceWithServiceEvent>
    ) {
        val eventId = addEventToLog(logId, eventEntity)

        trackingServiceEntities.forEach {
            val serviceId = addTrackingServiceToEvent(eventId, it.service)
            val serviceEventId = addServiceEventToTrackingService(serviceId, it.serviceEvent)
        }
    }
}

@Dao
internal interface NetDao {
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insert(netDataEntity: NetDataEntity): Long

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addNetLogMapping(mappingEntity: LogToNetMappingEntity)

    @Transaction
    suspend fun insert(logId: Long, netDataEntity: NetDataEntity) {
        val netId = insert(netDataEntity)
        addNetLogMapping(LogToNetMappingEntity(logId, netId))
    }
}

@Dao
internal interface SysDao {
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insert(sysDataEntity: SysDataEntity): Long

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addSysLogMapping(mappingEntity: LogToSysMappingEntity)

    @Transaction
    suspend fun insert(logId: Long, sysDataEntity: SysDataEntity) {
        val sysId = insert(sysDataEntity)
        addSysLogMapping(LogToSysMappingEntity(logId, sysId))
    }
}
