package com.ottogroup.appkit.tracking.services.dynamicyield.api

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldConfig
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent
import com.ottogroup.appkit.tracking.services.dynamicyield.api.request.chooseVariationsPayload
import com.ottogroup.appkit.tracking.services.dynamicyield.api.request.trackPageViewPayload
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.ChooseResponse
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.TrackingResponse
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.toSetCookieHeaders
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

internal const val DY_TAG = "DynamicYield"

public interface DynamicYieldNetworkDataSource {
    public suspend fun chooseVariations(
        event: DynamicYieldEvent.PageView,
        selectorNames: List<String>,
        selectorGroups: List<String>,
    ): Result<List<Choice>>
}

internal interface InternalDynamicYieldNetworkDataSource : DynamicYieldNetworkDataSource {
    suspend fun trackPageView(event: DynamicYieldEvent.PageView): Result<Unit>
}

internal class DynamicYieldNetworkDataSourceImpl internal constructor(
    private val configFlow: Flow<DynamicYieldConfig>,
    private val client: HttpClient = dynamicYieldHttpClient(),
) : InternalDynamicYieldNetworkDataSource {
    override suspend fun trackPageView(event: DynamicYieldEvent.PageView): Result<Unit> {
        return resultFor {
            val config = configFlow.first()
            val cookiesBridge = config.cookiesBridge
            val cookies = cookiesBridge.getCookies(config.cookiesDomain)

            val payload = trackPageViewPayload(
                event = event,
                dyid = cookies[CookieKeys.DYID] ?: "",
                dyidServer = cookies[CookieKeys.DYID_SERVER] ?: "",
                dyjsession = cookies[CookieKeys.DYJSESSION] ?: "",
            )
            Logger.d(DY_TAG) { "Tracking SKU: ${event.data.firstOrNull()}" }
            val response = client.post(config.trackPageViewUrl) {
                header("dy-api-key", config.apiKey)
                setBody(payload)
            }
            val trackingResponse: TrackingResponse = response.body()
            trackingResponse.toSetCookieHeaders().forEach {
                cookiesBridge.setCookie(config.cookiesDomain, it)
            }
        }
    }

    override suspend fun chooseVariations(
        event: DynamicYieldEvent.PageView,
        selectorNames: List<String>,
        selectorGroups: List<String>,
    ): Result<List<Choice>> {
        return resultFor {
            val config = configFlow.first()
            val cookiesBridge = config.cookiesBridge
            val cookies = cookiesBridge.getCookies(config.cookiesDomain)
            val payload = chooseVariationsPayload(
                event = event,
                selectorNames = selectorNames,
                selectorGroups = selectorGroups,
                dyid = cookies[CookieKeys.DYID] ?: "",
                dyidServer = cookies[CookieKeys.DYID_SERVER] ?: "",
                dyjsession = cookies[CookieKeys.DYJSESSION] ?: "",
            )
            val response = client.post(config.trackPageViewUrl) {
                header("dy-api-key", config.apiKey)
                setBody(payload)
            }
            val chooseResponse: ChooseResponse = response.body()

            /* It is probably better not to store any user/session IDs returned from just querying data to not
             * accidentally overwrite a session created by a concurrently in-flight tracking event.
             */

            chooseResponse.choices.also {
                Logger.d(DY_TAG) { "Requesting choices for ${event.data.firstOrNull()} returned $it" }
            }
        }
    }
}

private object CookieKeys {
    const val DYID = "_dyid"
    const val DYID_SERVER = "_dyid_server"
    const val DYJSESSION = "_dyjsession"
}
