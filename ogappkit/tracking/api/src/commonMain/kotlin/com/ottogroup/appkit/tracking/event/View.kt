package com.ottogroup.appkit.tracking.event

import kotlinx.serialization.Serializable
import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

/**
 * Whenever users access a new screen
 * (or a new overlay on a screen)
 * that has been fully loaded
 * or when the user goes back to the previous screen,
 * a view event should be triggered.
 */
@Serializable
public sealed class View : OGEvent {
    /**
     * Represents a screen that the user has accessed.
     * @param pageType The type of the page that the user has accessed.
     * @param pageTitle The title of the page that the user has accessed.
     */
    @Serializable
    @GenerateSubclassObjectMapper
    public sealed class Screen(public val pageType: PageType, public val pageTitle: String? = null) : View() {

        public companion object;

        override fun describe(): Map<String, Any?> = super.describe() + mapOf(
            "pageType" to pageType,
            "pageTitle" to pageTitle,
        )

        /**
         * Tracked Screen: Account Menu
         * Source: App
         */
        @Serializable
        public data object AccountMenu : Screen(PageType.Account, null)

        /**
         * Tracked Screen: Country Chooser
         * Source: App
         * Scenario: AccountMenu and Onboarding/Welcome
         */
        @Serializable
        public data object CountryScreen : Screen(PageType.CountryOverview, null)

        /**
         * Tracked Screen: Bra Fitting Guide Step #1-5
         * Source: App
         * @param step The step of the Bra Fitting Guide that the user has accessed.
         */
        @Serializable
        public data class BraFittingGuideStep(public val step: Int) :
            Screen(PageType.BraFittingGuide, "Step $step") {
            override fun describe(): Map<String, Any?> = super.describe() + ("step" to step)
        }

        /**
         * Tracked Screen: Catalog Scanner - Intro
         * Source: App
         */
        @Serializable
        public data object CatalogScannerIntro : Screen(PageType.CatalogScanner, "Intro")

        /**
         * Tracked Screen: Catalog Scanner - Scan
         * Source: App
         */
        @Serializable
        public data object CatalogScannerScan : Screen(PageType.CatalogScanner, "Scan")

        /**
         * Tracked Screen: Category/Sub-Category Menu
         * Source: App
         */
        @Serializable
        public data object CategoryMenu : Screen(PageType.CategoryOverview, null)

        /**
         * Tracked Screen: Deals Onboarding
         * Source: App
         */
        @Serializable
        public data object DealsOnboarding : Screen(PageType.Deals, "Onboarding")

        /**
         * Tracked Screen: Deals Onboarding Video
         * Source: App
         */
        @Serializable
        public data object DealsOnboardingVideo : Screen(PageType.Deals, "OnboardingVideo")

        /**
         * Tracked Screen: Deals Overview
         * Source: App
         */
        @Serializable
        public data object DealsOverview : Screen(PageType.Deals, "Overview")

        /**
         * Tracked Screen: Error
         * Source: App
         */
        @Serializable
        public data object Error : Screen(PageType.Error, null)

        /**
         * Tracked Screen: Inbox - Message
         * Source: App
         */
        @Serializable
        public data object InAppInboxMessage : Screen(PageType.Inbox, "Message")

        /**
         * Tracked Screen: Inbox - Overview
         * Source: App
         */
        @Serializable
        public data object InAppInboxOverview : Screen(PageType.Inbox, "Overview")

        /**
         * Tracked Screen: Search
         * (applies to all states - no separation between initial search state)
         * Source: App
         */
        @Serializable
        public data object Search : Screen(PageType.Search, null)

        /**
         * Tracked Screen: Store Finder - Store Overview List
         * Source: App
         */
        @Serializable
        public data object StorefinderStoreOverviewList :
            Screen(PageType.StoreLocator, "StoreOverviewList")

        /**
         * Tracked Screen: Store Finder - Store Overview Map
         * Source: App
         */
        @Serializable
        public data object StorefinderStoreOverviewMap : Screen(PageType.StoreLocator, "StoreOverviewMap")

        /**
         * Tracked Screen: Store Finder - Store Detail
         * Source: App
         */
        @Serializable
        public data object StorefinderStoreDetail : Screen(PageType.StoreLocator, "StoreDetail")

        /**
         * Tracked Screen: Welcome/Onboarding
         * Source: App
         */
        @Serializable
        public data object Welcome : Screen(PageType.Onboarding, null)

        /**
         * Tracked Screen: Basket/Cart
         * Source: Web
         */
        @Serializable
        public data object Basket : Screen(PageType.Cart, null)

        /**
         * Tracked Screen: Checkout
         * Source: Web
         */
        @Serializable
        public data object Checkout : Screen(PageType.Checkout, null)

        /**
         * Tracked Screen: Homepage
         * Source: Web
         */
        @Serializable
        public data object Home : Screen(PageType.Homepage, null)

        /**
         * Tracked Screen: Login
         * Source: Web
         */
        @Serializable
        public data object Login : Screen(PageType.Login, null)

        /**
         * Tracked Screen: Order Confirmation
         * Source: Web
         */
        @Serializable
        public data object OrderConfirmation : Screen(PageType.OrderConfirmation, null)

        /**
         * Tracked Screen: Product Detail Page
         * Source: Web or native PDP
         */
        @Serializable
        public data object ProductDetailPage : Screen(PageType.ProductDetail, null)

        /**
         * Tracked Screen: Product Detail gallery subscreen
         * Source: native PDP
         */
        @Serializable
        public data object ProductDetailGallery : Screen(PageType.ProductDetail, "Gallery")

        /**
         * Tracked Screen: Product Detail reviews subscreen
         * Source: native PDP
         */
        @Serializable
        public data object ProductDetailReviews : Screen(PageType.ProductDetail, "Reviews")

        /**
         * Tracked Screen: Product Detail variant sheet
         * Source: native PDP
         */
        @Serializable
        public data object ProductDetailVariants : Screen(PageType.ProductDetail, "Variants")

        /**
         * Tracked Screen: Product Detail add to cart confirmation sheet
         * Source: native PDP
         */
        @Serializable
        public data object ProductDetailConfirmation : Screen(PageType.ProductDetail, "Confirmation")

        /**
         * Tracked Screen: Product Listing
         * Source: Web
         */
        @Serializable
        public data object ProductList : Screen(PageType.ProductListing, null)

        /**
         * Tracked Screen: Product Listing originated of a Search Result
         * Source: Web
         */
        @Serializable
        public data object ProductListOfSearchResult : Screen(PageType.SearchResult, null)

        /**
         * Tracked Screen: Customer Registration
         * Source: Web
         */
        @Serializable
        public data object Registration : Screen(PageType.CustomerRegistration, null)

        /**
         * Tracked Screen: Wishlist
         * Source: Web
         */
        @Serializable
        public data object Wishlist : Screen(PageType.Wishlist, null)
    }

    /**
     * Represents an overlay that the user has accessed.
     * @param category The category of the overlay that the user has accessed.
     * @param detail The detail of the overlay that the user has accessed.
     * @param scenario The scenario of the overlay that the user has accessed.
     */
    @Serializable
    public sealed class Overlay(
        public val category: String,
        public val detail: String,
        public val scenario: String?
    ) : View() {

        override fun describe(): Map<String, Any?> = super.describe() + mapOf(
            "category" to category,
            "scenario" to scenario,
            "detail" to detail,
        )

        /**
         * Tracked Overlay: ATT System Dialog
         * Scenario: Onboarding
         * Source: App
         */
        @Serializable
        public data object ATTSystemDialog : Overlay(
            category = "Att",
            detail = "AttOptIn",
            scenario = "Onboarding"
        )

        /**
         * Tracked Overlay: Catalog Scanner - Product
         * Source: App
         */
        @Serializable
        public data object CatalogScannerProductOverlay : Overlay(
            category = "CatalogScanner",
            detail = "CatalogScanner_Results",
            scenario = null
        )

        /**
         * Tracked Overlay: Push Promotion Layer
         * Scenario: Triggered by x ScreenViews
         * Source: App
         */
        @Serializable
        public data object PushPromotionLayerScreenViews : Overlay(
            category = "Push",
            detail = "PushInfoLayer",
            scenario = "ScreenViews"
        )

        /**
         * Tracked Overlay: Push Promotion Layer
         * Scenario: Triggered on Order Confirmation
         * Source: App
         */
        @Serializable
        public data object PushPromotionLayerOrderConfirmation : Overlay(
            category = "Push",
            detail = "PushInfoLayer",
            scenario = "OrderConfirmation"
        )

        /**
         * Tracked Overlay: Push Promotion Layer
         * Scenario: Triggered by webbridge event
         * Source: App
         */
        @Serializable
        public data object PushPromotionLayerPromotionWebBridge : Overlay(
            category = "Push",
            detail = "PushInfoLayer",
            scenario = "PromotionWebBridge"
        )

        /**
         * Tracked Overlay: Push Promotion Layer
         * Scenario: Triggered after Onboarding
         * Source: App
         */
        @Serializable
        public data object PushPromotionLayerOnboarding : Overlay(
            category = "Push",
            detail = "PushInfoLayer",
            scenario = "Onboarding"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: ScreenViews
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogScreenViews : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "ScreenViews"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: Onboarding
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogOnboarding : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "Onboarding"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: Order Confirmation
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogOrderConfirmation : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "OrderConfirmation"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: Triggered by webbridge event
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogPromotionWebBridge : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "PromotionWebBridge"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: Account
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogAccount : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "Account"
        )

        /**
         * Tracked Overlay: Push Promotion Layer
         * Scenario: Triggered in Inbox
         * Source: App
         */
        @Serializable
        public data object PushPromotionLayerInbox : Overlay(
            category = "Push",
            detail = "PushInfoLayer",
            scenario = "Inbox"
        )

        /**
         * Tracked Overlay: Push System Dialog
         * Scenario: Inbox
         * Source: App
         */
        @Serializable
        public data object PushSystemDialogInbox : Overlay(
            category = "Push",
            detail = "PushOptIn",
            scenario = "Inbox"
        )
    }

    /**
     * User scrolled promotion banner associated with an item into view
     * Source: native PDP
     */
    @Serializable
    public data class ProductDetailPromotion(
        val item: ECommerceItem,
        val creativeName: String? = null,
        val creativeSlot: String? = null,
        val promotionId: String? = null,
        val promotionName: String? = null,
        val additionalParameters: Map<String, CustomParameter> = emptyMap(),
    ) : View() {
        public constructor(
            itemId: String,
            itemName: String,
            coupon: String? = null,
            creativeName: String? = null,
            creativeSlot: String? = null,
            promotionId: String? = null,
            promotionName: String? = null,
            additionalParameters: Map<String, CustomParameter> = emptyMap(),
        ) : this(
            item = ECommerceItem(
                id = itemId,
                name = itemName,
                coupon = coupon,
            ),
            creativeName = creativeName,
            creativeSlot = creativeSlot,
            promotionId = promotionId,
            promotionName = promotionName,
            additionalParameters = additionalParameters,
        )

        override fun describe(): Map<String, Any?> = super.describe() +
            mapOf("item" to item) +
            additionalParameters.mapValues { it.value.value }
    }

    /**
     * User viewed a product
     * Source: native PDP
     */
    @Serializable
    public data class ProductDetailViewItem(
        val item: ECommerceItem,
        val additionalParameters: Map<String, CustomParameter> = emptyMap(),
    ) : View() {
        override fun describe(): Map<String, Any?> = super.describe() +
            mapOf("item" to item) +
            additionalParameters.mapValues { it.value.value }
    }
}
