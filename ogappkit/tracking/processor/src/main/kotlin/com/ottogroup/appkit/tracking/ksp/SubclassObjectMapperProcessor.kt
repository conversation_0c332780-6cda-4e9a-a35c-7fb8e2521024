package com.ottogroup.appkit.tracking.ksp

import com.google.devtools.ksp.processing.*
import com.google.devtools.ksp.symbol.*
import com.google.devtools.ksp.validate

private class SubclassObjectMapperProcessor(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) : SymbolProcessor {

    override fun process(resolver: Resolver): List<KSAnnotated> {
        val symbol =
            resolver.getSymbolsWithAnnotation("com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper")
        val ret = symbol.filter { !it.validate() }.toList()

        symbol
            .filter { it is KSClassDeclaration && it.validate() }
            .forEach {
                if (it is KSClassDeclaration) {
                    if (!it.modifiers.contains(Modifier.SEALED)) {
                        logger.error(
                            "@GenerateSubclassObjectMapper can only be applied to sealed classes or interfaces",
                            it
                        )
                    } else if (!hasCompanionObject(it)) {
                        logger.error(
                            "@GenerateSubclassObjectMapper requires a companion object in the annotated class",
                            it
                        )
                    } else {
                        it.accept(SubclassObjectMapperVisitor(), Unit)
                    }
                }
            }

        return ret
    }

    private fun hasCompanionObject(classDeclaration: KSClassDeclaration): Boolean {
        return classDeclaration.declarations
            .filterIsInstance<KSClassDeclaration>()
            .any { it.isCompanionObject }
    }

    inner class SubclassObjectMapperVisitor : KSVisitorVoid() {
        override fun visitClassDeclaration(classDeclaration: KSClassDeclaration, data: Unit) {
            val packageName = classDeclaration.packageName.asString()
            val className = classDeclaration.simpleName.asString()
            val extensionClass = classDeclaration.qualifiedName?.asString()
            val subclasses = findSubclasses(classDeclaration)
            generateCompanionExtension(
                packageName,
                className,
                extensionClass,
                classDeclaration.containingFile!!,
                subclasses
            )

        }

        private fun findSubclasses(clazz: KSClassDeclaration): List<KSClassDeclaration> {
            return clazz.declarations
                .filterIsInstance<KSClassDeclaration>()
                .filter {
                    (it.classKind == ClassKind.OBJECT || it.classKind == ClassKind.CLASS) &&
                        !it.isCompanionObject
                }
                .toList()
        }

        private fun generateCompanionExtension(
            packageName: String,
            className: String,
            extensionImport: String?,
            file: KSFile,
            subclasses: List<KSClassDeclaration>
        ) {
            val fileName = "${className}Extensions"
            codeGenerator.associateWithClasses(subclasses, packageName, fileName)
            val outputFile = codeGenerator.createNewFile(
                Dependencies(true, file),
                packageName,
                fileName
            )
            val code = buildString {
                appendLine("package $packageName")
                appendLine()
                extensionImport?.let {
                    appendLine("import $it")
                    appendLine()
                }
                appendLine("/**")
                appendLine(" * Generated extension function for $className")
                appendLine(" * Maps simple class names to instances")
                appendLine(" */")
                appendLine("public fun $className.Companion.valueOf(name: String): $className? {")
                appendLine("    return when (name) {")

                subclasses.forEach { subclass ->
                    val subclassName = subclass.simpleName.asString()
                    if (subclass.classKind == ClassKind.OBJECT) {
                        appendLine("        \"$subclassName\" -> $className.$subclassName")
                    }
                    // Note: We skip data classes since they require parameters
                }

                appendLine("        else -> null")
                appendLine("    }")
                appendLine("}")
                appendLine()
                appendLine("/**")
                appendLine(" * Generated function to get all available $className names")
                appendLine(" */")
                appendLine("public fun $className.Companion.availableNames(): List<String> {")
                appendLine("    return listOf(")

                val objectSubclasses = subclasses.filter { it.classKind == ClassKind.OBJECT }
                objectSubclasses.forEachIndexed { index, subclass ->
                    val subclassName = subclass.simpleName.asString()
                    val comma = if (index < objectSubclasses.size - 1) "," else ""
                    appendLine("        \"$subclassName\"$comma")
                }

                appendLine("    )")
                appendLine("}")
            }

            outputFile.writer().use { it.write(code) }
        }
    }
}

public class SubclassObjectMapperProcessorProvider : SymbolProcessorProvider {
    override fun create(environment: SymbolProcessorEnvironment): SymbolProcessor {
        return SubclassObjectMapperProcessor(environment.codeGenerator, environment.logger)
    }
}
