package com.ottogroup.appkit.tracking.ksp

import com.tschuchort.compiletesting.KotlinCompilation
import com.tschuchort.compiletesting.SourceFile
import com.tschuchort.compiletesting.configureKsp
import com.tschuchort.compiletesting.kspSourcesDir
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import org.jetbrains.kotlin.compiler.plugin.ExperimentalCompilerApi

@OptIn(ExperimentalCompilerApi::class)
class SubclassObjectMapperProcessorTest {

    @Test
    fun `test processor generates correct extension functions for sealed class with object subclasses`() {
        // 1. Create sample Kotlin code to process
        val kotlinSource = SourceFile.kotlin(
            "TestScreen.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            sealed class TestScreen {
                object Home : TestScreen()
                object Profile : TestScreen()
                object Settings : TestScreen()

                companion object
            }
            """
        )

        // 2. Create sample annotation
        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )

        // 3. Compile the code with our processor
        val compilation = createKspCompilation(listOf(kotlinSource, annotationSource))
        val result = compilation.compile()

        // 4. Check the compilation result
        assertEquals(KotlinCompilation.ExitCode.OK, result.exitCode)

        // 5. Find and verify the generated file using kspSourcesDir
        val generatedFilePath =
            compilation.kspSourcesDir.resolve("kotlin/com/example/test/TestScreenExtensions.kt")

        assertTrue("Generated file should exist at $generatedFilePath", generatedFilePath.exists())

        // 6. Verify the content of the generated file
        val generatedContent = generatedFilePath.readText()
        val expectedContent = """
        package com.example.test

        import com.example.test.TestScreen

        /**
         * Generated extension function for TestScreen
         * Maps simple class names to instances
         */
        public fun TestScreen.Companion.valueOf(name: String): TestScreen? {
            return when (name) {
                "Home" -> TestScreen.Home
                "Profile" -> TestScreen.Profile
                "Settings" -> TestScreen.Settings
                else -> null
            }
        }

        /**
         * Generated function to get all available TestScreen names
         */
        public fun TestScreen.Companion.availableNames(): List<String> {
            return listOf(
                "Home",
                "Profile",
                "Settings"
            )
        }

        """.trimIndent()

        assertEquals(
            expectedContent,
            generatedContent
        )
    }

    @Test
    fun `test processor generates correct extension functions for sealed interface with object subclasses`() {
        // 1. Create sample Kotlin code to process
        val kotlinSource = SourceFile.kotlin(
            "TestScreen.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            sealed interface TestScreen {
                object Home : TestScreen
                object Profile : TestScreen
                object Settings : TestScreen

                companion object
            }
            """
        )

        // 2. Create sample annotation
        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )

        // 3. Compile the code with our processor
        val compilation = createKspCompilation(listOf(kotlinSource, annotationSource))
        val result = compilation.compile()

        // 4. Check the compilation result
        assertEquals(KotlinCompilation.ExitCode.OK, result.exitCode)

        // 5. Find and verify the generated file using kspSourcesDir
        val generatedFilePath =
            compilation.kspSourcesDir.resolve("kotlin/com/example/test/TestScreenExtensions.kt")

        assertTrue("Generated file should exist at $generatedFilePath", generatedFilePath.exists())

        // 6. Verify the content of the generated file
        val generatedContent = generatedFilePath.readText()
        val expectedContent = """
        package com.example.test

        import com.example.test.TestScreen

        /**
         * Generated extension function for TestScreen
         * Maps simple class names to instances
         */
        public fun TestScreen.Companion.valueOf(name: String): TestScreen? {
            return when (name) {
                "Home" -> TestScreen.Home
                "Profile" -> TestScreen.Profile
                "Settings" -> TestScreen.Settings
                else -> null
            }
        }

        /**
         * Generated function to get all available TestScreen names
         */
        public fun TestScreen.Companion.availableNames(): List<String> {
            return listOf(
                "Home",
                "Profile",
                "Settings"
            )
        }

        """.trimIndent()

        assertEquals(
            expectedContent,
            generatedContent
        )
    }


    @Test
    fun `test processor handles mixed subclass types correctly`() {
        // 1. Create sample Kotlin code with both object and class subclasses
        val kotlinSource = SourceFile.kotlin(
            "MixedEvent.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            sealed class MixedEvent {
                object Click : MixedEvent()
                object Swipe : MixedEvent()
                data class Custom(val name: String) : MixedEvent()

                companion object
            }
            """
        )

        // 2. Create sample annotation
        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )

        // 3. Compile the code with our processor
        val compilation = createKspCompilation(listOf(kotlinSource, annotationSource))
        val result = compilation.compile()


        // 4. Check the compilation result
        assertEquals(KotlinCompilation.ExitCode.OK, result.exitCode)

        // 5. Find and verify the generated file using kspSourcesDir
        val generatedFilePath =
            compilation.kspSourcesDir.resolve("kotlin/com/example/test/MixedEventExtensions.kt")

        assertTrue("Generated file should exist at $generatedFilePath", generatedFilePath.exists())

        // 6. Verify the content of the generated file
        val generatedContent = generatedFilePath.readText()
        val expectedContent = """
        package com.example.test

        import com.example.test.MixedEvent

        /**
         * Generated extension function for MixedEvent
         * Maps simple class names to instances
         */
        public fun MixedEvent.Companion.valueOf(name: String): MixedEvent? {
            return when (name) {
                "Click" -> MixedEvent.Click
                "Swipe" -> MixedEvent.Swipe
                else -> null
            }
        }

        /**
         * Generated function to get all available MixedEvent names
         */
        public fun MixedEvent.Companion.availableNames(): List<String> {
            return listOf(
                "Click",
                "Swipe"
            )
        }

        """.trimIndent()

        assertEquals(
            expectedContent,
            generatedContent
        )
    }

    @Test
    fun `test processor with non-sealed class will fail`() {
        val kotlinSource = SourceFile.kotlin(
            "NonSealedClass.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            open class NonSealedClass {
                object SubObject1 : NonSealedClass()
                object SubObject2 : NonSealedClass()

                companion object
            }
            """
        )

        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )

        val result = createKspCompilation(listOf(kotlinSource, annotationSource)).compile()

        assertEquals(KotlinCompilation.ExitCode.COMPILATION_ERROR, result.exitCode)
    }


    @Test
    fun `test processor with non-sealed interface will fail`() {
        val kotlinSource = SourceFile.kotlin(
            "InterfaceClass.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            interface InterfaceClass {
                object SubObject1 : InterfaceClass
                object SubObject2 : InterfaceClass

                companion object
            }
            """
        )

        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )
        val result = createKspCompilation(listOf(kotlinSource, annotationSource)).compile()

        assertEquals(KotlinCompilation.ExitCode.COMPILATION_ERROR, result.exitCode)
    }

    @Test
    fun `test processor with non companion object will fail`() {
        val kotlinSource = SourceFile.kotlin(
            "InterfaceClass.kt", """
            package com.example.test

            import com.ottogroup.appkit.tracking.ksp.GenerateSubclassObjectMapper

            @GenerateSubclassObjectMapper
            sealed interface InterfaceClass {
                object SubObject1 : InterfaceClass
                object SubObject2 : InterfaceClass
            }
            """
        )

        val annotationSource = SourceFile.kotlin(
            "GenerateSubclassObjectMapper.kt", """
            package com.ottogroup.appkit.tracking.ksp

            @Target(AnnotationTarget.CLASS)
            @Retention(AnnotationRetention.SOURCE)
            annotation class GenerateSubclassObjectMapper
            """
        )

        val result = createKspCompilation(listOf(kotlinSource, annotationSource)).compile()

        assertEquals(KotlinCompilation.ExitCode.COMPILATION_ERROR, result.exitCode)
    }

    private fun createKspCompilation(sourceFiles: List<SourceFile>): KotlinCompilation {
        return KotlinCompilation().apply {
            sources = sourceFiles
            inheritClassPath = true

            configureKsp(true) {
                withCompilation = true
                symbolProcessorProviders.add(SubclassObjectMapperProcessorProvider())
            }
        }
    }
}
