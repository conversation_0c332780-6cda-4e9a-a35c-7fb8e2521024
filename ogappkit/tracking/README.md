# OGTracking

How to use the tracking SDK.

## Tracking Processor

The tracking module includes a Kotlin Symbol Processing (KSP) processor that automatically generates mapper extension functions for tracking event classes. This processor simplifies the mapping between string identifiers and typed event objects.

### How it works

The processor scans for classes annotated with `@GenerateSubclassObjectMapper` and generates extension functions for their companion objects. For each annotated class, it:

1. Identifies all object subclasses (excluding companion objects and regular classes that require parameters)
2. Generates a `valueOf(name: String)` extension function that maps string names to object instances
3. Generates an `availableNames()` extension function that returns all available object names

### Usage

Annotate your sealed class with `@GenerateSubclassObjectMapper`:

```kotlin
@GenerateSubclassObjectMapper
sealed class Screen {
    companion object;

    data object HomeScreen : Screen()
    data object ProfileScreen : Screen()
    data object SettingsScreen : Screen()
}
```

The processor will generate `ScreenExtensions.kt` with:

```kotlin
fun Screen.Companion.valueOf(name: String): Screen? {
    return when (name) {
        "HomeScreen" -> Screen.HomeScreen
        "ProfileScreen" -> Screen.ProfileScreen
        "SettingsScreen" -> Screen.SettingsScreen
        else -> null
    }
}

fun Screen.Companion.availableNames(): List<String> {
    return listOf("HomeScreen", "ProfileScreen", "SettingsScreen")
}
```

This is particularly useful for:
- Converting screen names to event objects
- Parsing analytics data containing screen identifiers
- Validating screen names dynamically

## Obtain the `OGTracking` instance

```kotlin
// Android
val ogTracking = OGAppKitSdk.init(androidApplication()).tracking()
```

```swift
// Swift
let ogTracking = OGAppKitSdk.shared.tracking()
```

## Configure it

The tracking SDK can be configured with the `OGTrackingConfig` class. The configuration class allows you to enable or disable tracking, set the log level, define the event mapping and more.

```kotlin
ogTracking.configure(
    OGTrackingConfig(
        trackingEnabled = true,
        viewEventMapping = mapOf(
            View.Screen.Basket to listOf(UrlMatcher(".*basket")),
            View.Screen.Wishlist to listOf(UrlMatcher(".*wishlist"),UrlMatcher(".*/favorites/")),
        ),
        logLevel = LogLevel.Assert,
        observeOverlaysForScreenEvents = true
    )
)

```

## Supported TrackingServices

Currently, we prepared support for a bunch of TrackingServices. The following list shows the current status of the integration:
- Adjust
- Airship (no event mapping)
- Firebase
- Glycerin (no event mapping)
- Snowplow
- Dynamic Yield

Adjust, Firebase and Snowplow have the event mapping already implemented, whereas the others are still missing the specific event mapping. Once we have the event mapping for the other services defined, we will need to implement the mapping in the respective TrackingService implementation class.

## Track Events

### General Event Tracking

The tracking SDK provides a way to track events in a structured and typesafe way.
All events are based on the `OGEvent` class, which is the base class for all events.
Generally we distinguish between `View` and `Interaction` events.
`View` events are used to track whenever the User has seen something, whereas `Interaction` events are used to track every interaction within the app or web.
There are some exceptions for custom Events that to not belong to the `View` or `Interaction` category e.g. status changes like Login or Search. But still these events are based on the `OGEvent` class.

The make use of highly typed events and every new event should follow the same pattern. This way we can ensure that every event is tracked in the same way and that we can easily add new TrackingServices in the future.

The event then simply can be tracked by calling the `track` method on the `OGTracking` instance.

```kotlin
ogTracking.track(View.Screen.Basket)
```

```swift
ogTracking.track(View.Screen.Basket())
```

### Url based Event Tracking

Since we are in a hybrid app environment, we can also track events based on the current URL. This is especially useful for tracking view events that directly relate to a webpage.
This is done by adding a `UrlMatcher` to the `ViewEventMapping` in the `OGTrackingConfig` class. The `UrlMatcher` is a simple regex that is used to match the current URL. If the current URL matches the regex, the view event is triggered.
This way we can track the view event for every web screen in the app without the need to add a tracking event for every screen manually.

### Event Mapping

Each TrackingService has its own internal definition of events.
To make it easier to track events in the app, we internally map the events to the specific TrackingService events.
This way we can track the same event in different TrackingServices without the need to call the TrackingService directly.
By having the event mapping in place, we can easily apply any custom logic to the specific TrackingServices events without the need to change the tracking calls in the app.
To map any event to a specific TrackingService event, we need to implement the mapping in the `OGTrackingService` class by overriding the `mapEvent` function.

#### Adjust

The Adjust tracking service is special in that the events that it tracks are identified by tokens that are unique per app. Therefore, we need to pass the event-to-token mapping into the configuration for the Adjust tracking service (via `OGTracking.onUpdateConfig`). Events are identified by partially matching their fully qualified name. The matching is pretty lenient: separator characters (dots or dollar signs) are stripped, case does not matter and matching only looks for the mapping string to be contained in the event type name, which allows combing similar events into a single token. Mapping rules must be unique. If an event can be mapped to more than one token, it is not tracked at all.

## Consent Management

Each TrackingService has its own way of handling consent. Therefore we provide a mechanism to handle the consent for each TrackingService separately.

## Global Context Enrichment

Nearly every TrackingService allows to enrich the tracking events with additional context information in a global way. Therefore we provide a mechanism to mimic this behavior in the tracking SDK.
Therefore you just call the `updateGlobalContext(id: OGTrackingServiceId, contextParameters: ContextParameter)` method on the `OGTracking` instance. This will take care that the context parameters will be stored locally and dispatched to the respective TrackingService.

# How to:

## Add a new Tracking event:

1. Define event on [Confluence page](https://mobilelab.atlassian.net/wiki/spaces/ACO/pages/3825860634/OG+App+Kit+Tracking+Events)
2. Add the event to the `OGEvent` sealed class.
3. Add event mapping to each TrackingService implementation.
4. Create new release of the tracking SDK.
5. Update the tracking SDK in the apps.

## Add a new TrackingService:

1. Add service name to OGTrackingServiceId
2. Add new package to services package.
3. Create a class that implements the `OGTrackingService` interface.
4. Add default event mapping to the `OGTrackingService` class.
5. Create an abstraction layer for the TrackingService that is shared with client apps. This abstraction layer should provide a way to track events and handle consent and configure global context.
6. Add "configure" method to the `OGTracking` class that will take care of configuring the TrackingService and registering it to the DI.
7. Create a new release of the tracking SDK.

## Configure TrackingService in app:

1. After new release, update the tracking SDK in the app.
2. Configure the TrackingService in the app by calling the `configure` method on the `OGTracking` instance.
3. Implement the new abstraction layer in the app and pass all the events, consent and global context to the 3rd party TrackingService sdk.

## Map event to specific TrackingService event:

1. See for general mapping [Confluence page](https://mobilelab.atlassian.net/wiki/spaces/ACO/pages/3825860634/OG+App+Kit+Tracking+Events)
2. Implement the `mapEvent` function in the `OGTrackingService` class.
3. If the mapping is generic, add the mapping to the `OGTrackingService` class.
4. If the mapping is specific to the app, add the mapping to abstraction layer implementation.

## Trigger event based on URL:

1. Create a pattern for the URL that should trigger the event.
2. Map the corresponding View event to the URL and configure it by setting the `viewEventMapping` in the `OGTrackingConfig` class.
3. Verify event is triggered by navigating to the URL.
