package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.config.CoordinatorConfig
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent
import kotlinx.coroutines.flow.Flow

/**
 * Delegate interface for providing push notification permission status.
 * Implement this on the platform side (especially iOS) to provide custom
 * push permission detection logic.
 */
public interface PushPermissionDelegate {
    /**
     * Asynchronously gets the current push notification permission status.
     * @return true if push notifications are enabled/granted, false otherwise
     */
    public suspend fun isPushOptInGranted(): Boolean
}

/**
 * The main entry point to Coordinator functionality. Obtain an instance from
 * the `OGAppKitSdk` object.
 */
public interface OGCoordinator {
    /**
     * Configures the Coordinator SDK. MUST be called before performing any other
     * operations.
     */
    public fun configure(config: CoordinatorConfig)

    /**
     * Configures the Coordinator SDK with behaviors as JSON string. MUST be called before performing any other
     * operations.
     */
    public fun configure(debounceMs: Long, behaviorsJson: String, pushOptInGranted: Boolean)

    /**
     * Sends a [CoordinatorEvent] to the Coordinator for processing.
     */
    public fun onEvent(event: CoordinatorEvent)

    /**
     * Gets a flow with the [Action] resulting from the events sent to the Coordinator.
     */
    public fun getActions(): Flow<Action>

    /**
     * Sets a delegate for push notification permission checking.
     * If set, this delegate will be used instead of the configured pushOptInGranted value.
     * This is especially useful on iOS where you can implement automatic permission detection.
     */
    public fun setPushPermissionDelegate(delegate: PushPermissionDelegate)
}
