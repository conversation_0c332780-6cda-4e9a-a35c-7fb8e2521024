package com.ottogroup.appkit.coordinator.model

import com.ottogroup.appkit.base.uri.UrlMatcher
import kotlinx.serialization.Serializable

@Serializable
public data class Behavior(
    /**
     * This MUST be fulfilled before any of the conditions are even evaluated.
     */
    val precondition: Precondition? = null,
    /**
     * If ANY condition is fulfilled, the action is performed
     */
    val conditions: List<Condition>,

    val action: Action,
    val id: String,
    val maxInvocations: Int? = null,
    val disabledUrls: List<UrlMatcher> = emptyList(),
    val minutesBetweenInvocations: Int? = null,
)
