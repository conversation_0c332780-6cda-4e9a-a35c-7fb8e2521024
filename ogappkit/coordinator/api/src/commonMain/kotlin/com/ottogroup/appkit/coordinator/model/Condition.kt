package com.ottogroup.appkit.coordinator.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
public sealed interface Condition {
    public val start: Int get() = 1 // 1-indexed because this is a count of events.
    public val period: Int get() = 1 // fire every time. values below 1 are meaningless.

    @Serializable
    @SerialName("appStarts")
    public data class AppStarts(
        override val start: Int = 1,
        override val period: Int = 1,
    ) : Condition {

        public fun isMet(totalAppStarts: Int): Boolean {
            return totalAppStarts >= start && (totalAppStarts - start) % period == 0
        }
    }

    @Serializable
    @SerialName("screenViews")
    public data class ScreenViews(
        override val start: Int = 1,
        override val period: Int = 1,
    ) : Condition {

        public fun isMet(totalScreenViews: Int): Boolean {
            return totalScreenViews >= start && (totalScreenViews - start) % period == 0
        }
    }

    @Serializable
    @SerialName("webBridgeCall")
    public data class WebBridgeCall(
        val webBridgeCallName: String,
    ) : Condition {

        override val start: Int = 1
        override val period: Int = 1

        public fun isMet(callName: String): Boolean {
            return callName == webBridgeCallName
        }
    }

    @Serializable
    @SerialName("trackingEvents")
    public data class TrackingEvents(
        val eventName: String,
        val countType: CountType = CountType.TOTAL,
        override val start: Int = 1,
        override val period: Int = 1,
    ) : Condition {

        @Serializable
        public enum class CountType {
            @SerialName("session")
            SESSION,

            @SerialName("total")
            TOTAL
        }

        public fun isMet(
            event: CoordinatorEvent.TrackingEvent,
            sessionCount: Int,
            totalCount: Int
        ): Boolean {
            val eventClassName = event.event::class.simpleName
            if (eventClassName != eventName) return false

            val count = when (countType) {
                CountType.SESSION -> sessionCount
                CountType.TOTAL -> totalCount
            }

            return count >= start && (count - start) % period == 0
        }
    }
}
