package com.ottogroup.appkit.coordinator.model

import com.ottogroup.appkit.tracking.event.OGEvent
import kotlinx.serialization.Serializable

public sealed interface CoordinatorEvent {
    public data class ScreenView(val url: String?) : CoordinatorEvent
    public data object AppStart : CoordinatorEvent
    public data class WebBridgeCall(val name: String) : CoordinatorEvent

    @Serializable
    public data class TrackingEvent(val event: OGEvent) : CoordinatorEvent
}
