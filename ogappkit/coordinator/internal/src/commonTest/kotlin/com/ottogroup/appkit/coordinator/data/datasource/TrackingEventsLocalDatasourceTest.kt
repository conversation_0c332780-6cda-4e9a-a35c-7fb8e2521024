package com.ottogroup.appkit.coordinator.data.datasource

import com.ottogroup.appkit.coordinator.model.TrackingEventsHolder
import com.ottogroup.appkit.coordinator.utils.createTestTrackingEvent
import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest

class TrackingEventsLocalDatasourceTest {

    @Test
    fun `saveBatchEvents should add new event when events map is empty`() = runTest {
        val stateFlow = MutableStateFlow(TrackingEventsHolder(emptyMap()))
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val testEvent = createTestTrackingEvent("test-event")

        datasource.saveBatchEvents(mapOf(testEvent to 3))

        val expectedData = TrackingEventsHolder(
            mapOf("test-event" to 3)
        )
        assertEquals(expectedData, stateFlow.value)
    }

    @Test
    fun `saveBatchEvents should add multiple new events`() = runTest {
        val stateFlow = MutableStateFlow(TrackingEventsHolder(emptyMap()))
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val event1 = createTestTrackingEvent("event-1")
        val event2 = createTestTrackingEvent("event-2")

        datasource.saveBatchEvents(
            mapOf(
                event1 to 2,
                event2 to 5
            )
        )

        val result = stateFlow.value
        assertEquals(2, result.events.size)
        assertEquals(2, result.events["event-1"])
        assertEquals(5, result.events["event-2"])
    }

    @Test
    fun `saveBatchEvents should update existing event count`() = runTest {
        val testEvent = createTestTrackingEvent("existing-event")
        val initialData = TrackingEventsHolder(
            mapOf("existing-event" to 10)
        )
        val stateFlow = MutableStateFlow(initialData)
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)

        datasource.saveBatchEvents(mapOf(testEvent to 5))

        val expectedData = TrackingEventsHolder(
            mapOf("existing-event" to 15)
        )
        assertEquals(expectedData, stateFlow.value)
    }

    @Test
    fun `saveBatchEvents should handle mix of existing and new events`() = runTest {
        val existingEvent = createTestTrackingEvent("existing")
        val newEvent = createTestTrackingEvent("new")
        val anotherExistingEvent = createTestTrackingEvent("another-existing")

        val initialData = TrackingEventsHolder(
            mapOf(
                "existing" to 7,
                "another-existing" to 3
            )
        )
        val stateFlow = MutableStateFlow(initialData)
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)

        datasource.saveBatchEvents(
            mapOf(
                existingEvent to 2,
                newEvent to 4,
                anotherExistingEvent to 1
            )
        )

        val result = stateFlow.value
        assertEquals(3, result.events.size)
        assertEquals(9, result.events["existing"])
        assertEquals(4, result.events["another-existing"])
        assertEquals(4, result.events["new"])
    }

    @Test
    fun `getEventOccurrences should return 0 for non-existing event`() = runTest {
        val dataStore = mockDataStore(TrackingEventsHolder(emptyMap()))
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val testEvent = createTestTrackingEvent("non-existing")

        val result = datasource.getEventOccurrences(testEvent)

        assertEquals(0, result)
    }

    @Test
    fun `getEventOccurrences should return correct count for existing event`() = runTest {
        val testEvent = createTestTrackingEvent("test-event")
        val initialData = TrackingEventsHolder(
            mapOf("test-event" to 42)
        )
        val dataStore = mockDataStore(initialData)
        val datasource = TrackingEventsLocalDatasource(dataStore)

        val result = datasource.getEventOccurrences(testEvent)

        assertEquals(42, result)
    }

    @Test
    fun `getEventOccurrences should return correct count when multiple events exist`() = runTest {
        val event1 = createTestTrackingEvent("event-1")
        val event2 = createTestTrackingEvent("event-2")
        val event3 = createTestTrackingEvent("event-3")

        val initialData = TrackingEventsHolder(
            mapOf(
                "event-1" to 10,
                "event-2" to 25,
                "event-3" to 5
            )
        )
        val dataStore = mockDataStore(initialData)
        val datasource = TrackingEventsLocalDatasource(dataStore)

        assertEquals(10, datasource.getEventOccurrences(event1))
        assertEquals(25, datasource.getEventOccurrences(event2))
        assertEquals(5, datasource.getEventOccurrences(event3))
    }
}
