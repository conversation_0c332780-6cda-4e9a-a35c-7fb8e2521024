package com.ottogroup.appkit.coordinator.utils

import com.ottogroup.appkit.base.uri.UrlMatcher
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.Behavior
import com.ottogroup.appkit.coordinator.model.Condition
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent
import com.ottogroup.appkit.coordinator.model.Precondition
import com.ottogroup.appkit.tracking.event.GenericEvent

internal fun createTestBehavior(
    id: String = "test-behavior",
    conditions: List<Condition> = listOf(Condition.AppStarts(1, 1)),
    action: Action = Action.Navigation("test://url"),
    precondition: Precondition? = null,
    disabledUrls: List<UrlMatcher> = emptyList(),
    maxInvocations: Int? = null,
    minutesBetweenInvocations: Int? = null
) = Behavior(
    id = id,
    conditions = conditions,
    action = action,
    precondition = precondition,
    disabledUrls = disabledUrls,
    maxInvocations = maxInvocations,
    minutesBetweenInvocations = minutesBetweenInvocations
)

internal fun createTestTrackingEvent(name: String) =
    CoordinatorEvent.TrackingEvent(GenericEvent(name))
