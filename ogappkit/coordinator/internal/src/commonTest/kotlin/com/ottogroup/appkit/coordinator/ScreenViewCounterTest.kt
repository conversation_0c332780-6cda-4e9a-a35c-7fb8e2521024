package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class ScreenViewCounterTest {

    @Test
    fun `incrementCount should return 1 for first increment of new behavior ID`() = runTest {
        val dataStore = mockDataStore(ScreenViewCounts())
        val counter = ScreenViewCounter(dataStore)

        val result = counter.incrementCount("behavior1")

        assertEquals(1, result)
    }

    @Test
    fun `incrementCount should return incremented count for existing behavior ID`() = runTest {
        val initialCounts = ScreenViewCounts(screenViewCounts = mapOf("behavior1" to 5))
        val dataStore = mockDataStore(initialCounts)
        val counter = ScreenViewCounter(dataStore)

        val result = counter.incrementCount("behavior1")

        assertEquals(6, result)
    }

    @Test
    fun `incrementCount should handle multiple different behavior IDs independently`() = runTest {
        val dataStore = mockDataStore(ScreenViewCounts())
        val counter = ScreenViewCounter(dataStore)

        val result1 = counter.incrementCount("behavior1")
        val result2 = counter.incrementCount("behavior2")
        val result3 = counter.incrementCount("behavior1")

        assertEquals(1, result1)
        assertEquals(1, result2)
        assertEquals(2, result3)
    }

    @Test
    fun `incrementCount should update dataStore with correct counts for multiple behaviors`() = runTest {
        val stateFlow = MutableStateFlow(ScreenViewCounts())
        val dataStore = mockDataStore(stateFlow)
        val counter = ScreenViewCounter(dataStore)

        counter.incrementCount("behavior1")
        counter.incrementCount("behavior2")
        counter.incrementCount("behavior1")
        counter.incrementCount("behavior3")

        val expectedCounts = mapOf(
            "behavior1" to 2,
            "behavior2" to 1,
            "behavior3" to 1
        )
        assertEquals(expectedCounts, stateFlow.value.screenViewCounts)
    }
}
