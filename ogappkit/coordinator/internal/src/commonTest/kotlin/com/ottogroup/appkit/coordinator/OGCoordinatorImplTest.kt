package com.ottogroup.appkit.coordinator

import app.cash.turbine.test
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.LifecycleEvent
import com.ottogroup.appkit.base.uri.UrlMatcher
import com.ottogroup.appkit.coordinator.config.CoordinatorConfig
import com.ottogroup.appkit.coordinator.config.CoordinatorConfigProvider
import com.ottogroup.appkit.coordinator.data.TrackingEventsRepository
import com.ottogroup.appkit.coordinator.data.datasource.TrackingEventsLocalDatasource
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.Condition
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent
import com.ottogroup.appkit.coordinator.model.Precondition
import com.ottogroup.appkit.coordinator.model.TrackingEventsHolder
import com.ottogroup.appkit.coordinator.utils.createTestBehavior
import com.ottogroup.appkit.coordinator.utils.mockDataStore
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.Flow

@OptIn(ExperimentalCoroutinesApi::class)
class OGCoordinatorImplTest {

    private fun createTestCoordinator(
        config: CoordinatorConfig = CoordinatorConfig(),
        testScope: TestScope,
        autoConfigure: Boolean = true
    ): OGCoordinatorImpl {
        val configProvider = CoordinatorConfigProvider()
        if (autoConfigure) {
            configProvider.update(config)
        }
        val appStartCounter = AppStartCounter(mockDataStore(PersistableEventCount()))
        val screenViewCounter = ScreenViewCounter(mockDataStore(ScreenViewCounts()))
        val behaviorInvocationsCounter = BehaviorInvocationsCounter(mockDataStore(BehaviorInvocationData()))
        val behaviorQueue = BehaviorQueue(configProvider, behaviorInvocationsCounter, testScope.backgroundScope)
        val trackingDealer = OGTrackingDealer(
            enabledFlow = flowOf(true),
            userPropertyContributors = emptyList(),
            consent = createMockConsent(),
            coroutineScope = testScope.backgroundScope
        )

        val trackingEventsRepository = TrackingEventsRepository(
            datasource = createMockTrackingEventsLocalDatasource(),
            coroutineScope = testScope.backgroundScope
        )

        val applicationLifecycleProvider = object : ApplicationLifecycleProvider {
            override val lifecycle = MutableStateFlow(LifecycleEvent.AppInForeground)
        }

        return OGCoordinatorImpl(
            configProvider = configProvider,
            appStartCounter = appStartCounter,
            screenViewCounter = screenViewCounter,
            behaviorQueue = behaviorQueue,
            trackingDealer = trackingDealer,
            trackingEventsRepository = trackingEventsRepository,
            applicationLifecycleProvider = applicationLifecycleProvider,
            coroutineScope = testScope.backgroundScope
        )
    }

    private fun createMockConsent() = object : OGTrackingConsent {
        override fun consentForService(serviceId: OGTrackingServiceId): Flow<Boolean> {
            return flowOf(true)
        }

        override fun setGlobalConsent(consent: Boolean) {}

        override fun setConsentsForServices(consents: Map<OGTrackingServiceId, Boolean>, replacePrevious: Boolean) {}
    }

    private fun createMockTrackingEventsLocalDatasource(): TrackingEventsLocalDatasource {
        return TrackingEventsLocalDatasource(
            mockDataStore(TrackingEventsHolder(emptyMap()))
        )
    }

    @Test
    fun `configure sends AppStart event automatically on first configuration`() = runTest {
        val behavior = createTestBehavior(
            id = "app-start-behavior",
            conditions = listOf(Condition.AppStarts(start = 1, period = 1))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this, autoConfigure = false)

        coordinator.getActions().test {
            coordinator.configure(config)
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `configure does not send AppStart event on subsequent configurations`() = runTest {
        val behavior = createTestBehavior(
            id = "app-start-behavior",
            conditions = listOf(Condition.AppStarts(start = 1, period = 1))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this, autoConfigure = false)

        coordinator.getActions().test {
            // First configure should trigger AppStart
            coordinator.configure(config)
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())

            // Second configure should not trigger AppStart again
            coordinator.configure(config)
            advanceTimeBy(150)
            expectNoEvents()
        }
    }

    @Test
    fun `onEvent ScreenView triggers behaviors with ScreenViews conditions`() = runTest {
        val behavior = createTestBehavior(
            id = "screen-view-behavior",
            conditions = listOf(Condition.ScreenViews(start = 1, period = 1))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.ScreenView("test://screen"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `onEvent ScreenView sanitizes url before checking behaviors`() = runTest {
        val behavior = createTestBehavior(
            id = "screen-view-behavior",
            conditions = listOf(Condition.ScreenViews(start = 1, period = 1)),
            disabledUrls = listOf(UrlMatcher("test://normalized"))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.ScreenView("test://normalized?param=value#fragment"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `onEvent ScreenView ignores duplicate urls after sanitization`() = runTest {
        val behavior = createTestBehavior(
            id = "screen-view-behavior",
            conditions = listOf(Condition.ScreenViews(start = 1, period = 1))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.ScreenView("test://screen"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())

            coordinator.onEvent(CoordinatorEvent.ScreenView("test://screen?param=value#fragment"))
            advanceTimeBy(150)
            expectNoEvents()

            coordinator.onEvent(CoordinatorEvent.ScreenView("test://different"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `onEvent ScreenView respects disabledUrls`() = runTest {
        val behavior = createTestBehavior(
            id = "screen-view-behavior",
            conditions = listOf(Condition.ScreenViews(start = 1, period = 1)),
            disabledUrls = listOf(UrlMatcher("disabled://.*"))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.ScreenView("disabled://screen"))
            advanceTimeBy(150)
            expectNoEvents()

            coordinator.onEvent(CoordinatorEvent.ScreenView("allowed://screen"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `onEvent WebBridgeCall triggers behaviors with WebBridgeCall conditions`() = runTest {
        val behavior = createTestBehavior(
            id = "bridge-call-behavior",
            conditions = listOf(Condition.WebBridgeCall("testCall"))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.WebBridgeCall("testCall"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `onEvent WebBridgeCall ignores non-matching call names`() = runTest {
        val behavior = createTestBehavior(
            id = "bridge-call-behavior",
            conditions = listOf(Condition.WebBridgeCall("expectedCall"))
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.WebBridgeCall("differentCall"))
            advanceTimeBy(150)
            expectNoEvents()

            coordinator.onEvent(CoordinatorEvent.WebBridgeCall("expectedCall"))
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `behaviors with PUSH_ENABLED precondition only execute when push is enabled`() = runTest {
        val behavior = createTestBehavior(
            id = "push-enabled-behavior",
            conditions = listOf(Condition.AppStarts(1, 1)),
            precondition = Precondition.PUSH_ENABLED
        )
        val config = CoordinatorConfig(
            behaviors = listOf(behavior),
            pushOptInGranted = false,
            debounceMs = 100
        )
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            advanceTimeBy(150)
            expectNoEvents()
        }

        coordinator.configure(config.copy(pushOptInGranted = true))

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.AppStart)
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `behaviors with PUSH_DISABLED precondition only execute when push is disabled`() = runTest {
        val behavior = createTestBehavior(
            id = "push-disabled-behavior",
            conditions = listOf(Condition.AppStarts(1, 1)),
            precondition = Precondition.PUSH_DISABLED
        )
        val config = CoordinatorConfig(
            behaviors = listOf(behavior),
            pushOptInGranted = true,
            debounceMs = 100
        )
        val coordinator = createTestCoordinator(config, this)

        coordinator.getActions().test {
            advanceTimeBy(150)
            expectNoEvents()
        }

        coordinator.configure(config.copy(pushOptInGranted = false))

        coordinator.getActions().test {
            coordinator.onEvent(CoordinatorEvent.AppStart)
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }

    @Test
    fun `behaviors without precondition always execute when conditions are met`() = runTest {
        val behavior = createTestBehavior(
            id = "no-precondition-behavior",
            conditions = listOf(Condition.AppStarts(1, 1)),
            precondition = null
        )
        val config = CoordinatorConfig(behaviors = listOf(behavior), debounceMs = 100)
        val coordinator = createTestCoordinator(config, this, autoConfigure = false)

        coordinator.getActions().test {
            coordinator.configure(config)
            advanceTimeBy(150)
            assertEquals(Action.Navigation("test://url"), awaitItem())
        }
    }
}
