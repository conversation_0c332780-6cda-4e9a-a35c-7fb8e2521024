package com.ottogroup.appkit.coordinator.data

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.coordinator.data.datasource.TrackingEventsLocalDatasource
import com.ottogroup.appkit.coordinator.model.TrackingEventsHolder
import com.ottogroup.appkit.coordinator.utils.createTestTrackingEvent
import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class TrackingEventsRepositoryTest {

    private fun createRepository(
        testScope: TestScope,
        initialData: TrackingEventsHolder = TrackingEventsHolder(emptyMap())
    ): TrackingEventsRepository {
        val stateFlow = MutableStateFlow(initialData)
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        return TrackingEventsRepository(datasource, testScope.backgroundScope)
    }

    @Test
    fun `addSessionEvent should increment session count for new event`() = runTest {
        val repository = createRepository(this)
        val event = createTestTrackingEvent("test-event")

        repository.addSessionEvent(event)

        assertEquals(1, repository.getSessionOccurrences(event))
    }

    @Test
    fun `addSessionEvent should increment session count for existing event`() = runTest {
        val repository = createRepository(this)
        val event = createTestTrackingEvent("test-event")

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        assertEquals(3, repository.getSessionOccurrences(event))
    }

    @Test
    fun `addSessionEvent should handle multiple different events independently`() = runTest {
        val repository = createRepository(this)
        val event1 = createTestTrackingEvent("event-1")
        val event2 = createTestTrackingEvent("event-2")

        repository.addSessionEvent(event1)
        repository.addSessionEvent(event2)
        repository.addSessionEvent(event1)

        assertEquals(2, repository.getSessionOccurrences(event1))
        assertEquals(1, repository.getSessionOccurrences(event2))
    }

    @Test
    fun `getTotalOccurrences should return sum of persistent and pending counts`() = runTest {
        val event = createTestTrackingEvent("test-event")
        val initialData = TrackingEventsHolder(
            mapOf("test-event" to 10)
        )
        val repository = createRepository(this, initialData)

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        val result = repository.getTotalOccurrences(event)

        assertEquals(13, result)
    }

    @Test
    fun `getTotalOccurrences should return only persistent count when no pending events`() = runTest {
        val event = createTestTrackingEvent("test-event")
        val initialData = TrackingEventsHolder(
            mapOf("test-event" to 5)
        )
        val repository = createRepository(this, initialData)

        val result = repository.getTotalOccurrences(event)

        assertEquals(5, result)
    }

    @Test
    fun `getTotalOccurrences should return only pending count when no persistent events`() = runTest {
        val repository = createRepository(this)
        val event = createTestTrackingEvent("test-event")

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        val result = repository.getTotalOccurrences(event)

        assertEquals(2, result)
    }

    @Test
    fun `onAppBackground should merge pending events with existing persistent events`() = runTest {
        val event = createTestTrackingEvent("merge-test")
        val initialData = TrackingEventsHolder(
            mapOf("merge-test" to 3)
        )
        val stateFlow = MutableStateFlow(initialData)
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val repository = TrackingEventsRepository(datasource, this.backgroundScope)

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        testScheduler.advanceUntilIdle()
        repository.onAppBackground()

        assertEquals(1, stateFlow.value.events.size)
        assertEquals(5, stateFlow.value.events["merge-test"])
    }

    @Test
    fun `multiple flush cycles should work correctly`() = runTest {
        val stateFlow = MutableStateFlow(TrackingEventsHolder(emptyMap()))
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val repository = TrackingEventsRepository(datasource, this.backgroundScope)
        val event = createTestTrackingEvent("multi-flush-test")

        repository.addSessionEvent(event)
        advanceTimeBy(6_000L)
        assertEquals(1, stateFlow.value.events["multi-flush-test"])

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)
        advanceTimeBy(6_000L)
        assertEquals(3, stateFlow.value.events["multi-flush-test"])

        repository.addSessionEvent(event)
        advanceTimeBy(6_000L)
        assertEquals(4, stateFlow.value.events["multi-flush-test"])
    }

    @Test
    fun `session events should persist across flush operations`() = runTest {
        val repository = createRepository(this)
        val event = createTestTrackingEvent("session-persist-test")

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        repository.onAppBackground()

        assertEquals(2, repository.getSessionOccurrences(event))
    }

    @Test
    fun `concurrent addSessionEvent calls should be handled correctly`() = runTest {
        val repository = createRepository(this)
        val event1 = createTestTrackingEvent("concurrent-1")
        val event2 = createTestTrackingEvent("concurrent-2")

        repository.addSessionEvent(event1)
        awaitAll(
            async { repository.addSessionEvent(event1) },
            async { repository.addSessionEvent(event2) },
            async { repository.addSessionEvent(event1) },
            async { repository.addSessionEvent(event2) }
        )

        assertEquals(3, repository.getSessionOccurrences(event1))
        assertEquals(2, repository.getSessionOccurrences(event2))
    }

    @Test
    fun `batching should reset timer after each flush`() = runTest {
        val stateFlow = MutableStateFlow(TrackingEventsHolder(emptyMap()))
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val repository = TrackingEventsRepository(datasource, this.backgroundScope)
        val event = createTestTrackingEvent("timer-reset-test")

        repository.addSessionEvent(event)
        advanceTimeBy(6_000L) // Should trigger flush
        assertEquals(1, stateFlow.value.events["timer-reset-test"])

        repository.addSessionEvent(event)
        advanceTimeBy(3_000L) // Not enough time for another flush
        assertEquals(1, stateFlow.value.events["timer-reset-test"])

        advanceTimeBy(3_000L) // Now enough time has passed
        assertEquals(2, stateFlow.value.events["timer-reset-test"])
    }

    @Test
    fun `getTotalOccurrences should work correctly after multiple operations`() = runTest {
        val event = createTestTrackingEvent("complex-test")
        val initialData = TrackingEventsHolder(
            mapOf("complex-test" to 7)
        )
        val stateFlow = MutableStateFlow(initialData)
        val dataStore = mockDataStore(stateFlow)
        val datasource = TrackingEventsLocalDatasource(dataStore)
        val repository = TrackingEventsRepository(datasource, this.backgroundScope)

        // Should start with persistent count
        assertEquals(7, repository.getTotalOccurrences(event))

        // Add some session events
        repository.addSessionEvent(event)
        repository.addSessionEvent(event)
        assertEquals(9, repository.getTotalOccurrences(event))

        // Flush to persistence
        repository.onAppBackground()
        assertEquals(9, repository.getTotalOccurrences(event))

        // Add more session events
        repository.addSessionEvent(event)
        assertEquals(10, repository.getTotalOccurrences(event))

        // Another flush
        repository.onAppBackground()
        assertEquals(10, repository.getTotalOccurrences(event))
    }

    @Test
    fun `flush should handle datastore errors gracefully and retry events`() = runTest {
        val stateFlow = MutableStateFlow(TrackingEventsHolder(emptyMap()))
        var attemptCount = 0

        val failingThenRecoveringDataStore = object : DataStore<TrackingEventsHolder> {
            override val data = stateFlow
            override suspend fun updateData(transform: suspend (t: TrackingEventsHolder) -> TrackingEventsHolder): TrackingEventsHolder {
                attemptCount++
                if (attemptCount == 1) {
                    throw RuntimeException("Simulated storage failure on first attempt")
                }
                // Second attempt succeeds
                stateFlow.value = transform(stateFlow.value)
                return stateFlow.value
            }
        }

        val datasource = TrackingEventsLocalDatasource(failingThenRecoveringDataStore)
        val repository = TrackingEventsRepository(datasource, this.backgroundScope)
        val event = createTestTrackingEvent("error-retry-test")

        repository.addSessionEvent(event)
        repository.addSessionEvent(event)

        // Trigger first flush which should fail
        repository.onAppBackground()

        // Verify first attempt failed - events should still be in session (and re-added to pending)
        assertEquals(2, repository.getSessionOccurrences(event))
        assertEquals(1, attemptCount)
        assertEquals(0, stateFlow.value.events.size)

        // Trigger second flush
        repository.onAppBackground()

        // Verify second attempt succeeded
        assertEquals(2, attemptCount)
        assertEquals(1, stateFlow.value.events.size)
        assertEquals(2, stateFlow.value.events["error-retry-test"])
        assertEquals(2, repository.getSessionOccurrences(event))
        assertEquals(2, repository.getTotalOccurrences(event))
    }
}
