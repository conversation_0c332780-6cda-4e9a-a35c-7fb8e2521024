package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class AppStartCounterTest {

    @Test
    fun `incrementCount should return 1 for first increment`() = runTest {
        val dataStore = mockDataStore(PersistableEventCount())
        val counter = AppStartCounter(dataStore)

        val result = counter.incrementCount()

        assertEquals(1, result)
    }

    @Test
    fun `incrementCount should return incremented count for existing count`() = runTest {
        val initialCount = PersistableEventCount(count = 5)
        val dataStore = mockDataStore(initialCount)
        val counter = AppStartCounter(dataStore)

        val result = counter.incrementCount()

        assertEquals(6, result)
    }

    @Test
    fun `incrementCount should persist counts between calls`() = runTest {
        val stateFlow = MutableStateFlow(PersistableEventCount())
        val dataStore = mockDataStore(stateFlow)
        val counter = AppStartCounter(dataStore)

        val result1 = counter.incrementCount()
        val result2 = counter.incrementCount()
        val result3 = counter.incrementCount()

        assertEquals(1, result1)
        assertEquals(2, result2)
        assertEquals(3, result3)
        assertEquals(3, stateFlow.value.count)
    }

    @Test
    fun `incrementCount should update dataStore with correct count`() = runTest {
        val stateFlow = MutableStateFlow(PersistableEventCount())
        val dataStore = mockDataStore(stateFlow)
        val counter = AppStartCounter(dataStore)

        counter.incrementCount()
        counter.incrementCount()

        assertEquals(2, stateFlow.value.count)
    }
}
