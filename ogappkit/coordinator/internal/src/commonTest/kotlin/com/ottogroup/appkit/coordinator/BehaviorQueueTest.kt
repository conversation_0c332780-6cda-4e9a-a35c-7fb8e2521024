package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.config.CoordinatorConfig
import com.ottogroup.appkit.coordinator.config.CoordinatorConfigProvider
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.utils.TestClock
import com.ottogroup.appkit.coordinator.utils.createTestBehavior
import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class BehaviorQueueTest {

    @Test
    fun `enqueue should emit action after debounce delay`() = runTest {
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 100))
        }
        val invocationsCounter = BehaviorInvocationsCounter(
            mockDataStore(BehaviorInvocationData())
        )
        val queue = BehaviorQueue(configProvider, invocationsCounter, this)

        val behavior = createTestBehavior("test1")
        val actions = mutableListOf<Action>()

        val collectJob = launch {
            queue.actionsFlow.toList(actions)
        }

        queue.enqueue(behavior, priority = 1)

        assertEquals(0, actions.size)
        advanceTimeBy(101)
        assertEquals(1, actions.size)
        assertEquals(behavior.action, actions[0])

        collectJob.cancel()
    }

    @Test
    fun `enqueue should select behavior with lowest priority when multiple are enqueued`() = runTest {
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 100))
        }
        val invocationsCounter = BehaviorInvocationsCounter(
            mockDataStore(BehaviorInvocationData())
        )
        val queue = BehaviorQueue(configProvider, invocationsCounter, this)

        val behavior1 = createTestBehavior(id = "test1", action = Action.Navigation("url1"))
        val behavior2 = createTestBehavior(id = "test2", action = Action.Navigation("url2"))
        val behavior3 = createTestBehavior(id = "test3", action = Action.Navigation("url3"))
        val actions = mutableListOf<Action>()

        val collectJob = launch {
            queue.actionsFlow.toList(actions)
        }

        queue.enqueue(behavior1, priority = 3)
        queue.enqueue(behavior2, priority = 1)
        queue.enqueue(behavior3, priority = 2)

        advanceTimeBy(101)

        assertEquals(1, actions.size)
        assertEquals(behavior2.action, actions[0])

        collectJob.cancel()
    }

    @Test
    fun `enqueue should skip behavior when max invocations reached`() = runTest {
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 50))
        }
        val invocationsCounter = BehaviorInvocationsCounter(
            mockDataStore(
                BehaviorInvocationData(
                    invocations = mapOf("test1" to InvocationData(count = 3, lastInvocation = 1000))
                )
            )
        )
        val queue = BehaviorQueue(configProvider, invocationsCounter, this)

        val behavior = createTestBehavior("test1", maxInvocations = 3)
        val actions = mutableListOf<Action>()

        val collectJob = launch {
            queue.actionsFlow.toList(actions)
        }

        queue.enqueue(behavior, priority = 1)

        advanceTimeBy(100)
        assertEquals(0, actions.size)
        collectJob.cancel()
    }

    @Test
    fun `enqueue should skip behavior when time between invocations not elapsed`() = runTest {
        val testClock = TestClock(10000)
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 50))
        }
        val invocationsCounter = BehaviorInvocationsCounter(
            mockDataStore(
                BehaviorInvocationData(
                    invocations = mapOf("test1" to InvocationData(count = 1, lastInvocation = 8000))
                )
            ),
            testClock
        )
        val queue = BehaviorQueue(configProvider, invocationsCounter, this, testClock)

        val behavior = createTestBehavior("test1", minutesBetweenInvocations = 5)
        val actions = mutableListOf<Action>()

        val collectJob = launch {
            queue.actionsFlow.toList(actions)
        }

        queue.enqueue(behavior, priority = 1)

        advanceTimeBy(100)
        assertEquals(0, actions.size)
        collectJob.cancel()
    }

    @Test
    fun `enqueue should track invocation when behavior is executed`() = runTest {
        val testClock = TestClock(5000)
        val dataStore = mockDataStore(BehaviorInvocationData())
        val invocationsCounter = BehaviorInvocationsCounter(dataStore, testClock)
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 50))
        }
        val queue = BehaviorQueue(configProvider, invocationsCounter, this, testClock)

        val behavior = createTestBehavior("test1")

        queue.enqueue(behavior, priority = 1)
        advanceTimeBy(100)

        val invocationData = invocationsCounter.getInvocationData("test1")
        assertEquals(1, invocationData.count)
        assertEquals(5000L, invocationData.lastInvocation)
    }

    @Test
    fun `enqueue should clear queue after executing behavior`() = runTest {
        val configProvider = CoordinatorConfigProvider().apply {
            update(CoordinatorConfig(debounceMs = 100))
        }
        val invocationsCounter = BehaviorInvocationsCounter(
            mockDataStore(BehaviorInvocationData())
        )
        val queue = BehaviorQueue(configProvider, invocationsCounter, this)

        val behavior1 = createTestBehavior(id = "test1", action = Action.Navigation("url1"))
        val behavior2 = createTestBehavior(id = "test2", action = Action.Navigation("url2"))
        val actions = mutableListOf<Action>()

        val collectJob = launch {
            queue.actionsFlow.toList(actions)
        }

        queue.enqueue(behavior1, priority = 2)
        queue.enqueue(behavior2, priority = 1)

        advanceTimeBy(150)
        assertEquals(1, actions.size)
        assertEquals(behavior2.action, actions[0])

        val behavior3 = createTestBehavior(id = "test3", action = Action.Navigation("url3"))
        queue.enqueue(behavior3, priority = 1)

        advanceTimeBy(150)
        assertEquals(2, actions.size)
        assertEquals(behavior3.action, actions[1])

        collectJob.cancel()
    }
}
