package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.utils.TestClock
import com.ottogroup.appkit.coordinator.utils.mockDataStore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class BehaviorInvocationsCounterTest {
    @Test
    fun `getInvocationData should return existing data for tracked behavior ID`() = runTest {
        val existingData = InvocationData(count = 5, lastInvocation = 2000L)
        val initialData = BehaviorInvocationData(
            invocations = mapOf("behavior1" to existingData)
        )
        val dataStore = mockDataStore(initialData)
        val counter = BehaviorInvocationsCounter(dataStore)

        val result = counter.getInvocationData("behavior1")

        assertEquals(existingData, result)
    }

    @Test
    fun `trackInvocation should return InvocationData with count 1 for new behavior ID`() = runTest {
        val testClock = TestClock(1500L)
        val dataStore = mockDataStore(BehaviorInvocationData())
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        val result = counter.trackInvocation("behavior1")

        assertEquals(InvocationData(count = 1, lastInvocation = 1500L), result)
    }

    @Test
    fun `trackInvocation should increment count and update timestamp for existing behavior ID`() = runTest {
        val testClock = TestClock(2500L)
        val existingData = InvocationData(count = 3, lastInvocation = 1000L)
        val initialData = BehaviorInvocationData(
            invocations = mapOf("behavior1" to existingData)
        )
        val dataStore = mockDataStore(initialData)
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        val result = counter.trackInvocation("behavior1")

        assertEquals(InvocationData(count = 4, lastInvocation = 2500L), result)
    }

    @Test
    fun `trackInvocation should handle multiple different behavior IDs independently`() = runTest {
        val testClock = TestClock(3000L)
        val dataStore = mockDataStore(BehaviorInvocationData())
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        testClock.setTime(3000L)
        val result1 = counter.trackInvocation("behavior1")

        testClock.setTime(4000L)
        val result2 = counter.trackInvocation("behavior2")

        testClock.setTime(5000L)
        val result3 = counter.trackInvocation("behavior1")

        assertEquals(InvocationData(count = 1, lastInvocation = 3000L), result1)
        assertEquals(InvocationData(count = 1, lastInvocation = 4000L), result2)
        assertEquals(InvocationData(count = 2, lastInvocation = 5000L), result3)
    }

    @Test
    fun `trackInvocation should persist data between calls and update dataStore`() = runTest {
        val testClock = TestClock(1000L)
        val stateFlow = MutableStateFlow(BehaviorInvocationData())
        val dataStore = mockDataStore(stateFlow)
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        testClock.setTime(1000L)
        counter.trackInvocation("behavior1")

        testClock.setTime(2000L)
        counter.trackInvocation("behavior1")

        val expectedData = BehaviorInvocationData(
            invocations = mapOf(
                "behavior1" to InvocationData(count = 2, lastInvocation = 2000L)
            )
        )
        assertEquals(expectedData, stateFlow.value)
    }

    @Test
    fun `getInvocationData should reflect data after trackInvocation calls`() = runTest {
        val testClock = TestClock(1500L)
        val dataStore = mockDataStore(BehaviorInvocationData())
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        counter.trackInvocation("behavior1")
        testClock.advanceTime(500L)
        counter.trackInvocation("behavior1")

        val result = counter.getInvocationData("behavior1")

        assertEquals(InvocationData(count = 2, lastInvocation = 2000L), result)
    }

    @Test
    fun `trackInvocation should update timestamps correctly with advancing time`() = runTest {
        val testClock = TestClock(1000L)
        val dataStore = mockDataStore(BehaviorInvocationData())
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        testClock.setTime(1000L)
        val result1 = counter.trackInvocation("behavior1")

        testClock.setTime(5000L)
        val result2 = counter.trackInvocation("behavior1")

        testClock.setTime(10000L)
        val result3 = counter.trackInvocation("behavior1")

        assertEquals(1000L, result1.lastInvocation)
        assertEquals(5000L, result2.lastInvocation)
        assertEquals(10000L, result3.lastInvocation)
        assertEquals(3, result3.count)
    }

    @Test
    fun `multiple behaviors should maintain independent timestamps and counts`() = runTest {
        val testClock = TestClock()
        val stateFlow = MutableStateFlow(BehaviorInvocationData())
        val dataStore = mockDataStore(stateFlow)
        val counter = BehaviorInvocationsCounter(dataStore, testClock)

        testClock.setTime(1000L)
        counter.trackInvocation("behavior1")
        counter.trackInvocation("behavior1")

        testClock.setTime(2000L)
        counter.trackInvocation("behavior2")

        testClock.setTime(3000L)
        counter.trackInvocation("behavior3")
        counter.trackInvocation("behavior1")

        val expectedData = BehaviorInvocationData(
            invocations = mapOf(
                "behavior1" to InvocationData(count = 3, lastInvocation = 3000L),
                "behavior2" to InvocationData(count = 1, lastInvocation = 2000L),
                "behavior3" to InvocationData(count = 1, lastInvocation = 3000L)
            )
        )
        assertEquals(expectedData, stateFlow.value)
    }
}
