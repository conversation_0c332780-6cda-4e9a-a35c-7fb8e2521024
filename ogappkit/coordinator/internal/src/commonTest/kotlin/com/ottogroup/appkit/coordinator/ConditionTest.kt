package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.model.Condition
import kotlin.test.Test
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class ConditionTest {

    @Test
    fun `AppStarts condition with start 1 and period 1 is always met`() {
        val condition = Condition.AppStarts(
            start = 1,
            period = 1,
        )
        assertTrue(condition.isMet(1))
        assertTrue(condition.isMet(2))
        assertTrue(condition.isMet(3))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `AppStarts condition with start 10 and period 1 is always met after 10 starts`() {
        val condition = Condition.AppStarts(
            start = 10,
            period = 1,
        )
        assertFalse(condition.isMet(1))
        assertFalse(condition.isMet(9))
        assertTrue(condition.isMet(10))
        assertTrue(condition.isMet(11))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `AppStarts condition with start 10 and period 5 is met every 5 starts after 10 starts`() {
        val condition = Condition.AppStarts(
            start = 10,
            period = 5,
        )
        assertFalse(condition.isMet(1))
        assertFalse(condition.isMet(5))
        assertTrue(condition.isMet(10))
        assertFalse(condition.isMet(11))
        assertTrue(condition.isMet(15))
        assertFalse(condition.isMet(19))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `ScreenViews condition with start 10 and period 5 is met every 5 starts after 10 starts`() {
        val condition = Condition.ScreenViews(
            start = 10,
            period = 5,
        )
        assertFalse(condition.isMet(1))
        assertFalse(condition.isMet(5))
        assertTrue(condition.isMet(10))
        assertFalse(condition.isMet(11))
        assertTrue(condition.isMet(15))
        assertFalse(condition.isMet(19))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `ScreenViews condition with start 1 and period 1 is always met`() {
        val condition = Condition.ScreenViews(
            start = 1,
            period = 1,
        )
        assertTrue(condition.isMet(1))
        assertTrue(condition.isMet(2))
        assertTrue(condition.isMet(3))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `ScreenViews condition with start 10 and period 1 is always met after 10 starts`() {
        val condition = Condition.ScreenViews(
            start = 10,
            period = 1,
        )
        assertFalse(condition.isMet(1))
        assertFalse(condition.isMet(9))
        assertTrue(condition.isMet(10))
        assertTrue(condition.isMet(11))
        assertTrue(condition.isMet(100))
    }

    @Test
    fun `WebBridgeCall condition is met every time when call name matches`() {
        val condition = Condition.WebBridgeCall("purchaseCompleted")
        assertTrue(condition.isMet("purchaseCompleted"))
        assertFalse(condition.isMet("basketBadgeCount"))
        assertFalse(condition.isMet("userInfo"))
        assertTrue(condition.isMet("purchaseCompleted"))
    }
}
