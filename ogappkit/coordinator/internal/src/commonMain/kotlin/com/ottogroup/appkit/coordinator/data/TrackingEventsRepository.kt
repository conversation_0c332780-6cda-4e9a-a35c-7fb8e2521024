package com.ottogroup.appkit.coordinator.data

import com.ottogroup.appkit.coordinator.data.datasource.TrackingEventsLocalDatasource
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent.TrackingEvent
import com.ottogroup.appkit.coordinator.utils.CoordinatorLogger
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

internal class TrackingEventsRepository(
    private val datasource: TrackingEventsLocalDatasource,
    private val coroutineScope: CoroutineScope
) {
    private val events = MutableStateFlow<EventState>(EventState())

    companion object {
        private val FLUSH_INTERVAL = 5.seconds
    }

    private data class EventState(
        val sessionEvents: Map<TrackingEvent, Int> = emptyMap(),
        val pendingEvents: Map<TrackingEvent, Int> = emptyMap()
    )

    init {
        startPeriodicFlush()
    }

    private fun startPeriodicFlush() {
        coroutineScope.launch {
            while (isActive) {
                delay(FLUSH_INTERVAL.inWholeMilliseconds)
                flushPendingEvents()
            }
        }
    }

    fun addSessionEvent(event: TrackingEvent) {
        events.update { current ->
            current.copy(
                sessionEvents = current.sessionEvents + (event to ((current.sessionEvents[event] ?: 0) + 1)),
                pendingEvents = current.pendingEvents + (event to ((current.pendingEvents[event] ?: 0) + 1))
            )
        }
    }

    fun getSessionOccurrences(event: TrackingEvent): Int {
        return events.value.sessionEvents[event] ?: 0
    }

    suspend fun getTotalOccurrences(event: TrackingEvent): Int {
        val persistentCount = datasource.getEventOccurrences(event)
        val pendingCount = events.value.pendingEvents[event] ?: 0
        return persistentCount + pendingCount
    }

    private suspend fun flushPendingEvents() {
        var eventsToFlush: Map<TrackingEvent, Int> = emptyMap()

        events.update { current ->
            eventsToFlush = current.pendingEvents
            if (current.pendingEvents.isEmpty()) {
                current
            } else {
                current.copy(pendingEvents = emptyMap())
            }
        }

        if (eventsToFlush.isEmpty()) return

        try {
            datasource.saveBatchEvents(eventsToFlush)
        } catch (e: Exception) {
            handleFlushFailure(eventsToFlush, e)
        }
    }

    private fun handleFlushFailure(failedEvents: Map<TrackingEvent, Int>, exception: Exception) {
        events.update { current ->
            val mergedPending = current.pendingEvents.toMutableMap()
            failedEvents.forEach { (event, count) ->
                mergedPending[event] = (mergedPending[event] ?: 0) + count
            }
            current.copy(pendingEvents = mergedPending)
        }
        CoordinatorLogger.e("coordinator: failed to persist tracking events batch", exception)
        // Retry after delay - the periodic flush will pick it up
    }

    suspend fun onAppBackground() {
        flushPendingEvents()
    }
}
