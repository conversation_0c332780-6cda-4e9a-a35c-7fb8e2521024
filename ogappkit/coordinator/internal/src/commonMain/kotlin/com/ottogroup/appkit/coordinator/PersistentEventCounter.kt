package com.ottogroup.appkit.coordinator

import androidx.datastore.core.DataStore
import kotlinx.serialization.Serializable

@Serializable
internal data class PersistableEventCount(val count: Int = 0)

internal abstract class PersistentEventCounter(
    private val dataStore: DataStore<PersistableEventCount>,
) {
    suspend fun incrementCount(): Int {
        return dataStore.updateData { it.copy(count = it.count + 1) }.count
    }
}

internal class AppStartCounter(
    dataStore: DataStore<PersistableEventCount>,
) : PersistentEventCounter(dataStore)
