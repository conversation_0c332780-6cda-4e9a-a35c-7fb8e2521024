package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.base.datastore.jsonDataStore
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.coordinator.config.CoordinatorConfigProvider
import com.ottogroup.appkit.coordinator.data.TrackingEventsRepository
import com.ottogroup.appkit.coordinator.data.datasource.TrackingEventsLocalDatasource
import com.ottogroup.appkit.coordinator.model.TrackingEventsHolder
import org.koin.core.module.Module
import org.koin.dsl.module

public val coordinatorModule: Module = module {
    single<OGCoordinator> {
        OGCoordinatorImpl(
            configProvider = get(),
            appStartCounter = get(),
            screenViewCounter = get(),
            behaviorQueue = get(),
            coroutineScope = getCoroutineScope(),
            trackingDealer = get(),
            trackingEventsRepository = get(),
            applicationLifecycleProvider = get()
        )
    }
    single { CoordinatorConfigProvider() }
    single {
        AppStartCounter(
            jsonDataStore(
                default = PersistableEventCount(),
                fileName = APP_START_COUNT_DATA_STORE,
                fileSystem = get()
            )
        )
    }
    single {
        BehaviorInvocationsCounter(
            jsonDataStore(
                default = BehaviorInvocationData(),
                fileName = BEHAVIOR_INVOCATIONS_COUNT_DATA_STORE,
                fileSystem = get()
            )
        )
    }
    single {
        ScreenViewCounter(
            jsonDataStore(
                default = ScreenViewCounts(),
                fileName = SCREEN_VIEW_COUNT_DATA_STORE,
                fileSystem = get()
            )
        )
    }
    factory { BehaviorQueue(get(), get(), getCoroutineScope()) }
    single {
        TrackingEventsLocalDatasource(
            jsonDataStore(
                default = TrackingEventsHolder(),
                fileName = TRACKING_EVENTS_DATA_STORE,
                fileSystem = get()
            )
        )
    }
    single { TrackingEventsRepository(get(), getCoroutineScope()) }
}

private const val APP_START_COUNT_DATA_STORE = "coordinator_app_start_count"
private const val SCREEN_VIEW_COUNT_DATA_STORE = "coordinator_screen_view_count"
private const val BEHAVIOR_INVOCATIONS_COUNT_DATA_STORE = "coordinator_behavior_invocations_count"
private const val TRACKING_EVENTS_DATA_STORE = "tracking_events_count"
