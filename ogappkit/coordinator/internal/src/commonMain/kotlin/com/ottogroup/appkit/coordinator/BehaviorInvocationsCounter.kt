package com.ottogroup.appkit.coordinator

import androidx.datastore.core.DataStore
import kotlinx.coroutines.flow.first
import kotlinx.datetime.Clock
import kotlinx.serialization.Serializable

internal class BehaviorInvocationsCounter(
    private val dataStore: DataStore<BehaviorInvocationData>,
    private val clock: Clock = Clock.System,
) {

    suspend fun getInvocationData(behaviorId: String): InvocationData =
        dataStore.data.first().invocations[behaviorId] ?: InvocationData()

    suspend fun trackInvocation(behaviorId: String): InvocationData {
        return dataStore.updateData { invocations ->
            val previousInvocations =
                invocations.invocations[behaviorId] ?: InvocationData()
            val newInvocations = InvocationData(
                count = previousInvocations.count + 1,
                lastInvocation = clock.now().toEpochMilliseconds(),
            )
            invocations.copy(
                invocations = invocations.invocations + (behaviorId to newInvocations)
            )
        }.invocations[behaviorId] ?: InvocationData()
    }
}

@Serializable
internal data class BehaviorInvocationData(
    val invocations: Map<String, InvocationData> = emptyMap()
)

@Serializable
internal data class InvocationData(
    val count: Int = 0,
    val lastInvocation: Long = 0,
)
