package com.ottogroup.appkit.coordinator.data.datasource

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.coordinator.model.CoordinatorEvent.TrackingEvent
import com.ottogroup.appkit.coordinator.model.TrackingEventsHolder
import kotlinx.coroutines.flow.first

internal class TrackingEventsLocalDatasource(private val dataStore: DataStore<TrackingEventsHolder>) {
    suspend fun saveBatchEvents(events: Map<TrackingEvent, Int>) {
        if (events.isEmpty()) return

        dataStore.updateData { currentData ->
            val updatedEvents = currentData.events.toMutableMap()
            events.forEach { (trackingEvent, count) ->
                val eventName = getEventName(trackingEvent)
                updatedEvents[eventName] = (updatedEvents[eventName] ?: 0) + count
            }
            currentData.copy(events = updatedEvents)
        }
    }

    suspend fun getEventOccurrences(event: TrackingEvent): Int {
        val eventName = getEventName(event)
        return dataStore.data.first().events[eventName] ?: 0
    }

    private fun getEventName(trackingEvent: TrackingEvent): String {
        val description = trackingEvent.event.describe()
        return description["name"] as? String ?: trackingEvent.event::class.simpleName ?: "Unknown"
    }
}
