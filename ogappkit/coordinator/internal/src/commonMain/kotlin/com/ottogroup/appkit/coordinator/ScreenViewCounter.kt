package com.ottogroup.appkit.coordinator

import androidx.datastore.core.DataStore
import kotlinx.serialization.Serializable

internal class ScreenViewCounter(
    private val dataStore: DataStore<ScreenViewCounts>,
) {
    suspend fun incrementCount(behaviorId: String): Int {
        return dataStore.updateData { invocationCounts ->
            val previousInvocations = invocationCounts.screenViewCounts[behaviorId] ?: 0
            val newInvocations = previousInvocations + 1
            invocationCounts.copy(
                screenViewCounts = invocationCounts.screenViewCounts + (behaviorId to newInvocations)
            )
        }.screenViewCounts[behaviorId] ?: 0
    }
}

@Serializable
internal data class ScreenViewCounts(val screenViewCounts: Map<String, Int> = emptyMap())
