package com.ottogroup.appkit.coordinator.utils

import co.touchlab.kermit.LogWriter
import co.touchlab.kermit.Logger
import co.touchlab.kermit.LoggerConfig
import co.touchlab.kermit.Severity
import co.touchlab.kermit.loggerConfigInit
import co.touchlab.kermit.platformLogWriter
import com.ottogroup.appkit.base.di.InternalKoinComponent

internal object CoordinatorLogger : Logger(
    config = loggerConfigInit(platformLogWriter()),
    tag = "OGCoordinator"
) {
    override val config: LoggerConfig = CoordinatorLoggerConfig(super.config.logWriterList)

    class CoordinatorLoggerConfig(
        override val logWriterList: List<LogWriter>
    ) : LoggerConfig, InternalKoinComponent {
        override val minSeverity: Severity
            get() = Severity.Verbose
    }
}
