package com.ottogroup.appkit.coordinator

import com.ottogroup.appkit.coordinator.config.CoordinatorConfigProvider
import com.ottogroup.appkit.coordinator.model.Action
import com.ottogroup.appkit.coordinator.model.Behavior
import com.ottogroup.appkit.coordinator.utils.CoordinatorLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

internal class BehaviorQueue(
    private val configProvider: CoordinatorConfigProvider,
    private val invocationsCounter: BehaviorInvocationsCounter,
    private val coroutineScope: CoroutineScope,
    private val clock: Clock = Clock.System,
) {

    private val enqueuedBehaviors: MutableSet<EnqueuedBehavior> = mutableSetOf()
    private var queueJob: Job? = null

    private val _actionsFlow = MutableSharedFlow<Action>()
    val actionsFlow: SharedFlow<Action> = _actionsFlow.asSharedFlow()

    suspend fun enqueue(behavior: Behavior, priority: Int) {
        if (behavior.maxInvocationsReached()) {
            CoordinatorLogger.d("coordinator: behavior ${behavior.id} is skipped because its maximum invocations are reached.")
            return
        }

        if (behavior.needsToWaitBeforeNextInvocation()) {
            CoordinatorLogger.d("coordinator: behavior ${behavior.id} is skipped because its time between invocations is not elapsed.")
            return
        }

        CoordinatorLogger.d("coordinator: behavior ${behavior.id} is enqueued.")
        enqueuedBehaviors.add(EnqueuedBehavior(behavior, priority))
        queueJob?.cancel()
        queueJob = coroutineScope.launch {
            delay(configProvider.configState.value.debounceMs)

            val mostImportantBehavior =
                enqueuedBehaviors.minByOrNull { it.priority }?.behavior ?: return@launch
            CoordinatorLogger.d("coordinator: behavior ${mostImportantBehavior.id} is executed.")

            invocationsCounter.trackInvocation(mostImportantBehavior.id)
            _actionsFlow.emit(mostImportantBehavior.action)

            enqueuedBehaviors.clear()
        }
    }

    private suspend fun Behavior.maxInvocationsReached(): Boolean {
        val maxInvocations = this.maxInvocations
        return maxInvocations != null &&
            invocationsCounter.getInvocationData(this.id).count >= maxInvocations
    }

    private suspend fun Behavior.needsToWaitBeforeNextInvocation(): Boolean {
        val minutesBetweenInvocations = this.minutesBetweenInvocations ?: return false
        val elapsedMillisSinceLastInvocation =
            clock.now().toEpochMilliseconds() - invocationsCounter.getInvocationData(this.id).lastInvocation
        val minutesBetweenInvocationsMillis = minutesBetweenInvocations * MINUTES_TO_MILLIS
        return elapsedMillisSinceLastInvocation < minutesBetweenInvocationsMillis
    }

    private data class EnqueuedBehavior(
        val behavior: Behavior,
        val priority: Int,
    )

    private companion object {
        const val MINUTES_TO_MILLIS = 60 * 1000
    }
}
