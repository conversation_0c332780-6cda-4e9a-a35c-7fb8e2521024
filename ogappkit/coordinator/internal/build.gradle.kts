plugins {
    id("ogAppKit.libraryModule")
}

kotlin {
    sourceSets {
        commonMain.dependencies {
            api(projects.ogappkit.coordinator.api)

            implementation(libs.kotlinx.datetime)
            implementation(libs.stately.concurrentCollections)
            implementation(libs.androidx.datastore.core)
            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.serialization.kotlin.json)
            implementation(projects.ogappkit.tracking.api)
        }
    }
}
