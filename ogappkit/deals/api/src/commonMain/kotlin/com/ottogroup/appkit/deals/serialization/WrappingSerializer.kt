package com.ottogroup.appkit.deals.serialization

import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.JsonTransformingSerializer

/**
 * Utility serializer for wrapping Primitive value in a JsonObject to allow deserialization with default primitive value and complex objects.
 * This is useful when the complex object has a meaningfull simple default representation with just a main single value and othwerwise fill default value properties.
 *
 * this would allow the following cases:
 * ```
 * object TextSerializer : WrappingSerializer<Text>("value", Text.serializer())
 * @Serializable
 * data class Text(val value: String, val value2: String = "default")
 *
 * @Serializable
 * data class Response(@Serializable(with = TextSerializer::class) val text: Text)
 *
 * val text: Text = Json.decodeFromString("""{"value": "Hello World"}""")
 * val response: Response = Json.decodeFromString("""{"text": "Hello World"}""")
 * val response: Response = Json.decodeFromString("""{"text": {"value": "Hello World"}}""")
 * ```
 */
public open class WrappingSerializer<T : Any>(private val key: String, serializer: KSerializer<T>) :
    JsonTransformingSerializer<T>(serializer) {

    override fun transformDeserialize(element: JsonElement): JsonElement {
        return when (element) {
            is JsonPrimitive -> JsonObject(mapOf(key to element))
            is JsonObject -> element
            else -> throw IllegalArgumentException("Unexpected element: $element")
        }
    }
}
