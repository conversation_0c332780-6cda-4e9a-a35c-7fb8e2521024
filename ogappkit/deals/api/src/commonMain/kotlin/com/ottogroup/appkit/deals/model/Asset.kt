package com.ottogroup.appkit.deals.model

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonClassDiscriminator("type")
public sealed interface Asset {

    public interface Remote {
        public val url: String
    }

    @Serializable
    public sealed interface Media : Content

    @Serializable
    public sealed interface Content : Asset

    @Serializable
    @SerialName("image")
    public data class Image(
        val url: String,
        val altText: String? = null
    ) : Media

    @Serializable
    @SerialName("video")
    public data class Video(
        val url: String,
        val altText: String? = null
    ) : Media

    @Serializable
    @SerialName("lottie")
    public data class Lottie(
        val url: String,
        val altText: String? = null
    ) : Media

    @Serializable
    @SerialName("color")
    public data class Color(
        val value: String
    ) : Content

    @Serializable
    @SerialName("filter")
    public sealed interface Filter : Content {

        @Serializable
        @SerialName("blur")
        public data object Blur : Filter
    }

    @Serializable
    @SerialName("text")
    public data class Text(
        val value: String
    ) : Asset
}
