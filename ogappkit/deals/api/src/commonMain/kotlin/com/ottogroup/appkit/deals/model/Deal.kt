package com.ottogroup.appkit.deals.model

import com.ottogroup.kotlinx.serialization.LossyList
import kotlinx.datetime.Instant
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

@Serializable
public data class Deals(
    val items: LossyList<Deal>,
    val startDate: Instant,
    val endDate: Instant,
    val endDateTitle: Asset.Text
)

@Serializable
@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator("type")
public sealed interface Deal {
    public val id: String
    public val title: Asset.Text
    public val url: String
    public val image: Asset.Image
    public val ctaText: Asset.Text
    public val additionalInfo: Asset.Text?
    public val legalText: Asset.Text?
    public val legalHeader: Asset.Text?

    @Serializable
    @SerialName("voucher")
    public data class Voucher(
        override val id: String,
        override val title: Asset.Text,
        override val url: String,
        override val image: Asset.Image,
        override val ctaText: Asset.Text,
        override val additionalInfo: Asset.Text?,
        override val legalText: Asset.Text?,
        override val legalHeader: Asset.Text?,
    ) : Deal

    @Serializable
    @SerialName("product")
    public data class Product(
        override val id: String,
        override val title: Asset.Text,
        override val url: String,
        override val image: Asset.Image,
        override val ctaText: Asset.Text,
        override val additionalInfo: Asset.Text?,
        override val legalText: Asset.Text?,
        override val legalHeader: Asset.Text?,
        val price: Price,
        val label: String?,
        val rating: Rating?
    ) : Deal {

        @Serializable
        public data class Rating(
            val numberOfStars: Int,
            val numberOfReviews: Int
        )
    }

    @Serializable
    @SerialName("promo")
    public data class Promo(
        override val id: String,
        override val title: Asset.Text,
        override val url: String,
        override val image: Asset.Image,
        override val ctaText: Asset.Text,
        override val additionalInfo: Asset.Text?,
        override val legalText: Asset.Text?,
        override val legalHeader: Asset.Text?,
    ) : Deal

    @Serializable
    @SerialName("code")
    public data class Code(
        override val id: String,
        override val title: Asset.Text,
        override val url: String,
        override val image: Asset.Image,
        override val ctaText: Asset.Text,
        override val additionalInfo: Asset.Text?,
        override val legalText: Asset.Text?,
        override val legalHeader: Asset.Text?,
        val code: String
    ) : Deal
}
