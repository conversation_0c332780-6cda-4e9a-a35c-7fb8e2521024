package com.ottogroup.appkit.deals.references

import co.touchlab.kermit.Logger
import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.JsonTransformingSerializer
import kotlinx.serialization.json.contentOrNull

/**
 * Utility serializer for resolving references in json objects.
 * This is useful when the json object contains references to other objects.
 * References are identified via a special flag in the form of "$<base>://<path>"
 * The base is used to identify the reference source and the path is used to resolve the reference.
 * The path is a list of keys to traverse the reference object.
 *
 * Each path should be separated by a "/"
 *
 * If this is used with a json array,
 * Each element in the array is resolved independently and no reference is shared between each item.
 *
 * Current limitation:
 * References can only be resolved via json objects and json primitives - no arrays supported.
 *
 * Each Reference is then replaced by the resolved object/value.
 *
 * For further processing, if an object is referenced, the original raw value is added as "_raw" key
 * the resolved path is added as "_path" key
 * the base is added as "_base" key
 *
 * Challenge:
 * Since referenced objects have no strict type, and deserialization can have multiple valid types, we need to add a type key
 * this is added as "type" key in the resolved object and needs to be configured to match the type of the object.
 * The default implementation uses the first path element as the type.
 *
 * Example:
 * ```
 * {
 *     "config": {
 *         "param1": "$values://param1/value",
 *         "param2": "$values://param2",
 *         "url": "$somewhere://urls/shop"
 *         "other": "$somewhere://urls/base"
 *     },
 *     "values": {
 *         "param1": {
 *             "value": "Hello World"
 *         },
 *         "param2": {
 *  *             "value": "Hello World2"
 *  *         }
 *
 *     },
 *     "somewhere": {
 *         "urls": {
 *             "shop": "$somewhere://urls/base",
 *              "base": "https://www.example.com"
 *              "other": {
 *                  "value": "Hello World"
 *              }
 *         }
 *     }
 * }
 *  ```
 */
public abstract class ReferenceResolvingJsonTransformationSerializer<T : Any>(
    serializer: KSerializer<T>,
    private val typeResolver: (base: String, path: List<String>) -> String? = { _, path -> path.first() }
) : JsonTransformingSerializer<T>(serializer) {
    private val maxDepth = 10
    private val referenceFlag = "^\\$(?<base>\\w*)://(?<path>.*)$".toRegex()

    private fun JsonElement.resolveReferences(
        assets: JsonObject,
        depth: Int = 0,
        root: JsonElement? = null
    ): JsonElement? {
        val nexDepth = depth + 1
        if (nexDepth > maxDepth) {
            Logger.w("Max depth reached - highly possible circular reference")
            return root
        }
        return when (this) {
            is JsonObject -> JsonObject(
                mapValues { (_, value) ->
                    val resolved = value.resolveReferences(
                        assets,
                        nexDepth,
                        value
                    )
                    resolved ?: root ?: value
                }
            )

            is JsonArray -> JsonArray(
                map {
                    it.resolveReferences(assets, nexDepth) ?: it
                }
            )

            is JsonPrimitive -> {
                val matches = referenceFlag.matchEntire(contentOrNull ?: "")
                val base = matches?.groups?.get("base")?.value
                val path = matches?.groups?.get("path")?.value?.split("/")

                if (!base.isNullOrBlank() && !path.isNullOrEmpty()) {
                    val asset = assets.resolvePath(listOf(base) + path)
                    val resolvedElement = asset?.resolveReferences(assets, nexDepth, root ?: this)
                    if (resolvedElement == root || resolvedElement == null) {
                        Logger.w("Reference not found for $base://$path - highly possible circular reference for $this : root: $root")
                        root
                    } else if (resolvedElement is JsonObject) {
                        val resolvedType = typeResolver(base, path)
                        JsonObject(
                            buildMap {
                                put("_raw", this@resolveReferences)
                                put("_base", JsonPrimitive("$base"))
                                put("_path", JsonArray(path.map { JsonPrimitive(it) }))
                                if (resolvedType != null) {
                                    put("type", JsonPrimitive(resolvedType))
                                }
                            } + resolvedElement
                        )
                    } else {
                        resolvedElement
                    }
                } else {
                    this
                }
            }
        }
    }

    private fun JsonObject.resolvePath(path: List<String>): JsonElement? {
        return path.fold(this as JsonElement?) { current, key ->
            (current as? JsonObject)?.get(key)
        }
    }

    override fun transformDeserialize(element: JsonElement): JsonElement {
        return when (element) {
            is JsonArray -> JsonArray(
                element.map {
                    transformDeserialize(it)
                }
            )

            is JsonObject -> element.resolveReferences(element) ?: element
            else -> element
        }
    }
}
