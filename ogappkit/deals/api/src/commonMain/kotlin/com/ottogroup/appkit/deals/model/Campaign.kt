package com.ottogroup.appkit.deals.model

import com.ottogroup.appkit.deals.references.ReferenceResolvingJsonTransformationSerializer
import com.ottogroup.kotlinx.serialization.LossyListSerializer2
import kotlinx.datetime.Clock
import kotlinx.serialization.Serializable
import kotlinx.datetime.Instant
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.json.JsonClassDiscriminator

private object CampaignsReferenceResolver :
    ReferenceResolvingJsonTransformationSerializer<List<Campaign>>(
        LossyListSerializer2(Campaign.serializer()),
        typeResolver = { base, path ->
            // images - > image
            // videos - > video
            // lotties - > lottie
            // texts - > text
            // colors - > color
            // filters - > filter
            path.first().removeSuffix("s")
        }
    )

@Serializable
public data class Campaigns(
    @Serializable(with = CampaignsReferenceResolver::class) val items: List<Campaign>
)

@Serializable
public data class Campaign(
    val id: String,
    val banner: Banner?,
    val startDate: Instant,
    val endDate: Instant,
    val title: Asset.Text,
    val game: Game,
    val deals: Deals
) {

    val isActive: Boolean
        get() {
            Clock.System.now().let {
                return it in startDate..endDate
            }
        }

    @Serializable
    public data class Banner(
        val id: String,
        val image: Asset.Image,
        val title: Asset.Text?,
        val subtitle: Asset.Text?,
        val index: Int
    )

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    @JsonClassDiscriminator("type")
    public sealed interface Game {

        public val title: Asset.Text
        public val text: Asset.Text?
        public val background: List<Asset.Content>?
        public val foreground: List<Asset.Content>?
        public val onboarding: Onboarding?
        public val content: Asset.Media
        public val finishAnimation: Asset.Media?

        @Serializable
        @SerialName("ShakeADeal")
        public data class ShakeADeal(
            override val title: Asset.Text,
            override val text: Asset.Text?,
            override val background: List<Asset.Content>?,
            override val foreground: List<Asset.Content>?,
            override val onboarding: Onboarding?,
            override val content: Asset.Media,
            override val finishAnimation: Asset.Media?,
            val countdown: Int
        ) : Game

        @Serializable
        @SerialName("ScratchADeal")
        public data class ScratchADeal(
            override val title: Asset.Text,
            override val text: Asset.Text?,
            override val background: List<Asset.Content>?,
            override val foreground: List<Asset.Content>?,
            override val onboarding: Onboarding?,
            override val content: Asset.Media,
            override val finishAnimation: Asset.Media?,
            val scratchPercentage: Double
        ) : Game

        @Serializable
        @SerialName("TapADeal")
        public data class TapADeal(
            override val title: Asset.Text,
            override val text: Asset.Text?,
            override val background: List<Asset.Content>?,
            override val foreground: List<Asset.Content>?,
            override val onboarding: Onboarding?,
            override val content: Asset.Media,
            override val finishAnimation: Asset.Media?,
        ) : Game

        @Serializable
        public data class Onboarding(
            val content: Asset.Video
        )
    }
}
