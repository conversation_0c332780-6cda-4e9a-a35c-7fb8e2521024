package com.ottogroup.appkit.deals

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.deals.config.DealsConfig
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import kotlinx.coroutines.flow.Flow

/**
 * The main entry point to Deals functionality. Obtain an instance from
 * the `OGAppKitSdk` object.
 */
public interface OGDeals {
    /**
     * Configures the Deals SDK. MUST be called before performing any other
     * operations.
     */
    public fun configure(config: DealsConfig)

    /**
     * Gets the available campaigns for the configured tenant.
     */
    public fun getCampaigns(): Flow<Result<Campaigns>>

    /**
     * Gets the available offers for the selected campaign.
     */
    public fun getCampaign(campaignId: String): Flow<Result<Campaign>>
}
