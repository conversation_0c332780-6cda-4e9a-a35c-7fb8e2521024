plugins {
    id("ogAppKit.libraryModule")
}

kotlin {
    sourceSets {
        commonMain.dependencies {
            api(projects.ogappkit.deals.api)

            api(libs.kotlinx.datetime)
            implementation(libs.stately.concurrentCollections)
            implementation(libs.androidx.datastore.core)
            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.serialization.kotlin.json)
        }
        commonTest.dependencies {
            implementation(libs.ktor.client.mock)
        }
        androidMain.dependencies {
            implementation(libs.ktor.client.cio)
        }
        iosMain.dependencies {
            implementation(libs.ktor.client.ios)
        }
    }
}
