package com.ottogroup.appkit.deals

import com.ottogroup.appkit.deals.references.ReferenceResolvingJsonTransformationSerializer
import com.ottogroup.appkit.deals.serialization.WrappingSerializer
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

class ReferenceResolvingTest {
    object ReferenceResolver :
        ReferenceResolvingJsonTransformationSerializer<JsonObject>(JsonObject.serializer())

    object TextSerializer : WrappingSerializer<Text>("value", Text.serializer())
    object Text2Serializer : WrappingSerializer<Text2>("value", Text2.serializer())

    @Serializable
    data class Text(val value: String)

    @Serializable
    data class Response(
        @Serializable(with = TextSerializer::class) val text: Text
    )

    @Serializable
    data class Text2(val value: String, val value2: String = "default")

    @Serializable
    data class Response2(
        @Serializable(with = Text2Serializer::class) val text: Text2
    )

    @Test
    fun `wrap json primitive in object with key for simple objects`() {
        val text1: Text = Json.decodeFromString(TextSerializer, """{"value": "Hello World"}""")
        assertEquals(Text("Hello World"), text1)

        val response1: Response =
            Json.decodeFromString("""{"text":{"value": "Hello World"}}""")
        assertEquals(Response(Text("Hello World")), response1)
        val response2: Response = Json.decodeFromString("""{"text": "Hello World"}""")

        assertEquals(Response(Text("Hello World")), response2)
    }

    @Test
    fun `wrap json primitive in object with key for complex objects with defaults`() {
        val text1: Text2 =
            Json.decodeFromString(Text2Serializer, """{"value": "Hello World"}""")
        assertEquals(Text2("Hello World"), text1)

        val response1: Response2 =
            Json.decodeFromString("""{"text":{"value": "Hello World"}}""")
        assertEquals(Response2(Text2("Hello World")), response1)
        val response2: Response2 = Json.decodeFromString("""{"text": "Hello World"}""")

        assertEquals(Response2(Text2("Hello World")), response2)
    }

    @Test
    fun `resolve references in json`() {
        val json = """
            {
                "config": {
                    "param1": "${"$"}values://param1/value",
                    "param2": "${"$"}values://param2",
                    "url": "${"$"}somewhere://urls/shop",
                    "other": "${"$"}somewhere://urls/other"
                },
                "values": {
                    "param1": {
                        "value": "Hello World"
                    },
                     "param2": {
                        "value": "Hello World2"
                    }
                },
                "somewhere": {
                    "urls": {
                        "shop": "${"$"}somewhere://urls/base",
                         "base": "https://www.example.com",
                         "other": {
                             "value": "Hello World"
                         }
                    }
                }
            }
        """.trimIndent()

        val expectedJson = """
            {
                "config": {
                    "param1": "Hello World",
                     "param2": {
                        "value": "Hello World2",
                        "_raw":"${"$"}values://param2",
                        "_base":"values",
                        "_path":["param2"],
                        "type":"param2"
                    },
                    "url": "https://www.example.com",
                    "other": {
                        "value": "Hello World",
                        "_raw":"${"$"}somewhere://urls/other",
                        "_base":"somewhere",
                        "_path":["urls","other"],
                        "type":"urls"
                         }
                },
                "values": {
                    "param1": {
                        "value": "Hello World"
                    },
                     "param2": {
                        "value": "Hello World2"
                    }
                },
                "somewhere": {
                    "urls": {
                        "shop": "https://www.example.com",
                         "base": "https://www.example.com",
                         "other": {
                             "value": "Hello World"
                         }
                    }
                }
            }
        """.trimIndent()
        val resolvedJsonObject = Json.decodeFromString(ReferenceResolver, json)
        val unresolvedJsonObject = Json.decodeFromString<JsonObject>(expectedJson)
        assertEquals(
            JsonObject(
                mapOf(
                    "config" to JsonObject(
                        mapOf(
                            "param1" to JsonPrimitive("Hello World"),
                            "param2" to JsonObject(
                                mapOf(
                                    "value" to JsonPrimitive("Hello World2"),
                                    "type" to JsonPrimitive("param2"),
                                    "_raw" to JsonPrimitive("${"$"}values://param2"),
                                    "_base" to JsonPrimitive("values"),
                                    "_path" to JsonArray(listOf(JsonPrimitive("param2"))),
                                )

                            ),
                            "url" to JsonPrimitive("https://www.example.com"),
                            "other" to JsonObject(
                                mapOf(
                                    "value" to JsonPrimitive("Hello World"),
                                    "type" to JsonPrimitive("urls"),
                                    "_raw" to JsonPrimitive("${"$"}somewhere://urls/other"),
                                    "_base" to JsonPrimitive("somewhere"),
                                    "_path" to JsonArray(listOf(JsonPrimitive("urls"), JsonPrimitive("other"))),
                                )
                            )
                        )
                    ),
                    "values" to JsonObject(
                        mapOf(
                            "param1" to JsonObject(mapOf("value" to JsonPrimitive("Hello World"))),
                            "param2" to JsonObject(mapOf("value" to JsonPrimitive("Hello World2"))),
                        )
                    ),
                    "somewhere" to JsonObject(
                        mapOf(
                            "urls" to JsonObject(
                                mapOf(
                                    "shop" to JsonPrimitive("https://www.example.com"),
                                    "base" to JsonPrimitive("https://www.example.com"),
                                    "other" to JsonObject(mapOf("value" to JsonPrimitive("Hello World")))
                                )
                            )
                        )
                    )
                )
            ),
            resolvedJsonObject
        )

        assertEquals(unresolvedJsonObject, resolvedJsonObject)
    }

    @Test
    fun `resolve references in json and handle circular dependency gracefully`() {
        val json = """
            {
                "config": {
                    "param1": "${"$"}values://params/param1"
                },
                "values": {
                    "params": {
                        "param1": "${"$"}somewhere://urls/base/domain"
                    }
                },
                "somewhere": {
                    "urls": {
                         "base": {
                                "domain": "${"$"}config://param1"
                            }
                    }
                }
            }
        """.trimIndent()
        val resolvedObject = Json.decodeFromString(ReferenceResolver, json)
        val unresolvedObject = Json.decodeFromString<JsonObject>(json)
        assertEquals(unresolvedObject, resolvedObject)
    }

    private object ListReferenceResolver :
        ReferenceResolvingJsonTransformationSerializer<List<Text>>(ListSerializer(Text.serializer()))

    @Serializable
    private data class Campaigns(
        @Serializable(with = ListReferenceResolver::class) val items: List<Text>
    )

    @Test
    fun `resolve references in for each json array element individually`() {
        val json = """
            {
                "items": [
                    {
                        "value": "${"$"}assets://test",
                         "assets":{
                            "test": "value1"
                        }
                    },
                    {
                        "value": "${"$"}assets://test",
                        "assets":{
                            "test": "value2"
                        }
                    },
                     {
                        "value": "${"$"}assets://test",
                        "assets":{
                            "test": "value3"
                        }
                    }
                ]
            }
        """.trimIndent()

        val expectedJson = """
            {
                "items": [
                    {
                        "value": "value1",
                         "assets":{
                            "test": "value1"
                        }
                    },
                    {
                        "value": "value2",
                        "assets":{
                            "test": "value2"
                        }
                    },
                     {
                        "value": "value3",
                        "assets":{
                            "test": "value3"
                        }
                    }
                ]
            }
        """.trimIndent()
        val serializer = Json { ignoreUnknownKeys = true }
        val resolvedJsonObject = serializer.decodeFromString(Campaigns.serializer(), json)
        val unresolvedJsonObject = serializer.decodeFromString<Campaigns>(expectedJson)
        assertEquals(unresolvedJsonObject, resolvedJsonObject)
    }

    @Test
    fun `resolve references in for each json array element will not leak into other elements`() {
        val json = """
            {
                "items": [
                    {
                        "value": "${"$"}assets://test1",
                         "assets":{
                            "test1": "value1"
                        }
                    },
                    {
                        "value": "${"$"}assets://test2",
                        "assets":{
                            "test2": "value2"
                        }
                    },
                     {
                        "value": "${"$"}assets://test1",
                        "assets":{
                            "test3": "value3"
                        }
                    }
                ]
            }
        """.trimIndent()

        val expectedJson = """
            {
                "items": [
                    {
                        "value": "value1",
                         "assets":{
                            "test1": "value1"
                        }
                    },
                    {
                        "value": "value2",
                        "assets":{
                            "test2": "value2"
                        }
                    },
                     {
                        "value": "${"$"}assets://test1",
                        "assets":{
                            "test3": "value3"
                        }
                    }
                ]
            }
        """.trimIndent()
        val serializer = Json { ignoreUnknownKeys = true }
        val resolvedJsonObject = serializer.decodeFromString(Campaigns.serializer(), json)
        val unresolvedJsonObject = serializer.decodeFromString<Campaigns>(expectedJson)
        assertEquals(unresolvedJsonObject, resolvedJsonObject)
    }
}
