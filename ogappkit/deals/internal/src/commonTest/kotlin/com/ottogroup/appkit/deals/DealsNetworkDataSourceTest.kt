package com.ottogroup.appkit.deals

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.http.AppInfoProvider
import com.ottogroup.appkit.base.http.InstallationIdProvider
import com.ottogroup.appkit.base.http.OGAppKitHeaders
import com.ottogroup.appkit.base.http.ogAppKitHeadersPlugin
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.deals.config.DealsConfig
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.data.DealsNetworkDataSource
import com.ottogroup.appkit.deals.data.OGAppKit
import com.ottogroup.appkit.deals.data.ResolvedCampaign
import com.ottogroup.appkit.deals.model.Campaigns
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockRequestHandler
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.test.runTest

class DealsNetworkDataSourceTest {

    private val dealsConfigProvider = DealsConfigProvider().apply {
        update(
            DealsConfig(
                baseUrl = "https://example.com",
                locale = "de-DE",
                apiKey = "secret-K3y",
            )
        )
    }
    private val config get() = dealsConfigProvider.configState.value

    private fun createDealsNetworkDataSource(mockEngine: MockEngine? = null): DealsNetworkDataSource {
        return DealsNetworkDataSource(
            configProvider = dealsConfigProvider,
            ogKitHeaders = ogAppKitHeadersPlugin(
                OGAppKitHeaders(
                    appInfoProvider = object : AppInfoProvider {
                        override val appName = "TestApp"
                        override val appVersionName = "1.0.0"
                        override val appPackage = "com.ottogroup.app"
                    },
                    installationId = object : InstallationIdProvider {
                        override val installationId = "installation-id-123"
                    }
                )
            ),
            customEngine = mockEngine,
        )
    }

    @Test
    fun `getCampaigns returns failure when config is incomplete`() = runTest {
        val dealsNetworkDataSource = createDealsNetworkDataSource()

        // no base URL
        dealsConfigProvider.update(
            DealsConfig(
                locale = "de-DE",
                apiKey = "secret-K3y",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaigns())

        // no locale
        dealsConfigProvider.update(
            DealsConfig(
                baseUrl = "https://example.com",
                apiKey = "secret-K3y",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaigns())

        // no API key
        dealsConfigProvider.update(
            DealsConfig(
                baseUrl = "https://example.com",
                locale = "de-DE",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaigns())
    }

    @Test
    fun `getCampaigns returns failure when server response with error`() = runTest {
        val dealsNetworkDataSource = createDealsNetworkDataSource(
            mockEngine = MockEngine {
                respondError(HttpStatusCode.NotFound)
            }
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaigns())
    }

    @Test
    fun `getCampaigns returns success when server response with success`() = runTest {
        val campaigns = lenientJson.decodeFromString<Campaigns>(campaignsJson)

        val dealsNetworkDataSource = createDealsNetworkDataSource(
            mockEngine = mockEngine { request ->
                assertEquals(
                    "${config.baseUrl}/campaigns",
                    request.url.toString()
                )
                respond(
                    content = ByteReadChannel(campaignsJson),
                    headers = headersOf(
                        HttpHeaders.ContentType to listOf("application/json"),
                    )
                )
            }
        )
        assertEquals(
            Result.Success(campaigns),
            dealsNetworkDataSource.getCampaigns()
        )
    }

    @Test
    fun `getCampaign returns failure when config is incomplete`() = runTest {
        val dealsNetworkDataSource = createDealsNetworkDataSource()

        // no base URL
        dealsConfigProvider.update(
            DealsConfig(
                locale = "de-DE",
                apiKey = "secret-K3y",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaign("some-id-123"))

        // no locale
        dealsConfigProvider.update(
            DealsConfig(
                baseUrl = "https://example.com",
                apiKey = "secret-K3y",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaign("some-id-123"))

        // no API key
        dealsConfigProvider.update(
            DealsConfig(
                baseUrl = "https://example.com",
                locale = "de-DE",
            )
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaign("some-id-123"))
    }

    @Test
    fun `getCampaign returns failure when server response with error`() = runTest {
        val dealsNetworkDataSource = createDealsNetworkDataSource(
            mockEngine = MockEngine {
                respondError(HttpStatusCode.NotFound)
            }
        )
        assertIsFailureWithException(dealsNetworkDataSource.getCampaign("some-id-123"))
    }

    @Test
    fun `getCampaign returns success when server response with success`() = runTest {
        val resolvedCampaign = lenientJson.decodeFromString<ResolvedCampaign>(campaignJson)
        val dealsNetworkDataSource = createDealsNetworkDataSource(
            mockEngine = mockEngine { request ->
                assertEquals(
                    "${config.baseUrl}/campaigns/some-id-123",
                    request.url.toString()
                )
                respond(
                    content = ByteReadChannel(campaignJson),
                    headers = headersOf(
                        HttpHeaders.ContentType to listOf("application/json"),
                    )
                )
            }
        )
        assertEquals(
            Result.Success(resolvedCampaign.campaign),
            dealsNetworkDataSource.getCampaign("some-id-123")
        )
    }

    private fun mockEngine(handler: MockRequestHandler): MockEngine {
        return MockEngine { request ->
            assertEquals(
                config.locale,
                request.headers[HttpHeaders.AcceptLanguage]
            )
            assertEquals(
                config.apiKey,
                request.headers["x-api-key"]
            )
            assertEquals(
                ContentType.Application.OGAppKit.json(2).toString(),
                request.headers[HttpHeaders.Accept]
            )

            handler(request)
        }
    }
}

private fun assertIsFailureWithException(result: Result<*>) {
    assertIs<Result.Failure<*>>(result)
    assertIs<Exception>(result.failure)
}
