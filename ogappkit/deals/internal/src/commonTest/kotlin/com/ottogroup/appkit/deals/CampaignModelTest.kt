package com.ottogroup.appkit.deals

import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.deals.data.ResolvedCampaign
import com.ottogroup.appkit.deals.model.Asset
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import com.ottogroup.appkit.deals.model.Deal
import com.ottogroup.appkit.deals.model.Deals
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

class CampaignModelTest {
    private val jsonSerializer = lenientJson

    @Test
    fun `Campaign will resolve references`() {
        val actual: ResolvedCampaign = jsonSerializer.decodeFromString(campaignJson)
        val expected = Campaign(
            id = "1234567890",
            banner = Campaign.Banner(
                id = "staticTop1",
                image = Asset.Image(url = "https://picsum.photos/seed/banner-shake/300/200"),
                title = Asset.Text(value = "Shake for deals!"),
                subtitle = Asset.Text(value = "Deals now available"),
                index = 0
            ),
            startDate = Instant.parse("2024-10-17T12:54:40.856233821Z"),
            endDate = Instant.parse("2024-10-23T12:54:40.856233821Z"),
            title = Asset.Text("Campaign title"),
            game = Campaign.Game.ShakeADeal(
                title = Asset.Text(value = "Shake for deals!"),
                text = Asset.Text(
                    value = "Instructions: play the shake-a-deal game!\r\n1. Shake your phone.\r\n2. Get your deals!"
                ),
                background = listOf(
                    Asset.Filter.Blur,
                    Asset.Color(value = "#880000FF")
                ),
                foreground = listOf(
                    Asset.Color(value = "#880000FF")
                ),
                onboarding = Campaign.Game.Onboarding(
                    content = Asset.Video(url = "https://videos.pexels.com/video-files/5512609/5512609-hd_1080_1920_25fps.mp4")
                ),
                countdown = 0,
                content = Asset.Image(
                    url = "https://picsum.photos/seed/deal-1234567890-game-image/300/200"
                ),
                finishAnimation = Asset.Lottie(
                    url = "https://www.youtube.com/watch?v=F_wobMVVdfY"
                )
            ),
            deals = Deals(
                items = listOf(
                    Deal.Promo(
                        id = "1234567890",
                        title = Asset.Text(value = "Deal number 1"),
                        url = "https://www.youtube.com/watch?v=F_wobMVVdfY",
                        image = Asset.Image(
                            url = "https://picsum.photos/seed/deal-1234567890-image/300/200"
                        ),
                        additionalInfo = Asset.Text(
                            value = "Check legal stuff <a href=\"#legalText\">here</a>."
                        ),
                        legalText = Asset.Text(
                            value = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        ),
                        legalHeader = null,
                        ctaText = Asset.Text("ctaText")
                    )
                ),
                startDate = Instant.parse("2024-10-17T12:54:40.856233821Z"),
                endDate = Instant.parse("2024-10-23T12:54:40.856233821Z"),
                endDateTitle = Asset.Text("Valid For")
            )
        )
        assertEquals(expected, actual.campaign)
    }

    @Test
    fun `Campaigns will resolve references`() {
        val actual = jsonSerializer.decodeFromString<Campaigns>(campaignsJson)
        val expected = Campaigns(
            items = listOf(
                Campaign(
                    id = "1234567890",
                    banner = Campaign.Banner(
                        id = "staticTop1",
                        image = Asset.Image(url = "https://picsum.photos/seed/banner-shake/300/200"),
                        title = Asset.Text(value = "Shake for deals!"),
                        subtitle = Asset.Text(value = "Deals now available"),
                        index = 0
                    ),
                    startDate = now.minus(1.days),
                    endDate = now.plus(5.days),
                    title = Asset.Text("Campaign title"),
                    game = Campaign.Game.ShakeADeal(
                        title = Asset.Text(value = "Shake for deals!"),
                        text = Asset.Text(
                            value = "Instructions: play the shake-a-deal game!\r\n1. Shake your phone.\r\n2. Get your deals!"
                        ),
                        background = listOf(
                            Asset.Filter.Blur,
                            Asset.Color(value = "#880000FF")
                        ),
                        foreground = listOf(
                            Asset.Color(value = "#880000FF")
                        ),
                        onboarding = Campaign.Game.Onboarding(
                            content = Asset.Video(url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ")
                        ),
                        countdown = 0,
                        content = Asset.Image(
                            url = "https://picsum.photos/seed/deal-1234567890-game-image/300/200"
                        ),
                        finishAnimation = Asset.Lottie(
                            url = "https://www.youtube.com/watch?v=F_wobMVVdfY"
                        )
                    ),
                    deals = Deals(
                        items = listOf(
                            Deal.Promo(
                                id = "1234567890",
                                title = Asset.Text(value = "Deal number 1"),
                                url = "https://www.youtube.com/watch?v=F_wobMVVdfY",
                                image = Asset.Image(
                                    url = "https://picsum.photos/seed/deal-1234567890-image/300/200"
                                ),
                                additionalInfo = Asset.Text(
                                    value = "Check legal stuff <a href=\"#legalText\">here</a>."
                                ),
                                legalText = Asset.Text(
                                    value = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
                                ),
                                legalHeader = null,
                                ctaText = Asset.Text("ctaText")
                            )
                        ),
                        startDate = now.minus(1.days),
                        endDate = now.plus(5.days),
                        endDateTitle = Asset.Text("Valid For")
                    )
                )
            )
        )
        assertEquals(expected, actual)
    }
}

private val now = Clock.System.now()

internal val campaignJson = """
    {
    "id": "1234567890",
    "randomkey": "randomValue",
    "banner": {
        "id": "staticTop1",
        "randomkey": "randomValue",
        "image": "${"$"}assets://images/banner",
        "title": "${"$"}assets://texts/banner-title",
        "subtitle": "${"$"}assets://texts/banner-subtitle",
        "index": 0
    },
    "startDate": "2024-10-17T12:54:40.856233821Z",
    "endDate": "2024-10-23T12:54:40.856233821Z",
    "title": "${"$"}assets://texts/campaign-title",
    "game": {
        "type": "ShakeADeal",
        "randomkey": "randomValue",
        "countdown": 0,
        "title": "${"$"}assets://texts/game-title",
        "text": "${"$"}assets://texts/game-text",
        "image": "${"$"}assets://images/game-image",
        "background": [
            "${"$"}assets://filters/game-bg",
            "${"$"}assets://colors/game-bg"
        ],
        "foreground": [
            "${"$"}assets://colors/game-bg"
        ],
        "onboarding": {
            "welcome": "to the new deals",
            "content": "${"$"}assets://videos/video-1"
        },
        "content": "${"$"}assets://images/game-image",
        "finishAnimation": "${"$"}assets://lotties/lottie-1"
    },
    "deals": {
        "startDate": "2024-10-17T12:54:40.856233821Z",
        "endDate": "2024-10-23T12:54:40.856233821Z",
        "endDateTitle": "${"$"}assets://texts/endDateTitle",
        "items": [
            {
                "id": "1234567890",
                "randomkey": "randomValue",
                "type": "promo",
                "image": "${"$"}assets://images/deal-1234567890-image",
                "title": "${"$"}assets://texts/deal-1234567890-title",
                "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                "ctaText": "${"$"}assets://texts/deal-ctaText"
            },
            {
                "id": "1234567890",
                "type": "random",
                "image": "${"$"}assets://images/deal-1234567890-image",
                "title": "${"$"}assets://texts/deal-1234567890-title",
                "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                "ctaText": "${"$"}assets://texts/deal-ctaText"
            }
        ]
    },
    "assets": {
        "randomkey": "randomValue",
        "images": {
            "banner": {
                "key": "value",
                "url": "https://picsum.photos/seed/banner-shake/300/200"
            },
            "deal-1234567890-image": {
                "url": "https://picsum.photos/seed/deal-1234567890-image/300/200"
            },
            "game-image": {
                "url": "https://picsum.photos/seed/deal-1234567890-game-image/300/200"
            },
            "game-image2": {
                "value": "https://picsum.photos/seed/deal-1234567890-game-image/300/200"
            }
        },
        "videos": {
            "video-1": {
                "key": "value",
                "url": "https://videos.pexels.com/video-files/5512609/5512609-hd_1080_1920_25fps.mp4"
            },
            "video-2": {
                "value": "https://media.istockphoto.com/id/1309628270/de/video/countdown-5-bis-0-mit-animierten-bunten-zahlen.mp4?s=mp4-640x640-is&k=20&c=xqjjQ9JHoTS0q7kUy2QlPZuqSfxG-rapHw0knktY0xM="
            }
        },
        "lotties": {
            "lottie-1": {
                "key": "value",
                "url": "https://www.youtube.com/watch?v=F_wobMVVdfY"
            },
            "lottie-2": {
                "file": "https://www.youtube.com/watch?v=F_wobMVVdfY"
            }
        },
        "texts": {
            "banner-title": {
                "value": "Shake for deals!"
            },
            "banner-subtitle": {
                "value": "Deals now available"
            },
            "game-title": {
                "key": "value",
                "value": "Shake for deals!"
            },
            "campaign-title": {
                "key": "value",
                "value": "Campaign title"
            },
            "endDateTitle": {"value": "Valid For"},
            "game-title2": {
                "text": "Shake for deals!"
            },
            "game-text": {
                "value": "Instructions: play the shake-a-deal game!\r\n1. Shake your phone.\r\n2. Get your deals!"
            },
            "deal-1234567890-title": {
                "value": "Deal number 1"
            },
            "deal-1234567890-additionalInfo": {
                "value": "Check legal stuff <a href=\"#legalText\">here</a>."
            },
            "deal-1234567890-legalText": {
                "value": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
            },
            "deal-ctaText": {
                "value": "ctaText"
            }
        },
        "colors": {
            "game-bg": {
                "key": "value",
                "value": "#880000FF"
            },
            "game-bg2": {
                "hex": "#880000FF"
            }
        },
        "filters": {
            "game-bg": {
                "key": "value",
                "type": "blur"
            },
            "game-bg2": {
                "value": "blur"
            }
        }
    }
}
""".trimIndent()

internal val campaignsJson = """
   {
   "items": [
       {
           "id": "1234567890",
           "randomkey": "randomValue",
           "banner": {
               "id": "staticTop1",
               "randomkey": "randomValue",
               "image": "${"$"}assets://images/banner",
               "title": "${"$"}assets://texts/banner-title",
               "subtitle": "${"$"}assets://texts/banner-subtitle",
               "index": 0
           },
           "startDate": "${now - 1.days}",
           "endDate": "${now + 5.days}",
           "title": "${"$"}assets://texts/campaign-title",
           "game": {
               "type": "ShakeADeal",
               "randomkey": "randomValue",
               "countdown": 0,
               "title": "${"$"}assets://texts/game-title",
               "text": "${"$"}assets://texts/game-text",
               "image": "${"$"}assets://images/game-image",
               "background": [
                   "${"$"}assets://filters/game-bg",
                   "${"$"}assets://colors/game-bg"
               ],
               "foreground": [
                   "${"$"}assets://colors/game-bg"
               ],
               "onboarding": {
                   "welcome": "to the new deals",
                   "content": "${"$"}assets://videos/onboarding"
               },
               "content": "${"$"}assets://images/game-image",
               "finishAnimation": "${"$"}assets://lotties/lottie-1"
           },
           "deals": {
                "startDate": "${now - 1.days}",
                "endDate": "${now + 5.days}",
                "endDateTitle": "${"$"}assets://texts/endDateTitle",
                "items": [
                   {
                       "id": "1234567890",
                       "randomkey": "randomValue",
                       "type": "promo",
                       "image": "${"$"}assets://images/deal-1234567890-image",
                       "title": "${"$"}assets://texts/deal-1234567890-title",
                       "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                       "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                       "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                       "ctaText": "${"$"}assets://texts/deal-ctaText"
                   },
                   {
                       "id": "1234567890",
                       "type": "random",
                       "image": "${"$"}assets://images/deal-1234567890-image",
                       "title": "${"$"}assets://texts/deal-1234567890-title",
                       "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                       "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                       "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                       "ctaText": "${"$"}assets://texts/deal-ctaText"
                   }
               ]
           },
           "assets": {
               "randomkey": "randomValue",
               "images": {
                   "banner": {"key": "value", "url": "https://picsum.photos/seed/banner-shake/300/200"},
                   "deal-1234567890-image": {"url": "https://picsum.photos/seed/deal-1234567890-image/300/200"},
                   "game-image": {"url": "https://picsum.photos/seed/deal-1234567890-game-image/300/200"},
                   "game-image2": {"value": "https://picsum.photos/seed/deal-1234567890-game-image/300/200"}
               },
               "videos": {
                   "video-1": {"key": "value", "url": "https://www.youtube.com/watch?v=F_wobMVVdfY"},
                   "video-2": {"value": "https://www.youtube.com/watch?v=F_wobMVVdfY"},
                   "onboarding": {"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
               },
               "lotties": {
                   "lottie-1": {"key": "value", "url": "https://www.youtube.com/watch?v=F_wobMVVdfY"},
                   "lottie-2": {"file": "https://www.youtube.com/watch?v=F_wobMVVdfY"}
               },
               "texts": {
                   "banner-title": {"value": "Shake for deals!"},
                   "banner-subtitle": {"value": "Deals now available"},
                   "campaign-title": {"value": "Campaign title"},
                   "endDateTitle": {"value": "Valid For"},
                   "game-title": {"key": "value", "value": "Shake for deals!"},
                   "game-title2": {"text": "Shake for deals!"},
                   "game-text": {"value": "Instructions: play the shake-a-deal game!\r\n1. Shake your phone.\r\n2. Get your deals!"},
                   "deal-1234567890-title": {"value": "Deal number 1"},
                   "deal-1234567890-additionalInfo": {"value": "Check legal stuff <a href=\"#legalText\">here</a>."},
                   "deal-1234567890-legalText": {"value": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."},
                   "deal-ctaText": {"value": "ctaText"}
               },
               "colors": {
                   "game-bg": {"key": "value", "value": "#880000FF"},
                   "game-bg2": {"hex": "#880000FF"}
               },
               "filters": {
                   "game-bg": {"key": "value", "type": "blur"},
                   "game-bg2": {"value": "blur"}
               }
           }
       },
       {
           "id": "1234567891",
           "banner": {
               "id": "staticBottom2",
               "image": "${"$"}assets://images/banner",
               "title": "${"$"}assets://texts/banner-title",
               "subtitle": "${"$"}assets://texts/banner-subtitle"
           },
           "startDate": "${now - 1.days}",
           "endDate": "${now + 5.days}",
           "title": "${"$"}assets://texts/campaign-title",
           "game": {
               "type": "ScratchADeal",
               "scratchPercentage": 0.65,
               "title": "${"$"}assets://texts/game-title",
               "text": "${"$"}assets://texts/game-text",
               "image": "${"$"}assets://images/game-image",
               "background": [
                   "${"$"}assets://images/game-bg"
               ],
               "foreground": [
                   "${"$"}assets://images/game-bg"
               ],
                "content": "${"$"}assets://images/game-image",
                "finishAnimation": "${"$"}assets://lotties/lottie-1"
           },
           "deals": {
                "startDate": "${now - 1.days}",
                "endDate": "${now + 5.days}",
                "items": [
                   {
                       "id": "1234567890",
                       "randomkey": "randomValue",
                       "type": "promo",
                       "image": "${"$"}assets://images/deal-1234567890-image",
                       "title": "${"$"}assets://texts/deal-1234567890-title",
                       "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                       "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                       "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                       "ctaText": "${"$"}assets://texts/deal-ctaText"
                   },
                   {
                       "id": "1234567890",
                       "type": "random",
                       "image": "${"$"}assets://images/deal-1234567890-image",
                       "title": "${"$"}assets://texts/deal-1234567890-title",
                       "url": "https://www.youtube.com/watch?v=F_wobMVVdfY",
                       "additionalInfo": "${"$"}assets://texts/deal-1234567890-additionalInfo",
                       "legalText": "${"$"}assets://texts/deal-1234567890-legalText",
                       "ctaText": "${"$"}assets://texts/deal-ctaText"
                   }
               ]
           },
           "assets": {
               "images": {
                   "banner": {"url": "https://picsum.photos/seed/banner-scratch/300/200"},
                   "deal-1234567891-image": {"url": "https://picsum.photos/seed/deal-1234567891-image/300/200"},
                   "game-image": {"url": "https://picsum.photos/seed/deal-1234567891-game-image/300/200"},
                   "game-image2": {"value": "https://picsum.photos/seed/deal-1234567891-game-image/300/200"},
                   "game-bg": {"url": "https://picsum.photos/seed/game-background/300/600"}
               },
               "videos": {
                   "video-1": {"url": "https://www.youtube.com/watch?v=F_wobMVVdfY"},
                   "video-2": {"value": "https://www.youtube.com/watch?v=F_wobMVVdfY"}
               },
               "lotties": {
                   "lottie-1": {"url": "https://www.youtube.com/watch?v=F_wobMVVdfY"},
                   "lottie-2": {"file": "https://www.youtube.com/watch?v=F_wobMVVdfY"}
               },
               "texts": {
                   "banner-title": {"value": "Scratch your deals!"},
                   "banner-subtitle": {"value": "Deals now available"},
                   "campaign-title": {"value": "Campaign title"},
                   "game-title": {"value": "Scratch the image!"},
                   "game-title2": {"text": "Scratch the image!"},
                   "game-text": {"value": "Instructions: play the scratch-a-deal game!\r\n1. Scratch the image.\r\n2. Get your deals!"},
                   "deal-1234567891-title": {"value": "Deal number 1"},
                   "deal-1234567891-additionalInfo": {"value": "Check legal stuff <a href=\"#legalText\">here</a>."},
                   "deal-1234567891-legalText": {"value": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."},
                   "deal-ctaText": {"value": "ctaText"}
               },
               "colors": {
                   "game-bg": {"value": "#880000FF"},
                   "game-bg2": {"hex": "#880000FF"}
               },
               "filters": {
                   "game-bg": {"type": "blur"},
                   "game-bg2": {"value": "blur"}
               }
           }
       },
       {
           "id": "1234567892",
           "startDate": "${now + 1.days}",
           "endDate": "${now + 15.days}",
           "title": "${"$"}assets://texts/game-title"
       },
       {
           "id": "1234567893",
           "startDate": "${now + 1.days}",
           "endDate": "${now + 15.days}",
           "title": "${"$"}assets://texts/game-title"
       },
       {
           "id": "1234567893",
           "startDate": "${now + 1.days}",
           "endDate": "${now + 15.days}",
           "title": "${"$"}assets://texts/game-title",
           "banner": {
               "id": "1234567890"
           }
       },
       {
           "id": "1234567894",
           "startDate": "${now + 1.days}",
           "endDate": "${now + 15.days}",
           "title": "${"$"}assets://texts/campaign-title",
           "game": {
               "type": "RandomGame",
               "title": "${"$"}assets://texts/game-title",
               "text": "${"$"}assets://texts/game-text",
               "image": "${"$"}assets://images/game-image",
               "background": [
                   "${"$"}assets://images/game-bg"
               ],
               "foreground": [
                   "${"$"}assets://images/game-bg"
               ],
                "content": "${"$"}assets://images/game-image",
                "finishAnimation": "${"$"}assets://lotties/lottie-1"
           }
       }
   ]
   }
""".trimIndent()
