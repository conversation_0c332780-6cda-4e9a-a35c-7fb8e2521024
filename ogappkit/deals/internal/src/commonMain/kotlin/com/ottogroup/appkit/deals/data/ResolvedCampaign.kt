package com.ottogroup.appkit.deals.data

import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.references.ReferenceResolvingJsonTransformationSerializer
import kotlin.jvm.JvmInline
import kotlinx.serialization.Serializable

internal object CampaignReferenceResolver : ReferenceResolvingJsonTransformationSerializer<Campaign>(
    Campaign.serializer(),
    typeResolver = { base, path ->
        // images - > image
        // videos - > video
        // lotties - > lottie
        // texts - > text
        // colors - > color
        // filters - > filter
        path.first().removeSuffix("s")
    }
)

@JvmInline
@Serializable
internal value class ResolvedCampaign(@Serializable(with = CampaignReferenceResolver::class) val campaign: Campaign)
