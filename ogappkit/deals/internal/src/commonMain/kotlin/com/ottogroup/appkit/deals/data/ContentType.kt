package com.ottogroup.appkit.deals.data

import io.ktor.http.ContentType
import io.ktor.http.HeaderValueParam

internal val ContentType.Application.OGAppKit: OGAppKitContentType
    get() = OGAppKitContentType

internal object OGAppKitContentType {

    fun json(version: Int) = ContentType(
        contentType = "application",
        contentSubtype = "vnd.ogappkit+json",
        parameters = listOf(HeaderValueParam("version", version.toString()))
    )
}
