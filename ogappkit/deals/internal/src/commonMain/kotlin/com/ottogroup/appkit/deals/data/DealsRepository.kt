package com.ottogroup.appkit.deals.data

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapLatest

internal interface DealsRepository {
    fun getCampaigns(): Flow<Result<Campaigns>>
    fun getCampaign(campaignId: String): Flow<Result<Campaign>>
}

@OptIn(ExperimentalCoroutinesApi::class)
internal class DealsRepositoryImpl(
    private val configProvider: DealsConfigProvider,
    private val remoteData: DealsNetworkDataSource
) : DealsRepository {
    override fun getCampaigns(): Flow<Result<Campaigns>> {
        return configProvider.configState.mapLatest {
            remoteData.getCampaigns()
        }
    }

    override fun getCampaign(campaignId: String): Flow<Result<Campaign>> {
        return configProvider.configState.mapLatest {
            remoteData.getCampaign(campaignId)
        }
    }
}
