package com.ottogroup.appkit.deals.data

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.http.OGAppKitHeadersPlugin
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.cache.HttpCache
import io.ktor.client.plugins.cache.storage.CacheStorage
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.isSuccess
import io.ktor.serialization.kotlinx.json.json

internal class DealsNetworkDataSource(
    private val configProvider: DealsConfigProvider,
    ogKitHeaders: OGAppKitHeadersPlugin,
    customEngine: HttpClientEngine? = null,
) {
    private val cache by lazy { CacheStorage.Unlimited() }

    private val client: HttpClient by lazy {
        val config: HttpClientConfig<*>.() -> Unit = {
            install(ogKitHeaders)
            install(HttpCache) {
                // FIXME insert file cache here
                privateStorage(cache)
            }
            install(ContentNegotiation) {
                val json = lenientJson
                json(
                    json,
                    ContentType.Application.OGAppKit.json(2)
                )
                json(json)
            }
        }
        if (customEngine != null) {
            HttpClient(customEngine, config)
        } else {
            HttpClient(config)
        }
    }

    private val config get() = configProvider.configState.value

    private fun validateParameters() {
        require(config.baseUrl.isNotEmpty()) { "Base URL is empty" }
        require(config.locale.isNotEmpty()) { "Locale is empty" }
        require(config.apiKey.isNotEmpty()) { "API key is empty" }
    }

    private suspend inline fun <reified T : Any> get(
        urlString: String,
        crossinline errorMessage: () -> String,
    ): Result<T> = resultFor {
        validateParameters()

        val response: HttpResponse = client.get(urlString) {
            headers {
                append("Accept-Language", config.locale)
                append("x-api-key", config.apiKey)
            }
            accept(ContentType.Application.OGAppKit.json(2))
        }

        if (response.status.isSuccess()) {
            response.body<T>()
        } else {
            throw Exception("${errorMessage()}: ${response.status}")
        }
    }

    suspend fun getCampaigns(): Result<Campaigns> {
        return get(
            urlString = "${config.baseUrl}/campaigns",
            errorMessage = { "Failed to fetch campaigns" },
        )
    }

    suspend fun getCampaign(campaignId: String): Result<Campaign> {
        return get<ResolvedCampaign>(
            urlString = "${config.baseUrl}/campaigns/$campaignId",
            errorMessage = { "Failed to fetch campaign $campaignId" },
        ).map { it.campaign }
    }
}
