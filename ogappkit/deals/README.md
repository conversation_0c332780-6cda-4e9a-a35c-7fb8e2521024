# Deals

How to use the Deals SDK.

## Obtain the `OGDeals` instance

```kotlin
// Android
val deals = OGAppKitSdk.init(androidApplication()).deals()
```

```swift
// Swift
let deals = OGAppKitSdk.shared.deals()
```

## Configure it

```kotlin
deals.configure(
    DealsConfig(
        baseUrl = config.apiUrl ?: "",
        locale = tenantRepository.tenant.identifier,
        apiKey = "apiKey"
    )
)
```

Where

- `baseUrl` is the base url defined in the app config
- `tenant` is the current tenant used by the app
- `apiKey` the apiKey defined for the Deals feature

## Request data

### Campaigns

After configuring the SDK, the apps should request the campaign data when they show the banners in the assortment. To
do that you should use `getCampaigns()`.

It is also possible to request a specific campaign via `getCampaign(campaignId: String)`.

This data is exposed as a `Flow` in Kotlin, which is translated to an `AsyncSequence` in Swift.
