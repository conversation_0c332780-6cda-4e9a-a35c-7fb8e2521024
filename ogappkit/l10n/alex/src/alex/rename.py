import json
import questionary
from pathlib import Path
from alex.customValidator import old_key_validator,new_key_validator
from alex.mode import Mode
from alex.util import loadDefaultTranslationData
from alex.util import writeToChanges
from alex.config import Config
from alex.util import key_format_info
import alex.style as style

def renameKeys(config: Config, importFilePath: str):
    data = {}
    with open(importFilePath, "r", encoding="utf-8") as f:
        data = json.load(f)

    for key in data:
        renameKey(config, key, data[key])

def renameKey(config: Config, old_key: str = "", new_key: str = ""):
    branchName = config.branch_name
    data = loadDefaultTranslationData(config)
    keys = list(data.keys())

    if not old_key:
        # Use questionary for autocomplete prompt
        old_key = questionary.autocomplete(
            "Enter old key name:",
            choices=keys,
            match_middle=False,
            validate=lambda text: old_key_validator(text, keys=keys),
            style=style.custom_style_fancy
        ).ask()

    key_format_info()

    if not new_key:
        new_key = questionary.autocomplete(
            "Enter new key name",
            validate=new_key_validator,
            complete_in_thread=True,
            match_middle=False,
            choices=keys,
            style=style.custom_style_fancy
        ).ask()

    # Process all translation files
    translation_files = Path(config.translationDir).glob("*.json")

    for file_path in translation_files:
        # Read file
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Check if old key exists
        if old_key not in data:
            questionary.print(f"Key {old_key} not found in {file_path.name}, skipping...", style="bold italic fg:red")
            return

        # Check if new key already exists
        if new_key in data:
            questionary.print(f"Key {new_key} already exists in {file_path.name}, skipping...", style="bold italic fg:red")
            continue

        # Rename key
        data[new_key] = data.pop(old_key)

        # Write updated data back to file
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, sort_keys=True, indent=2, ensure_ascii=False)

        print(f"Renamed key in {file_path.name}")

    writeToChanges(
        branchName, old_key + " " + new_key, Mode.RENAME.symbol, config.changesDir
    )
    questionary.print(
        f"Successfully renamed key '{old_key}' to '{new_key}' in all translation files", style="bold italic fg:green"
    )
