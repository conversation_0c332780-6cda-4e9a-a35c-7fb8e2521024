from questionary import Validator, ValidationError, prompt

class NotEmptyValidator(Validator):
    def validate(self, document):
        if len(document.text) == 0:
            raise ValidationError(
                message="Please enter a value",
                cursor_position=len(document.text),
            )
        
def old_key_validator(key_name:str, keys: list[str]) -> str | bool:
    
    if len(key_name) == 0 or key_name == "":
        return "Please enter a value"

    elif key_name.find(" ") != -1:
        return "Key cannot contain spaces"

    elif key_name not in keys:
        return "Key does not exist"

    else:
        return True           
    
def new_key_validator(key_name) -> str| bool:

    if len(key_name) == 0 or key_name == "":
        return "Please enter a value"

    elif key_name.find(" ") != -1:
        return "Key cannot contain spaces"

    else:
        return True    