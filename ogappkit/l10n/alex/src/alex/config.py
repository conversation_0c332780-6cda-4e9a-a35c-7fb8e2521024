
class Config:
    def __init__(self, branch_name, default_lang="en", translationDir="translations", changesDir="changes"):
        self.default_lang = default_lang
        self.branch_name = branch_name
        # Supported languages
        self.langs = ["en","de", "at", "ch", "cs", "es", "fi", "hu", "fr", "nl", "pl", "ro", "sk", "sv"]
        self.translationDir = translationDir
        self.changesDir = changesDir