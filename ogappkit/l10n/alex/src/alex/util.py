import asyncio
import json
from pathlib import Path
import re
import time
import os
import questionary
import alex.style as style
from alex.mode import Mode
from alex.translate import googleTranslate, language_to_locale_map
from alex.config import Config
from halo import Halo


def writeToChanges(fileName: str, changed: str, symbol: str, changes_dir: str):
    Path(changes_dir).mkdir(exist_ok=True)
    with open(f"{changes_dir}/{fileName}.txt", "a", encoding="utf-8") as file:
        file.write("\n")
        file.write("# " + time.strftime("%Y-%m-%d %H:%M:%S\n"))
        file.write(f"{symbol} " + changed)


def askForJobName(changes_dir: str) -> str:
    # Get all file names in translations directory with .txt without extension
    files = [f.stem for f in Path(changes_dir).glob("*.txt")]
    branch_name = os.popen("git branch --show-current").read().strip()
    files.append(branch_name)
    # Ask for branch name
    branch_name = questionary.autocomplete(
        "Enter the name of the changelog file (meaningful name or ticket number it will later be used for the Phrase job creation) and press enter:",
        choices=files,
        default=branch_name,
        match_middle=False,
        validate=lambda text: text != "",
        style=style.custom_style_fancy
    ).ask()

    # Replace / to _ in branch name
    branch_name = branch_name.replace("/", "_")
    return branch_name


def processKeyTranslation(key_name: str, value: str, config: Config, mode: Mode):
    langs = config.langs
    spinner = Halo(text='Translating value', spinner='dots')
    spinner.start()

    # Extract all placeholders from the string
    pattern = r'%{(.*?)}'
    placeholders = re.findall(pattern, value)

    # Replace all placeholders with unique temporary markers
    # This prevents Google Translate from modifying them
    temp_value = value
    placeholder_map = {}

    for i, ph in enumerate(placeholders):
        marker = f"___{i}__"
        temp_value = temp_value.replace(f"%{{{ph}}}", marker)
        placeholder_map[marker] = f"%{{{ph}}}"

    # Translate the text with placeholders removed
    translations = asyncio.run(googleTranslate(temp_value, langs, config.default_lang))
    spinner.stop()
    # Restore the original values in all translations
    for lang in langs:
        for marker, ph in placeholder_map.items():
            translations[lang] = translations[lang].replace(marker, ph)

    # Display translations to user
    print("Translations:")
    for lang in langs:
        print(f" {lang} -> {translations[lang]}")

    choice = questionary.select(
        "What would you like to do with this translation?",
        choices=["Add", "Skip"],
        use_shortcuts=True,
        style=style.custom_style_fancy
    ).ask()
    addIt = True if choice == "Add" else False

    if addIt:
        symbol = mode.symbol
        action = "added" if mode == Mode.ADD else "replaced"
        value_with_escaped_newlines = value.replace("\n", "\\n")
        writeKeyToTranslationFile(key_name, langs, translations, config)
        writeToChanges(
            config.branch_name, f'{key_name} "{value_with_escaped_newlines}"', symbol, config.changesDir
        )

        questionary.print(
            f"Successfully {action} {key_name}: {value} to {config.branch_name}", style="bold italic fg:green"
        )


def writeKeyToTranslationFile(
    key_name: str, langs: list[str], translations: dict[str, str], config: Config
):
    for lang in langs:
        # append data or replace it to existing file
        fileName = language_to_locale_map[lang]
        json_file = Path(config.translationDir) / f"{fileName}.json"
        json_file.parent.mkdir(parents=True, exist_ok=True)
        if not json_file.exists():
            with open(json_file, "w", encoding="utf-8") as f:
                json.dump({}, f, indent=2, ensure_ascii=False)

        with open(json_file, "r", encoding="utf-8") as f:
            existing_data = json.load(f)
            # Will update if exists, add if doesn't exist
            existing_data[key_name] = translations[lang]

        with open(json_file, "w", encoding="utf-8") as f:
            json.dump({key: value for key, value in sorted(existing_data.items())}, f, indent=2, ensure_ascii=False)


def loadDefaultTranslationData(config: Config) -> dict[str, str]:
    defaultFile = language_to_locale_map[config.default_lang]
    default_file_path = Path(config.translationDir) / f"{defaultFile}.json"

    with open(default_file_path) as f:
        data = json.load(f)
    return data


def key_format_info():
    print("""
    The new key name should follow the format:
    <feature>.<screen>.<element>(.accessibility)
    <feature>.<screen>.<element>(.accessibility).<project>
    <feature>.<screen>.<element>(.accessibility).<project>
    <feature>.<screen>.<element>(.accessibility).<project>.<platform>
    <feature>.<screen>.<element>(.accessibility).<project>.<platform>
    deals.onboarding.button
    deals.onboarding.buttonOk.accessibility
    deals.onboarding.buttonLogin.accessibility

    Possible project names:
    BON, BPX, CRE, HEI, LAS, MAN, SAN, SHE, WIT, YLFLSE, YLFLNL
    """)
