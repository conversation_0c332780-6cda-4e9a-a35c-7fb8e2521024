import json
import questionary
from alex.util import loadDefaultTranslationData
from alex.mode import Mode
from alex.util import writeToChanges
from alex.config import Config
from pathlib import Path
from alex.customValidator import old_key_validator
import alex.style as style

def deleteKey(config: Config, key_name: str = ""):
    translation_files = Path(config.translationDir).glob("*.json")

    if not key_name:
        global keys
        keys = list(loadDefaultTranslationData(config).keys())

        if not keys:
            questionary.print(f"Keys not found in default language file", style="bold italic fg:red")
            return

        key_name  = questionary.autocomplete(
            "Search for key to be deleted: (Use tab key for autocomplete)",
            choices=keys,
            match_middle=False,
            validate= lambda text: old_key_validator(text, keys=keys),
            style=style.custom_style_fancy
        ).ask()

        if not key_name:
            return

    langNames = []
    for file in translation_files:
        with open(file) as f:
            data = json.load(f)
            if key_name in data:
                del data[key_name]
                with open(file, "w", encoding="utf-8") as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    langNames.append(file.stem)
            else:
                questionary.print(f"Key {key_name} not found in {file.stem}, skipping...", style="bold italic fg:red")

    if langNames:
        questionary.print(f"Successfully deleted {key_name} from {langNames}", style="bold italic fg:green")
    else:
        questionary.print(f"Key {key_name} not found in any language file", style="bold italic fg:red")

    writeToChanges(config.branch_name, key_name, Mode.DELETE.symbol, config.changesDir)
