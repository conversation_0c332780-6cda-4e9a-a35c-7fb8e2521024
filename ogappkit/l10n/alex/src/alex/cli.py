import typer
import questionary
import alex.style as style
from alex.add import addKey, importKeys
from alex.replace import replaceValues
from alex.replace import replaceValue
from alex.rename import rename<PERSON><PERSON>, renameKeys
from alex.delete import deleteKey
from alex.config import Config
from alex.util import askForJobName

def alex(
    add_arg: str = typer.Option("", "--add", "-a", help="Keys file to add"),
    delete_arg: str = typer.Option("", "--delete", "-d", help="Key to delete"),
    replace_arg: str = typer.Option(
        "", "--replace", "-rp", help="Values file to replace"
    ),
    rename_arg: str = typer.Option("", "--rename", "-rn", help="Keys file to rename"),
    job_arg: str = typer.Option("", "--job", "-j", help="This is the name of the file name where the changelog will be saved"),
    translations: str = typer.Option(
        "translations", "--translations", "-t", help="Translations directoy"
    ),
    changes: str = typer.Option("changes", "--changes", "-c", help="Change dir"),
    default_lang: str = typer.Option("", "--default-lang", "-lang", help="Default language")
):

    mode = ""
    import_arg = ""
    if add_arg:
        mode = "add"
        import_arg = add_arg
    elif delete_arg:
        mode = "delete"
        import_arg = delete_arg
    elif replace_arg:
        mode = "replace"
        import_arg = replace_arg
    elif rename_arg:
        mode = "rename"
        import_arg = rename_arg


    print("""
     _       __     __                             __           ___    __    _______  __
    | |     / /__  / /________  ____ ___  ___     / /_____     /   |  / /   / ____/ |/ /
    | | /| / / _ \\/ / ___/ __ \\/ __ `__ \\/ _ \\   / __/ __ \\   / /| | / /   / __/  |   /
    | |/ |/ /  __/ / /__/ /_/ / / / / / /  __/  / /_/ /_/ /  / ___ |/ /___/ /___ /   |
    |__/|__/\\___/_/\\___/\\____/_/ /_/ /_/\\___/   \\__/\\____/  /_/  |_/_____/_____//_/|_|

                    -Your personal App Localization Expert-
    """)

    branch_name = job_arg if job_arg else askForJobName(changes)
    config = Config(branch_name=branch_name, translationDir=translations, changesDir=changes)

    if default_lang:
        if default_lang not in config.langs:
            defaultLang =  questionary.select(
                "Invalid default language, please choose from",
                choices=config.langs,
                use_shortcuts=True,
                style=style.custom_style_fancy
            ).ask()
            config.default_lang = defaultLang

    # Handle command-line arguments if provided
    if mode and mode == "add":
        importKeys(config, import_arg)
        return

    elif mode and mode == "delete":
        deleteKey(config, import_arg)
        return

    elif mode and mode == "replace":
        replaceValues(config, import_arg)
        return

    elif mode and mode == "rename":
        renameKeys(config, import_arg)
        return

    elif mode:
        print("Invalid mode, please try again")
        return

    if not default_lang:
        defaultLang =  questionary.select(
                "What is the default language? \n This is the language from which the added values will be translated into the other languages.",
                choices=config.langs,
                use_shortcuts=True,
                style=style.custom_style_fancy
            ).ask()

        config.default_lang = defaultLang

    # Interactive mode
    choices = [
        "Add new key",
        "Add keys by file",
        "Replace value",
        "Replace values by file",
        "Rename key",
        "Delete key",
        "Exit"
    ]

    while True:
        answer = questionary.select(
            "What do you want to do? (Use arrow keys or type 1-5)",
            choices=choices,
            use_shortcuts=True,
            style=style.custom_style_fancy
        ).ask()


        index = choices.index(answer)

        if index == 0:
            addKey(config)
        elif index == 1:
            print("""
            Your json file should be in the following format:
            {
            "test.key1": "Value1",
            "test.key2": "Value2"
            }
            """)
            filePath = questionary.text("Enter path to Json file",validate=lambda text: text != "" and text.find(" ") == -1).ask()
            importKeys(config, filePath)
        elif index == 2:
            replaceValue(config)
        elif index == 3:
            print("""
            Your json file should be in the following format:
            {
            "test.key1": "Value1",
            "test.key2": "Value2"
            }
            """)
            filePath = questionary.text("Enter path to Json file",validate=lambda text: text != "" and text.find(" ") == -1).ask()
            replaceValues(config, filePath)
        elif index == 4:
            renameKey(config)
        elif index == 5:
            deleteKey(config)
        elif index == 6:
            break
        else:
            print("Invalid choice, please try again")
            continue

def main():
    typer.run(alex)

if __name__ == "__main__":
    main()
