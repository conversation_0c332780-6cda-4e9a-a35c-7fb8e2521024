from googletrans import Translator

language_to_locale_map = {
    "at": "de-AT",
    "ch": "de-CH",
    "cs": "cs-CZ",
    "de": "de-DE",
    "en": "en-US",
    "es": "es-ES",
    "fi": "fi-FI",
    "fr": "fr-CH",
    "hu": "hu-HU",
    "nl": "nl-NL",
    "pl": "pl-PL",
    "ro": "ro-RO",
    "sk": "sk-SK",
    "sv": "sv-SE",
}


async def googleTranslate(
    valueText: str, destLangs: list[str], defaultLang
) -> dict[str, str]:
    translator = Translator()
    data = {}

    for destLang in destLangs:
        requestLang = destLang
        if requestLang == "at" or requestLang == "ch":
            requestLang = "de"
        translation = await translator.translate(
            valueText, dest=requestLang, src=defaultLang
        )
        data[destLang] = translation.text

    return data
