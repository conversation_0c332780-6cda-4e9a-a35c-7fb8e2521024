import json
import questionary
from alex.util import loadDefaultTranslationData, processKeyTranslation
from alex.mode import Mode
from alex.config import Config
import alex.customValidator as cv
import alex.style as style

keys: list[str] = []

def key_validator(key_name):

    if len(key_name) == 0 or key_name == "":
        return "Please enter a value"

    elif key_name.find(" ") != -1:
        return "Key cannot contain spaces"

    elif key_name not in keys:
        return "Key does not exist"

    else:
        return True

def replaceValue(config: Config, key_name: str = "", value: str = ""):
    global keys
    keys = list(loadDefaultTranslationData(config).keys())

    if not key_name:
        key_name = questionary.autocomplete(
            "Search for key to replace value: (Use tab key for autocomplete)",
            choices=keys,
            match_middle=False,
            validate=key_validator,
            style=style.custom_style_fancy
        ).ask()

    if not value:

        default_value = loadDefaultTranslationData(config).get(key_name, "")
        value = questionary.text("Enter value", default=default_value, validate=lambda text: text != "").ask()

    processKeyTranslation(key_name, value, config, Mode.REPLACE)


def replaceValues(config: Config, importFilePath: str):
    data = {}
    with open(importFilePath, "r", encoding="utf-8") as f:
        data = json.load(f)

    for key in data:
        print("Key name: ", key)
        replaceValue(config, key, data[key])
