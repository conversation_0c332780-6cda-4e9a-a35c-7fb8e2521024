import unittest
from alex.customValidator import old_key_validator


class OldKeyValidatorTest(unittest.TestCase):
    def testThatOldKeyIsValid(self):
        """
        Test that it can sum a list of integers
        """
        data = ["test.key","test.key2", "test.key3"]

        valid = old_key_validator("test.key2", data)
        self.assertEqual(valid, True)

    def testwhenKeyIsEmptyReturnErrorMessage(self):
        """
        Test that it can sum a list of integers
        """
        data = ["test.key","test.key2", "test.key3"]

        valid = old_key_validator("", data)
        self.assertEqual(valid, "Please enter a value")

    def testwhenKeyContainsSpaceReturnErrorMessage(self):
        """
        Test that it can sum a list of integers
        """
        data = ["test.key","test.key2", "test.key3"]

        valid = old_key_validator("test key", data)
        self.assertEqual(valid, "Key cannot contain spaces")    

if __name__ == '__main__':
    unittest.main()
