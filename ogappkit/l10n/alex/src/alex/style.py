import questionary

custom_style_fancy = questionary.Style([
('qmark', 'fg:#1E90FF bold'),       # token in front of the question
('question', 'orange bold italic'),               # question text
('answer', 'fg:white bold'),      # submitted answer text behind the question
('pointer', 'fg:orange bold'),     # pointer used in select and checkbox prompts
('highlighted', 'fg:orange bold'), # pointed-at choice in select and checkbox prompts
('selected', 'fg:white'),         # style for a selected item of a checkbox
])