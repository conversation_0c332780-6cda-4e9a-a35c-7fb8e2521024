import json
import questionary
from alex.mode import Mode
from alex.config import Config
from alex.util import key_format_info, processKeyTranslation, loadDefaultTranslationData
import alex.customValidator as cv
import alex.style as style
from pathlib import Path
from alex.customValidator import old_key_validator,new_key_validator

def importKeys(config: Config, importFilePath: str):
    if not importFilePath:
        questionary.print(f"Could not find Json file", style="bold italic fg:red")
    try:
        data = {}
        with open(importFilePath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        myPairList = []
        for key in data:
            myPairList.append({key: data[key]})

        addKeys(config, myPairList)

    except Exception as e:
        questionary.print(f"Error importing keys", style="bold italic fg:red")

def addKeys(config: Config, pairList):
    for pair in pairList:
        key = list(pair.keys())[0]
        value = pair[key]
        print("Key name: ", key)
        print("Value: ", value)
        processKeyTranslation(key, value, config, Mode.ADD)


def value_exists(config: Config, new_value: str) -> bool:
    """Check if the new value already exists in any of the translation files."""
    translation_files = Path(config.translationDir).glob("*.json")

    for file in translation_files:
        with open(file, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                if new_value in data.values():
                    key_of_new_value = list(data.keys())[list(data.values()).index(new_value)]
                    questionary.print(f"⚠️  Warning: The value '{new_value}' (key: '{key_of_new_value}') already exists in {file.name}", style="bold italic fg:yellow")
                    return True
            except json.JSONDecodeError:
                questionary.print(f"❌  Error: Could not read {file.name}, skipping...", style="bold italic fg:red")

    return False


def addKey(config: Config):
    key_format_info()

    keys = list(loadDefaultTranslationData(config).keys())

    key_name = questionary.autocomplete(
        "Enter key name",
        validate=new_key_validator,
        complete_in_thread=True,
        choices=keys,
        match_middle=False,
        style=style.custom_style_fancy
    ).ask()
    value = questionary.text("Enter value", validate=cv.NotEmptyValidator, multiline=True).ask()

    if value_exists(config, value):
        choice = questionary.select(
            "What would you like to do with this translation?",
            choices=["Add", "Skip"],
            use_shortcuts=True
        ).ask()

        if choice == "Skip":
            questionary.print("Operation canceled. The key was not added.", style="bold italic fg:red")
            return

    processKeyTranslation(key_name, value, config, Mode.ADD)
