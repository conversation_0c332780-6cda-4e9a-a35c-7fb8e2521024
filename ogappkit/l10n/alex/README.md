# Alex
Alex is a command-line tool that helps you manage localization data across multiple language files. It simplifies the process of adding, updating, renaming, and deleting localization keys and values.

New translations will be translated automatically in all supported locals ["en","de", "at", "ch", "cs", "es", "fi", "hu", "fr", "nl", "pl", "ro", "sk", "sv"]
It also generates a changelog file that contains all the changes made to the localization data.


# Installation
For an easy installation, you can use the provided script `alex.sh` located in the project root directory.
This script will set up a virtual environment and install the necessary dependencies for you.

## Manual Installation
python3 -m venv path/to/venv

source path/to/venv/bin/activate

pip install -e .

## Building an executable:
* pip install pyinstaller
* pyinstaller src/alex/cli.py --name alex --onefile  --collect-submodules alex

# Usage
Command Structure
alex [OPTIONS] COMMAND [ARGS]...

Available Options
Run alex --help to see the available options.

## Add Keys
* Option 1:

Run alex and select "Add new key"

* Option 2:

Batch import

Run alex -a "pathToImportJsonFile"

```
The Json file should have a following format:
{
"test.key1": "Bye {world1}",
"test.key2": "Bye {world2}"
}
```

## Rename keys

Option 1:

Run alex and select "Rename key"

Option 2:
Batch import
Run alex -rn "pathToImportJsonFile"

```
The Json file should have a following format:
{
"test.key1": "new.test.key1",
"test.key2": "new.test.key2"
}
```

## Replace values
Option 1:

Run alex and select "Replace key"

Option 2:
Batch import
Run alex -rp "pathToImportJsonFile"

```
The Json file should have a following format:

{
"test.key1": "Bye {world1}",
"test.key2": "Bye {world2}"
}
```

## Delete keys
Option 1:

Run alex and select "Delete key"

Option 2:
```
alex -d "keyToDelete"
```
## Job name
```
./alex -j feature/l10n
```
This is the name of the file name where the changelog will be saved
It should be a meaningful name or ticket number, it will later be used for the Phrase job creation

## Changes
```
./alex -c "PATH_TO_CHANGES"
```
This is the name of the file where the changes will be saved

## Translations
```
./alex -t "PATH_TO_TRANSLATIONS"
```

This is the directory where the translations will are stored
See ./docs/specs/translations for more information

## Default language
```
./alex -lang de
```
This is the language from which the added values will be translated into the other languages.
