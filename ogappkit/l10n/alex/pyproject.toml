[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "alex"
version = "0.1.0"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
]
description = "A CLI tool for managing translations and localization files."
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "typer>=0.9.0",
    "requests>=2.31.0",
    "pandas>=2.0.0",
    "python-dotenv>=1.0.0",
    "jellyfish>=1.0.1",
    "googletrans==4.0.2",
    "questionary==2.1.0",
    "pyinstaller>=6.7.0",
    "halo>=0.0.31",
    "firebase_admin>=v6.6.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
    "questionary==2.1.0",
    "googletrans==4.0.2",
    "pyinstaller>=6.7.0",
]

[project.scripts]
alex = "alex.cli:main"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyx?$'

[tool.isort]
profile = "black"
multi_line_output = 3
