# Deployment script for the project
#
# 0. generate signature key
# 1. deploy signature key to Firebase Secrets
# 2. deploys function to Firebase Functions with signature
# 3. enables webhooks on Phrase project with signature

import os
import base64
import argparse
from projects import get_deployable_projects
from project_config import ProjectConfig
from services.firebase import FirebaseService
from services.phrase import PhraseService

class Deployer:
    def __init__(self, project_config: ProjectConfig):
        self.config = project_config
        self.firebase = FirebaseService(self.config)
        self.phrase = PhraseService(self.config)

    def get_or_create_signature(self) -> str:
        """Get existing signature or create a new one"""
        existing_signature = self.firebase.get_signature_secret()
        if existing_signature:
            print("Using existing signature from Firebase Secrets")
            return existing_signature
        else:
            print("No existing signature found, generating a new one")
            secret = self._generate_signature()
            # Save signature to Firebase
            success, _ = self.firebase.save_signature_secret(secret)
            return secret

    def _generate_signature(self) -> str:
        """Generate webhook signature"""
        return base64.b64encode(os.urandom(32)).decode('utf-8')

    def generate_env(self, functions_dir: str) -> None:
        """Generate .env file for Firebase functions"""
        env_path = os.path.join(functions_dir, f'.env.{self.config.project_id}')
        with open(env_path, 'w') as env_file:
            env_file.write(f'APP_ID={self.config.name}\n')
            env_file.write(f'APP_PLATFORMS=["android","ios","web"]\n')
            print(f"✅  Generated .env file at {env_path}")

    def remove_env(self, functions_dir: str) -> None:
        """Remove .env file for Firebase functions"""
        env_path = os.path.join(functions_dir, f'.env.{self.config.project_id}')
        if os.path.exists(env_path):
            os.remove(env_path)
            print(f"✅  Removed .env file at {env_path}")
        else:
            print(f"⚠️  No .env file found at {env_path}, nothing to remove")

    def deploy(self, working_dir: str = None) -> None:
        """Execute deployment process"""
        try:
            print(
                f"🚀 Starting deployment for project {self.config.project_id}")

            signature = self.get_or_create_signature()
            self.generate_env(f'{working_dir}/functions')

            self.firebase.configure_cleanup_policy()
            # Deploy function
            success, function_url = self.firebase.deploy_function(working_dir)
            if not success or not function_url:
                raise Exception("Failed to deploy function")

            if success:
                # Destroy old secrets
                self.firebase.destroy_old_secrets()

            # Setup Phrase webhook
            self.phrase.setup_webhook(function_url, signature)
            self.remove_env(f'{working_dir}/functions')
            print("✅ Deployment completed successfully!")

        except Exception as e:
            print(f"❌ Deployment failed: {str(e)}")
            raise


def main():
    parser = argparse.ArgumentParser(
        description="Deploy Firebase function and setup Phrase webhook",
        add_help=True,
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument("-p", "--projects", required=True, action='append', help="Project name to deploy, this will lookup the project configuration in the projects directory")
    parser.add_argument(
            "-f", "--firebase-path",
        default=os.path.abspath(os.path.join(os.path.dirname(__file__), "../firebase")),
        help="Path to the Firebase directory (default: %(default)s)"
        )
    parser.print_help()
    args = parser.parse_args()

    firebase_dir = args.firebase_path
    if not os.path.isdir(firebase_dir):
        raise FileNotFoundError(f"Firebase directory not found at {firebase_dir}")

    projects = args.projects
    if not projects:
        raise ValueError("At least one project name must be provided")
    for project in projects:
        if not project:
            raise ValueError("Project name cannot be empty")

        config = next((project for project in get_deployable_projects(filter = project)), None)
        if not config:
            raise ValueError(f"Project with name {args.project} not found")

        deployer = Deployer(config)
        deployer.deploy(firebase_dir)


if __name__ == "__main__":
    main()
