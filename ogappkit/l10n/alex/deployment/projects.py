import os
import json
import argparse
from typing import List
from project_config import ProjectConfig
from dataclasses import asdict

class ProjectEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ProjectConfig):
            return asdict(obj)
        return super().default(obj)

def get_deployable_projects(filter: str = None, exclude: List[str] = None) -> List[ProjectConfig]:
    """
    Scans the projects directory and returns a list of deployable projects.
    """
    projects = []
    projects_dir = os.path.join(os.path.dirname(
        os.path.dirname(__file__)), "projects")

    for file in os.listdir(projects_dir):

        if filter and filter not in os.path.splitext(file)[0]:
            continue

        if exclude and os.path.splitext(file)[0] in exclude:
            continue

        if file.endswith('.json'):
            project_path = os.path.join(projects_dir, file)
            projects.append(ProjectConfig.from_file(str(project_path)))

    return projects


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Retrieve all deployable projects",
        add_help=True,
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("-p", "--project",
                        help="Project name to look up, this will lookup the project configuration in the projects directory. If not provided, all deployable projects will be processed.")
    parser.add_argument("-e", "--exclude", action='append',
                        help="Project name to exclude from look up, this will lookup the project configuration in the projects directory. If not provided, all deployable projects will be processed.")
    args = parser.parse_args()

    print(json.dumps({"projects": get_deployable_projects(filter=args.project, exclude=args.exclude)}, cls=ProjectEncoder))
