import os
import requests
import sys
import time
from typing import Dict, List
from project_config import ProjectConfig
import re


class PhraseService:
    def __init__(self, config: ProjectConfig):
        self.config = config
        self.api_token = os.getenv('PHRASE_API_TOKEN')
        if not self.api_token:
            raise ValueError(
                "PHRASE_API_TOKEN environment variable is required")
        self.base_url = "https://api.phrase.com/v2"
        self.webhook_identifier = f"ogappkit - l10n - sync - {config.name}"
        self.headers = {
            'Authorization': f'token {self.api_token}',
            'Content-Type': 'application/json'
        }
        self.events = [
            "keys:create",
            "keys:update",
            "translations:update",
            "translations:verify",
            "translations:unverify",
            "translations:batch_verify",
            "translations:batch_unverify"
        ]

    def setup_webhook(self, function_url: str, signature: str) -> None:
        """Setup webhook with function URL and signature"""
        # Try to update existing webhook, create new one if none exists
        self._update_or_create_webhook(function_url, signature)

    def _handle_rate_limit(self, response):
        """Handle rate limiting by waiting until the reset time."""
        limit = response.headers.get("X-Rate-Limit-Limit")
        remaining = response.headers.get("X-Rate-Limit-Remaining")
        reset_time = response.headers.get("X-Rate-Limit-Reset")
        if reset_time is not None:
            try:
                reset_time = int(reset_time)
                wait_time = max(0, reset_time - int(time.time()))
                print("Rate limit reached.")
                print(f"Waiting for {wait_time} seconds... limit:{limit} remaining: {remaining}", end="", flush=True)

                while wait_time > 0:
                    sys.stdout.write(f"\rWaiting for {wait_time} seconds...")
                    sys.stdout.flush()
                    time.sleep(1)
                    wait_time = max(0, reset_time - int(time.time()))

                print("\nResuming execution.")
            except ValueError:
                print("Invalid X-Rate-Limit-Reset header value. Cannot handle rate limit.")
                raise
        else:
            print("Missing X-Rate-Limit-Reset header in 429 response. Cannot handle rate limit.")
            raise RuntimeError("Rate limit handling failed due to missing reset time.")

    def _make_request(self, method, url, max_retries=5, **kwargs):
        """Wrapper for making requests with rate limit handling."""
        retries = 0
        while retries < max_retries:
            response = requests.request(method, url, **kwargs)
            if response.status_code == 429:
                self._handle_rate_limit(response)
                retries += 1
            else:
                response.raise_for_status()
                return response
        raise RuntimeError(f"Max retries reached for {method} request to {url}. Exiting.")

    def _update_or_create_webhook(self, function_url: str, signature: str) -> None:
        """Update existing webhook or create new one if none exists"""
        url = f"{self.base_url}/projects/{self.config.phrase_project}/webhooks"
        response = self._make_request("GET", url, headers=self.headers)

        existing_webhook = None
        for webhook in response.json():
            if webhook.get('description', '').startswith(self.webhook_identifier):
                existing_webhook = webhook
                break

        if existing_webhook:
            # Update existing webhook
            webhook_id = existing_webhook['id']
            update_url = f"{url}/{webhook_id}"
            payload = {
                'callback_url': function_url,
                'events': self.events,
                'active': True,
                'secret': signature,
                'description': self.webhook_identifier
            }

            self._make_request("PATCH", update_url, headers=self.headers, json=payload)
            print(f"✅ Updated existing webhook: {existing_webhook['description']}")
        else:
            # Create new webhook
            payload = {
                'callback_url': function_url,
                'events': self.events,
                'active': True,
                'secret': signature,
                'description': self.webhook_identifier
            }

            self._make_request("POST", url, headers=self.headers, json=payload)

            print("✅ Created new webhook")

    def mark_deprecated(self, key: str) -> None:
        """Mark key as deprecated by adding a tag
        Args:
            key: The key name to mark as deprecated

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If key not found
        """
        # First, find the key ID
        url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"
        params = {
            'q': f'name:{key}',
            'per_page': 1
        }

        response = self._make_request("GET", url, headers=self.headers, params=params)

        keys = response.json()
        if not keys:
            raise ValueError(f"Key '{key}' not found")

        key_id = keys[0]['id']

        # Update key with deprecated tag
        update_url = f"{url}/{key_id}"
        payload = {
            "tags": "deprecated"  # This will add the tag while keeping existing ones
        }

        self._make_request("PATCH", update_url, headers=self.headers, json=payload)

        print(f"✅ Marked key '{key}' as deprecated with ID {key_id}")

    def rename(self, key: str, new_key: str) -> None:
        """Rename a translation key

        Args:
            key: The current key name
            new_key: The new key name

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If source key not found or target key already exists
        """
        url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"

        # Check if new key already exists
        check_params = {
            'q': f'name:{new_key}',
            'per_page': 1
        }
        check_response = self._make_request("GET", url, headers=self.headers, params=check_params)

        if check_response.json():
            # check if key is only capitalization fix, if so, ignore already existing target key error
            if key.lower() != new_key.lower():
                raise ValueError(f"Target key '{new_key}' already exists")

        # Find the source key ID
        params = {
            'q': f'name:{key}',
            'per_page': 1
        }
        response = self._make_request("GET", url, headers=self.headers, params=params)

        keys = response.json()
        if not keys:
            raise ValueError(f"Source key '{key}' not found")

        key_id = keys[0]['id']

        # Update key with new name
        update_url = f"{url}/{key_id}"
        payload = {
            "name": new_key,
        }

        self._make_request("PATCH", update_url, headers=self.headers, json=payload)

        print(f"✅ Renamed key from '{key}' to '{new_key}' with ID {key_id}")

    def create_key(self, key: str) -> str:
        """Create a new translation key

        Args:
            key: The name of the key to create

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If key already exists

        Returns:
            The ID of the created key
        """
        # normalize key (remove project suffix)
        key = key.replace(f".{self.config.name}", "")

        url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"

        # Check if key already exists
        check_params = {
            'q': f'name:{key}',
            'per_page': 1
        }
        check_response = self._make_request("GET", url, headers=self.headers, params=check_params)

        if check_response.json():
            existing_keys = check_response.json()
            # check if deprecated tag is set
            if ("deprecated" in existing_keys[0]['tags']):
                # if key is deprecated, remove the tag and return the id
                update_url = f"{url}/{existing_keys[0]['id']}"
                payload = {
                    # only remove the deprecated tag
                    "tags": [tag for tag in existing_keys[0]['tags'] if tag != "deprecated"]
                }
                self._make_request("PATCH", update_url, headers=self.headers, json=payload)
                print(f"✅ Removed deprecated tag from key '{key}' with ID {existing_keys[0]['id']}")
            return existing_keys[0]['id']

        else:
            # Create new key
            payload = {
                "name": key,
                "datatype": "string",
                "plural": False,
                "tags": []
            }

            response = self._make_request("POST", url, headers=self.headers, json=payload)

        print(f"✅ Created new key '{key}' with ID {response.json()['id']}")
        return response.json()['id']

    def delete_key(self, key: str) -> None:
        """Delete key

        Args:
            key: The key name to delete

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If key not found

        Returns
            The ID of the deleted key

        """
        # normalize key (remove project suffix)
        key = key.replace(f".{self.config.name}", "")

        url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"

        # Find the key ID
        params = {
            'q': f'name:{key}',
            'per_page': 1
        }

        response = self._make_request("GET", url, headers=self.headers, params=params)

        keys = response.json()
        if not keys:
            raise ValueError(f"Key '{key}' not found")

        key_id = keys[0]['id']

        # Delete key
        delete_url = f"{url}/{key_id}"
        self._make_request("DELETE", delete_url, headers=self.headers)

        print(f"✅ Deleted key '{key}' with ID {key_id}")
        return key_id

    def update_translation(self, key: str, locale: str, value: str) -> str:
        """Updates translation for a key in specified locale

        Args:
            key: The key name to translate
            locale: The locale code (e.g. 'en', 'de')
            value: The translation text

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If key not found or locale not found

        Returns:
            The ID of the created translation
        """
        # normalize key (remove project suffix)
        key = key.replace(f".{self.config.name}", "")

        # Find the key ID
        keys_url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"
        key_params = {
            'q': f'name:{key}',
            'per_page': 1
        }

        key_response = self._make_request("GET", keys_url, headers=self.headers, params=key_params)

        keys = key_response.json()
        if not keys:
            raise ValueError(f"Key '{key}' not found")

        key_id = keys[0]['id']

        # Find the locale ID
        locales_url = f"{self.base_url}/projects/{self.config.phrase_project}/locales"
        locale_response = self._make_request("GET", locales_url, headers=self.headers)

        locale_id = None
        for loc in locale_response.json():
            if loc['name'].lower() == locale.lower() or loc['code'].lower() == locale.lower():
                locale_id = loc['id']
                break

        if not locale_id:
            raise ValueError(f"Locale '{locale}' not found")

        # Find the translation ID
        translations_url = f"{self.base_url}/projects/{self.config.phrase_project}/keys/{key_id}/translations"
        translation_params = {
            'per_page': 25
        }
        translation_response = self._make_request("GET", translations_url, headers=self.headers, params=translation_params)
        translations = translation_response.json()
        # translations is array of translation that each has a locale with an id
        translation = next((t for t in translations if t['locale']['id'] == locale_id), None)
        if not translation:
            raise ValueError(f"Translation for key '{key}' in locale '{locale}' not found")
        translation_id = translation['id']
        # Update translation
        translations_url = f"{self.base_url}/projects/{self.config.phrase_project}/translations/{translation_id}"
        payload = {
            "content": value,
            "unverified": True
        }

        response = self._make_request("PATCH", translations_url, headers=self.headers, json=payload)

        print(f"✅ Updated translation '{translation_id}' for key '{key}' in locale '{locale}'")
        return response.json()['id']

    def create_translation(self, key: str, locale: str, value: str) -> str:
        """Create translation for a key in specified locale

        Args:
            key: The key name to translate
            locale: The locale code (e.g. 'en', 'de')
            value: The translation text

        Raises:
            requests.exceptions.HTTPError: If API request fails
            ValueError: If key not found or locale not found

        Returns:
            The ID of the created translation
        """
        # normalize key (remove project suffix)
        key = key.replace(f".{self.config.name}", "")

        # Find the key ID
        keys_url = f"{self.base_url}/projects/{self.config.phrase_project}/keys"
        key_params = {
            'q': f'name:{key}',
            'per_page': 1
        }

        key_response = self._make_request("GET", keys_url, headers=self.headers, params=key_params)

        keys = key_response.json()
        if not keys:
            raise ValueError(f"Key '{key}' not found")

        key_id = keys[0]['id']

        # Find the locale ID
        locales_url = f"{self.base_url}/projects/{self.config.phrase_project}/locales"
        locale_response = self._make_request("GET", locales_url, headers=self.headers)

        locale_id = None
        for loc in locale_response.json():
            if loc['name'].lower() == locale.lower() or loc['code'].lower() == locale.lower():
                locale_id = loc['id']
                break

        if not locale_id:
            raise ValueError(f"Locale '{locale}' not found")

        # Create translation
        translations_url = f"{self.base_url}/projects/{self.config.phrase_project}/translations"
        payload = {
            "locale_id": locale_id,
            "key_id": key_id,
            "content": value,
            "unverified": True
        }

        response = self._make_request("POST", translations_url, headers=self.headers, json=payload)

        print(f"✅ Created translation for key '{key}' in locale '{locale}'")
        return response.json()['id']

    def get_project_locales(self) -> List[Dict]:
        """Get list of locale codes configured for this project"""
        url = f"{self.base_url}/projects/{self.config.phrase_project}/locales"

        response = self._make_request("GET", url, headers=self.headers)

        return [{
            "name": locale['name'],
            "code": locale['code'],
            "id": locale['id']
        } for locale in response.json()]

    def create_job(self, name: str, keys: List[Dict]) -> None:
        """Create a new job with the given keys and translations

        Args:
            name: Name of the job
            keys: List of key objects with their translations
        """

        existing_job = self.find_job(name)

        if not existing_job:
            url = f"{self.base_url}/projects/{self.config.phrase_project}/jobs"
        else:
            url = f"{self.base_url}/projects/{self.config.phrase_project}/jobs/{existing_job['id']}/keys"

        payload = {
            "name": name,
            "translation_key_ids": [key['id'] for key in keys],
        }
        response = self._make_request("POST", url, headers=self.headers, json=payload)
        existing_job = response.json()

        existing_locale_ids = {locale['id'] for locale in existing_job['locales']}
        locale_ids = {translation['locale_id'] for key in keys for translation in key['translations']} - existing_locale_ids
        self._add_locale_to_job(existing_job['id'], locale_ids)

    def _get_job(self, id: str) -> Dict[str, str]:
        """Get job details by ID

        Args:
            id: ID of the job

        Returns:
            Dictionary with job details
        """
        url = f"{self.base_url}/projects/{self.config.phrase_project}/jobs/{id}"
        response = self._make_request("GET", url, headers=self.headers)
        return response.json()

    def find_job(self, name: str) -> Dict[str, str]:
        """Find a job by name

        Args:
            name: Name of the job

        Returns:
            Dictionary with job details
        """
        url = f"{self.base_url}/projects/{self.config.phrase_project}/jobs"

        params = {
            'per_page': 100,
            'page': 1,
        }

        # fetch all jobs until end reached
        while True:
            response = self._make_request("GET", url, headers=self.headers, params=params)
            jobs = response.json()

            # Check if job with given name exists
            for job in jobs:
                if job['name'] == name:
                    print(f"✅ Found job '{name}' with ID {job['id']}")
                    return self._get_job(job['id'])

            # Check if there are more pages
            if len(jobs) < params['per_page']:
                break

            params['page'] += 1
        print(f"❌ Job '{name}' not found")
        return None

    def _add_locale_to_job(self, jobId: str, locale_ids: List[str]) -> None:
        """ Add locales to job with given jobId and locales

        Args:
            jobId: Id of the job
            locales: List of locale ids to add
        """
        print(f"Adding locales: {locale_ids} to job '{jobId}'")
        for locale in locale_ids:
            url = f"{self.base_url}/projects/{self.config.phrase_project}/jobs/{jobId}/locales"

            payload = {
                "locale_id": locale
            }

            print(f"Adding locale: {locale} to job '{jobId}' with payload {payload} via url {url}")
            self._make_request("POST", url, headers=self.headers, json=payload)

        print(f"✅ Added locales: {locale_ids} to job '{jobId}'")

    def get_translations(self, locale_id: str, include_unverified: bool = False, include_untranslated: bool = False, include_deprecated=False) -> Dict[str, str]:
        """ Get translations for a locale

        Args:
            locale_id: The locale id to get translations for
            include_unverified: Include unverified translations
            include_untranslated: Include untranslated keys
            include_deprecated: Include deprecated/removed keys
        Returns:
            Dictionary of key to translation
        """
        url = f"{self.base_url}/projects/{self.config.phrase_project}/locales/{locale_id}/download"
        params = {
            'file_format': 'simple_json',
            'include_unverified_translations': include_unverified,
            'include_empty_translations': include_untranslated
        }
        response = self._make_request("GET", url, headers=self.headers, params=params)

        translations = response.json()
        # fetch deprecated keys and remove them from the response
        if not include_deprecated:
            params['tags'] = 'deprecated'
            exclude = self._make_request("GET", url, headers=self.headers, params=params)
            translations = {key: value for key, value in translations.items() if key not in exclude.json()}

        return translations
