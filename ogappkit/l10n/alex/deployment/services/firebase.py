import os
import subprocess
import json
from typing import Optional, <PERSON><PERSON>
from project_config import ProjectConfig

class FirebaseService:
    def __init__(self, config: ProjectConfig):
        self.config = config
        self._validate_environment()

    def _validate_environment(self) -> None:
        """Validate required environment variables"""
        if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
            raise ValueError("GOOGLE_APPLICATION_CREDENTIALS environment variable is required")

    def get_signature_secret(self) -> Optional[str]:
        """Get webhook signature from Firebase Secrets"""
        command = [
            'firebase',
            'functions:secrets:access',
            'PHRASE_SIGNATURE',
            '--project',
            self.config.project_id,
        ]
        print(f"Fetching signature from Firebase Secrets for project {self.config.project_id}")
        success, output = self._run_command(command)
        if not success:
            print(f"❌  Signature fetching failed with error:{output}")
            return None
        else:
            print(f"✅  Signature fetched successfully")
            return output.strip() if output else None


    def save_signature_secret(self, signature: str) -> <PERSON>ple[bool, Optional[str]]:
        """Save webhook signature to Firebase Secrets"""
        command = [
            'firebase',
            'functions:secrets:set',
            '--data-file',
            '-',
            'PHRASE_SIGNATURE',
            '--project',
            self.config.project_id,
            '--json',
            '--debug'
        ]
        print(f"Saving signature to Firebase Secrets for project {self.config.project_id}")
        success, output = self._run_command(command, input_data=f"{signature}")
        if not success:
            print(f"❌  Signature saving failed with error:{output}")
            return False, None
        else:
            print(f"✅  Signature saved successfully")
            return True, output

    def destroy_old_secrets(self, key: str = "PHRASE_SIGNATURE") -> Tuple[bool, Optional[str]]:
        """Destroy old secrets in Firebase"""
        command = [
            'firebase',
            'functions:secrets:describe',
            key,
            '--project',
            self.config.project_id,
            '--json',
            '--debug'
        ]
        print(f"Destroying old secrets for project {self.config.project_id}")
        success, output = self._run_command(command)
        if not success:
            print(f"❌  Failed to describe secrets with error:{output}")
            return False, None
        try:
            secrets = json.loads(output)
            phrase_secrets = [
                s for s in secrets.get("result", {}).get("secrets", [])
                if s.get("secret", {}).get("name") == key and s.get("state") == "ENABLED"
            ]
            # Sort by createTime descending
            phrase_secrets.sort(key=lambda s: s.get("createTime", ""), reverse=True)
            # Keep the most recent, delete the rest
            for old_secret in phrase_secrets[1:]:
                version_id = old_secret.get("versionId")
                if version_id:
                    delete_command = [
                        'firebase',
                        'functions:secrets:destroy',
                        f'{key}@{version_id}',
                        "--project",
                        self.config.project_id,
                        "--debug"
                    ]
                    success, output = self._run_command(delete_command)
                    if not success:
                        print(f"❌  Failed to destroy secret version {version_id}: {output}")
                    else:
                        print(f"✅  Destroyed old PHRASE_SIGNATURE version {version_id}")
            return True, None
        except json.JSONDecodeError:
            print(f"❌  Failed to parse secrets output: {output}")
            return False, None

    def configure_cleanup_policy(self) -> None:
        """Configure cleanup policy for Firebase Functions"""
        command = [
            'firebase',
            'functions:artifacts:setpolicy',
            '--location',
            'europe-west1',
            '--project',
            self.config.project_id,
            '--json',
            '--debug'
        ]
        print(f"Configuring cleanup policy for project {self.config.project_id}")
        success, output = self._run_command(command)
        if not success:
            print(f"❌  Cleanup policy configuration failed with error:{output}")
        else:
            print(f"✅  Cleanup policy configured successfully")

    def deploy_function(self, working_dir: str = None) -> Tuple[bool, Optional[str]]:
        """Deploy function and return success status and function URL"""
        command = [
            'firebase',
            'deploy',
            '--only',
            'functions,firestore',
            '--project',
            self.config.project_id,
            '--debug'
        ]
        print(f"Deploying function for project {self.config.project_id}")
        success, output = self._run_command(command, working_dir)
        if not success:
            print(f"❌  Function deployment failed with error:{output}")
            return False, None
        else:
            fun_success, fun_output = self._run_command(['firebase','functions:list','--project', self.config.project_id,'--json'], working_dir)
            fun_url = self._parse_function_url(fun_output, "on_update_translation")
            print(f"✅  Function deployed successfully {fun_url}")
            return True, fun_url

    def _run_command(self, command: list, working_dir: str = None, input_data: str = None) -> Tuple[bool, str]:
        """Run a command and return success status and output"""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                env={**os.environ},
                cwd=working_dir,
                input=input_data
            )
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, e.stderr

    def _parse_function_url(self, deployment_output: str, function_name: str) -> Optional[str]:
        """Parse function URL from deployment output"""
        try:
            data = json.loads(deployment_output)
            for fn in data.get("result", []):
                if fn.get("id") == function_name:
                    return fn.get("uri")
        except json.JSONDecodeError:
            pass
        return None
