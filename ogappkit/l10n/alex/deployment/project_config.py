from dataclasses import dataclass
import json
from pathlib import Path

@dataclass
class ProjectConfig:
    name: str
    project_id: str
    region: str
    phrase_project: str
    service_account_secret_name: str
    phrase_api_secret_name: str

    @classmethod
    def from_file(cls, config_path: str) -> 'ProjectConfig':
        """Load configuration from JSON file"""
        with open(config_path, 'r') as f:
            config = json.load(f)
            name = Path(config_path).stem
            return cls(
                name=name,
                project_id=config['projectId'],
                region=config['region'],
                phrase_project=config['phraseProject'],
                service_account_secret_name=config['serviceAccountSecretName'],
                phrase_api_secret_name=config['phraseApiSecretName']
            )
