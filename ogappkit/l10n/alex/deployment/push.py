# get called by parameter from command line
# parameter is epic or ticket number
# lookup changes file in changes dir
# read changes from file
# get all projects
# for each project create new key and translations in phrase

import json
from typing import List, Dict
import os
import argparse
from pathlib import Path
from dataclasses import dataclass
from services.phrase import PhraseService
from projects import get_deployable_projects
from project_config import ProjectConfig


@dataclass
class Change:
    operation: str  # '+', '-', '±', '~', '!'
    key: str
    new_key: str = None  # for rename operations
    value: str = None    # for value changes
    translations: Dict[str, str] = None  # translations for all locales


class ChangeProcessor:
    def __init__(self, phrase_service: PhraseService, project: ProjectConfig, other_projects: List[ProjectConfig]):
        self.phrase = phrase_service
        self.project = project
        self.other_projects = other_projects or []

    def parse_changes_file(self, file_path: str, translations_dir: str, filter_locale: str = None) -> List[Change]:
        """Parse a .changes file and return list of changes"""
        changes = []

        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Parse based on operation
                operation = line[0]
                parts = line[1:].strip().split()

                if operation == '+':
                    # For new keys, load all translations
                    translations = self._load_translations(
                        translations_dir, parts[0], filter_locale)
                    changes.append(Change(
                        operation='+',
                        key=parts[0],
                        translations=translations
                    ))
                elif operation == '-':
                    changes.append(Change(operation='-', key=parts[0]))
                elif operation == '!':
                    changes.append(Change(operation='!', key=parts[0]))
                elif operation == '±':
                    changes.append(
                        Change(operation='±', key=parts[0], new_key=parts[1]))
                elif operation == '~':
                    value = ' '.join(parts[1:]).strip('"')
                    # For changed values, load all translations
                    translations = self._load_translations(
                        translations_dir, parts[0], filter_locale)
                    changes.append(
                        Change(operation='~', key=parts[0], value=value, translations=translations))

        return changes

    def _all_translations_for_project(self, translations_dir: str, filter_locale: str = None) -> Dict[str, Dict[str, str]]:
        """Load all translations for the project from the directory

        Args:
            translations_dir: Directory containing translation files
            filter_locale: Locale to filter by (optional)

        Returns:
            Dictionary of locale codes to translation values
        """
        translations = {}
        # Iterate through all translation files
        for file in os.listdir(translations_dir):
            if not file.endswith('.json'):
                continue

            locale = file.split('.')[0]  # Extract locale from filename
            if filter_locale and locale != filter_locale:
                continue
            translations[locale] = {}
            file_path = os.path.join(translations_dir, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                    for key, value in content.items():
                        # exclude keys that end with any of other_project_ids
                        # or contains any of ".<other_project_ids>."
                        if any(key.endswith(f".{other_project.name}") or f".{other_project.name}." in key for other_project in self.other_projects):
                            continue

                        # Only allow override when the key contains the project name
                        # otherwise default would replace project override
                        if not (key.endswith(f".{self.project.name}") or f".{self.project.name}." in key):
                            if key in translations[locale]:
                                continue
                        # Replace ".<project_id>" from key
                        key = key.replace(f".{self.project.name}", "")

                        translations[locale][key] = value
            except Exception as e:
                print(f"❌ Error loading translations from {file}: {str(e)}")
        return translations

    def all_translations_as_changes(self, translations_dir: str, filter_locale: str = None) -> List[Change]:
        """Load all translations from the directory and return as changes
            This can be used to push all translations to Phrase

        Args:
            translations_dir: Directory containing translation files
            filter_locale: Locale to filter by (optional)

        Returns:
            List of Change objects representing all translations
        """
        changes = {}

        # Iterate through all translation files
        translations = self._all_translations_for_project(translations_dir, filter_locale)

        for locale, content in translations.items():
            for key, value in content.items():
                if key not in changes:
                    changes[key] = Change(
                        operation='~',
                        key=key,
                        translations={locale: value}
                    )
                else:
                    changes[key].translations[locale] = value

        return list(changes.values())

    def _load_translations(self, translations_dir: str, key: str, filter_locale: str = None) -> Dict[str, str]:
        """Load translations for a key from all locale files

         Args:
             translations_dir: Directory containing translation files
             key: The key to look up

         Returns:
             Dictionary of locale codes to translation values
         """
        translations = {}

        # Get all translation files
        for file in os.listdir(translations_dir):
            if not file.endswith('.json'):
                continue

            locale = file.split('.')[0]  # e.g., 'en' from 'en.json'
            if filter_locale and locale != filter_locale:
                continue
            file_path = os.path.join(translations_dir, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                if key in content:
                    translations[locale] = content[key]
            except Exception as e:
                print(f"❌ Error loading translations from {file}: {str(e)}")

        return translations

    def process_changes(self, changes: List[Change], job_name: str) -> str:
        """Process all changes using the Phrase service"""
        project_locales = self.phrase.get_project_locales()
        job_translations = []
        for change in changes:
            try:
                if change.operation == '+':
                    # Create the key first
                    key_id = self.phrase.create_key(change.key)

                    # Add translations for project-specific locales
                    if change.translations:
                        change_translations = []
                        for locale, value in change.translations.items():
                            project_locale = next((proj_locale for proj_locale in project_locales if proj_locale['code'] == locale or proj_locale['name'] == locale), None)
                            if project_locale:
                                translation_id = self.phrase.create_translation(change.key, locale, value)
                                change_translations.append({
                                    "id": translation_id,
                                    "locale": locale,
                                    "locale_id": project_locale['id'],
                                    "content": value
                                })
                            else:
                                print(f"⚠️ Skipping translation for locale {locale} - not configured in project")

                        job_translations.append({
                            "key": change.key,
                            "id": key_id,
                            "translations": change_translations
                        })

                elif change.operation == '!':
                    self.phrase.delete_key(change.key)
                elif change.operation == '-':
                    self.phrase.mark_deprecated(change.key)
                elif change.operation == '±':
                    self.phrase.rename(change.key, change.new_key)
                elif change.operation == '~':
                    # Add translations for project-specific locales
                    if change.translations:
                        change_translations = []
                        for locale, value in change.translations.items():
                            project_locale = next((proj_locale for proj_locale in project_locales if proj_locale['code'] == locale or proj_locale['name'] == locale), None)
                            if project_locale:
                                print(f"Updating translation for {change.key} in {locale} + {value}")
                                translation_id = self.phrase.update_translation(change.key, locale, value)
                                change_translations.append({
                                    "id": translation_id,
                                    "locale": locale,
                                    "locale_id": project_locale['id'],
                                    "content": value
                                })
                            else:
                                print(f"⚠️ Skipping translation for locale {locale} - not configured in project")
                    #
                    # # Assuming we want to update in the default locale
                    # self.phrase.create_translation(
                    #     change.key, 'en-US', change.value)
                print(f"✅ Processed change: {change}")
            except Exception as e:
                print(f"❌ Error processing change {change}: {str(e)}")

                # Create job with all processed changes
        if job_translations:
            self.phrase.create_job(job_name, job_translations)


def main():
    parser = argparse.ArgumentParser(
        description="Push changes to Phrase for all deployable projects",
        add_help=True,
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    def dir_path(path_str):
        path = Path(path_str)
        if not path.exists():
            parser.error(f"Directory does not exist: {path}")
        if not path.is_dir():
            parser.error(f"Not a directory: {path}")
        return path

    def file_path(path_str):
        path = Path(path_str)
        if not path.exists():
            parser.error(f"File does not exist: {path}")
        if path.is_dir():
            parser.error(f"File is a directory: {path}")
        return path

    parser.add_argument("-t", "--translations", type=dir_path, required=True, help="Directory containing translation files")
    parser.add_argument("-c", "--changes", type=file_path, required=True, help="Changes File to process")
    parser.add_argument("-a", "--all", action='store_true', help="Push all translations in the directory")
    parser.add_argument("-l", "--locale", help="Only process translations for this locale")
    parser.add_argument("-p", "--project", help="Project name to deploy, this will lookup the project configuration in the projects directory. If not provided, all deployable projects will be processed.")
    parser.print_help()
    args = parser.parse_args()
    changes_file = args.changes
    translations_dir = args.translations
    filter_locale = args.locale
    update_all = args.all

    job_name = os.path.splitext(os.path.basename(changes_file))[0]

    # Get deployable projects
    projects = get_deployable_projects(filter=args.project)

    for project in projects:
        # Initialize services for each project
        phrase_service = PhraseService(project)
        processor = ChangeProcessor(phrase_service, project, other_projects=get_deployable_projects(exclude=[project.name]))

        # Process changes
        if update_all:
            changes = processor.all_translations_as_changes(translations_dir, filter_locale)
        else:
            changes = processor.parse_changes_file(changes_file, translations_dir, filter_locale)

        print(f"Processing {len(changes)} changes for project {project}...")
        processor.process_changes(changes, job_name)

    print(f"✅ Processed changes file {changes_file}")

    # Rename changes file after successful processing
    # handled_file = changes_file + ".handled"
    # os.rename(changes_file, handled_file)
    # print(f"✅ Renamed changes file to {handled_file}")


if __name__ == "__main__":
    main()
