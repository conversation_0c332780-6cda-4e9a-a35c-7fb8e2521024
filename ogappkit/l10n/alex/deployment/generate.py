import json
import argparse
from pathlib import Path
from typing import Dict, List
from projects import get_deployable_projects

class InfoPlistGenerator:
    def __init__(self, translation_files_dir: str = "./../data/translations", output_files_dir: str = "./../data/infoPlist", project_name: str = None):
        self.output_dir = Path(output_files_dir)
        self.translations_dir = Path(translation_files_dir)
        self.project_name = project_name

    def generate(self) -> None:
        """Generate InfoPlist.strings files for each project for each locale"""
        print(f"🔨 Generating InfoPlist.strings files...\n{self.output_dir.resolve()}")
        projects =get_deployable_projects(filter= self.project_name)
        for project in projects:
            print(f"🔨 Generating InfoPlist.strings files for project {project.name}")
            project_strings = self._get_ns_strings_for_project(project.name,[p.name for p in projects if p.name != project.name])
            for locale, strings in project_strings.items():
                output_dir = self.output_dir / f"{project.name}/{locale}.lproj"
                output_dir.mkdir(parents=True, exist_ok=True)
                with open(output_dir / "InfoPlist.strings", 'w') as output_file:
                    for key, value in strings.items():
                        output_file.write(f'"{key}" = "{value}";\n')

    def _get_ns_strings_for_project(self, project_id: str, other_project_ids: List[str]) -> Dict[str, Dict[str, str]]:
        """Get NS strings for a project, excluding keys from other projects"""
        result = {}
        for file in self.translations_dir.glob("*.json"):
            locale = file.stem  # Use filename as locale
            with open(file, 'r') as f:
                translations = json.load(f)
            ns_translations = {k: v for k, v in translations.items() if
                               k.startswith('NS') and not any(
                                   k.endswith(f".{id}") for id in other_project_ids)}
            ns_merged_translations = {}
            for key, value in ns_translations.items():
                if key.endswith(f".{project_id}"):
                    base_key = key.rsplit('.', 1)[0]
                    ns_merged_translations[base_key] = value
                elif f"{key}.{project_id}" not in ns_translations:
                    ns_merged_translations[key] = value

            result[locale] = ns_merged_translations
        return result

    def get_ns_strings_for_project(self, project_id: str)-> Dict[str, Dict[str, str]]:
        """Get NS strings for a project"""
        excluded_project_ids = [project.name for project in get_deployable_projects(exclude= [project_id])]
        return self._get_ns_strings_for_project(project_id, excluded_project_ids)

def main():
    parser = argparse.ArgumentParser(
        description="Merge translations from base files and Firebase Firestore or Phrase",
        add_help=True,
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    def dir_path(path_str):
        path = Path(path_str)
        if not path.exists():
            parser.error(f"Directory does not exist: {path}")
        if not path.is_dir():
            parser.error(f"Not a directory: {path}")
        return path

    parser.add_argument("-t", "--translations",type=dir_path, required=True, help="Directory containing base translation files")
    parser.add_argument("-o","--output", type=dir_path, required=True, help="Directory to save generated infoPlist.strings files")
    parser.add_argument("-p","--project", help="Project name to generate InfoPlist.strings files for. If not provided, all deployable projects will be processed.")
    parser.print_help()
    args = parser.parse_args()

    # Create output directory if it doesn't exist
    args.output.mkdir(parents=True, exist_ok=True)

    generator = InfoPlistGenerator(args.translations, args.output, args.project)
    generator.generate()

    print("✅ InfoPlist.strings files generated successfully!")


if __name__ == "__main__":
    main()
