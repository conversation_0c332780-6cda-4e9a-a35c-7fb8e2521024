# get base translations
# iterate through all projects,
# fetch all firebase firestore translations
# merge all translations.
# if there are duplicate keys with different values, add suffix to the key
# save the merged translations

import os
import argparse
import json
from firebase_admin import App, initialize_app, delete_app
from firebase_admin import credentials, firestore
from typing import Dict, List
from pathlib import Path
from dataclasses import dataclass

from services.phrase import PhraseService
from project_config import ProjectConfig
from projects import get_deployable_projects


@dataclass
class Translation:
    key: str
    value: str


class TranslationMerger:
    def __init__(self, base_files_dir, output_files_dir, use_phrase=False, project_name: str = None):
        self.data_dir = base_files_dir
        self.output_dir = output_files_dir
        self.use_phrase = use_phrase
        self.project_name = project_name
        self.platform_keys = ["android", "ios"]
        # locale -> key -> text
        self.base_translations: Dict[str, Dict[str, str]] = {}
        self.firebase_apps: Dict[str, App] = {}
        # locale -> key -> MergedTranslation
        self.merged_translations: Dict[str, Dict[str, str]] = {}

    def _load_base_translations(self) -> None:
        """Load base translations from data directory for each locale"""
        print("📚 Loading base translations...")

        # Each JSON file in data dir represents a locale
        for file in self.data_dir.glob("*.json"):
            locale = file.stem  # Use filename as locale
            with open(file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
                self.base_translations[locale] = translations
                # Initialize merged translations dict for this locale
                self.merged_translations[locale] = translations

        print(
            f"✅ Loaded base translations for {len(self.base_translations)} locales")

    def _initialize_firebase_app(self, project_config: ProjectConfig) -> App:
        """Initialize Firebase app for a project"""
        service_account_json = os.getenv(project_config.service_account_secret_name)
        if not service_account_json:
            raise ValueError(
                f"{project_config.service_account_secret_name} environment variable is required"
            )

        cred_dict = json.loads(service_account_json)
        cred = credentials.Certificate(cred_dict)
        app = initialize_app(cred, {
            'projectId': project_config.project_id
        }, name=project_config.project_id)
        return app

    def _get_phrase_translation(self, project_config: ProjectConfig, include_unverified=False, include_deprecated=False) -> Dict[str, List[Translation]]:
        phrase = PhraseService(project_config)
        verified_translations: Dict[str, List[Translation]] = {}

        for locale in phrase.get_project_locales():
            translations = [Translation(key, value) for key, value in phrase.get_translations(locale["id"], include_unverified=include_unverified, include_deprecated=include_deprecated).items()]
            verified_translations[locale["name"]] = translations

        return verified_translations

    def _get_firebase_translations(self, project_config: ProjectConfig, include_deprecated=False) -> Dict[str, List[Translation]]:
        """Get translations from Firebase for a project"""
        try:
            app = self._initialize_firebase_app(project_config)
            self.firebase_apps[project_config.project_id] = app

            db = firestore.client(app=app)

            # Fetch all verified translations
            verified_translations: Dict[str, List[Translation]] = {}
            all_docs = db.collection('translations').stream()

            for doc in all_docs:
                data = doc.to_dict()

                if not include_deprecated and data.get('deprecated', False):
                    continue

                translations = data.get('translations', {})

                # Group by locale
                for locale, entry in translations.items():
                    if not entry.get('verified', False):
                        continue

                    if locale not in verified_translations:
                        verified_translations[locale] = []

                    translation = Translation(
                        key=doc.id,
                        value=entry.get('text', '')
                    )
                    verified_translations[locale].append(translation)

            print(
                f"📥 Found translations for {len(verified_translations)} locales in {project_config.project_id}")
            return verified_translations

        except Exception as e:
            print(
                f"⚠️  Error fetching translations for {project_config.project_id}: {e}")
            return {}

    def _merge_translations(self, firebase_translations: Dict[str, List[Translation]],
                            project_id: str) -> None:
        """Merge Firebase translations with base translations for each locale"""
        for locale, translations in firebase_translations.items():
            if locale not in self.merged_translations:
                self.merged_translations[locale] = self.base_translations.get(locale, {})

            base_translations = self.base_translations.get(locale, {})

            for trans in translations:
                key = trans.key
                value = trans.value

                # Check if key ends with project_id and/or platform
                parts = key.split('.')

                # Handle different key formats
                if len(parts) >= 2:
                    # Check if the key ends with platform
                    if parts[-1] in self.platform_keys:
                        platform = parts[-1]

                        # Check if the key also has project_id before platform
                        if len(parts) >= 3 and parts[-2] == project_id:
                            # Case: <key>.<part>.<project_id>.<platform>
                            self.merged_translations[locale][key] = value
                        else:
                            # Case: <key>.<part>.<platform>
                            new_key_parts = parts[:-1] + [project_id, platform]
                            new_key = '.'.join(new_key_parts)
                            self.merged_translations[locale][new_key] = value

                    # Check if the key ends with project_id
                    elif parts[-1] == project_id:
                        # Case: <key>.<part>.<project_id>
                        self.merged_translations[locale][key] = value

                    else:
                        # Case: <key>.<part>
                        base_key = key

                        # Check if base key exists and has different value
                        if base_key in base_translations and base_translations[base_key] != value:
                            # Create new key with project_id
                            new_key = f"{key}.{project_id}"
                            self.merged_translations[locale][new_key] = value
                        else:
                            # Use the key as is
                            self.merged_translations[locale][key] = value
                else:
                    # Case: <key>
                    base_key = key
                    # Check if base key exists and has different value
                    if base_key in base_translations and base_translations[base_key] != value:
                        # Create new key with project_id
                        new_key = f"{key}.{project_id}"
                        self.merged_translations[locale][new_key] = value
                    else:
                        # Use the key as is
                        self.merged_translations[locale][key] = value

    def merge(self) -> None:
        """Execute the merge process"""
        try:
            # Load base translations for all locales
            self._load_base_translations()

            # Process each project
            for project_config in get_deployable_projects(filter=self.project_name):
                print(f"\n🔄 Processing project {project_config.name}...")
                if self.use_phrase:
                    # Get translations from phrase
                    remote_translations = self._get_phrase_translation(project_config, include_unverified=False)
                else:
                    # Get translations from Firebase
                    remote_translations = self._get_firebase_translations(project_config)

                total_translations = sum(len(trans) for trans in remote_translations.values())
                print(f"📥 Found {total_translations} verified translations in {len(remote_translations)} locales")

                # Merge translations
                self._merge_translations(
                    remote_translations, project_config.name)

            # Save merged translations
            self._save_merged_translations()

        finally:
            # Cleanup Firebase apps
            for app in self.firebase_apps.values():
                delete_app(app)

    def _save_merged_translations(self) -> None:
        """Save merged translations to separate files per locale"""
        self.output_dir.mkdir(exist_ok=True)

        for locale, translations in self.merged_translations.items():
            output_file = self.output_dir / f"{locale}.json"

            # Convert to serializable format
            merged_data = {key: value for key, value in sorted(translations.items())}

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Saved merged translations for {locale} to {output_file}")

        print(f"\n📊 Summary:")
        for locale in self.merged_translations:
            base_count = len(self.base_translations.get(locale, {}))
            merged_count = len(self.merged_translations[locale])
            print(f"   - {locale}:")
            print(f"     Base translations: {base_count}")
            print(f"     Merged translations: {merged_count}")


def main():
    parser = argparse.ArgumentParser(
        description="Merge translations from base files and Firebase Firestore or Phrase",
        add_help=True,
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    def dir_path(path_str):
        path = Path(path_str)
        if not path.exists():
            parser.error(f"Directory does not exist: {path}")
        if not path.is_dir():
            parser.error(f"Not a directory: {path}")
        return path

    parser.add_argument("-t", "--translations", type=dir_path, required=True, help="Directory containing base translation files")
    parser.add_argument("-o", "--output", type=Path, help="Directory to save merged translation files. This will default to the input directory if not provided")
    parser.add_argument("-p", "--project", help="Project name to deploy, this will lookup the project configuration in the projects directory. If not provided, all deployable projects will be processed.")
    parser.add_argument("--phrase", action='store_true', help="Use Phrase service instead of Firebase to fetch all translations")
    parser.print_help()
    args = parser.parse_args()

    # Set output to translations if not provided
    if not args.output:
        args.output = args.translations
    else:
        # Create output directory if it doesn't exist
        args.output.mkdir(parents=True, exist_ok=True)

    base_files_dir = args.translations
    output_files_dir = args.output
    use_phrase = args.phrase
    project_name = args.project

    merger = TranslationMerger(base_files_dir, output_files_dir, use_phrase, project_name)
    merger.merge()


if __name__ == "__main__":
    main()
