# Translation File Specification

A translation file is a JSON file. The file name is the ISO Language Code.

## Format Description
The translation file contains a dictionary of key-value pairs. Each entry consists of:
1. **Key**: A string representing the localization key.
2. **Value**: A string representing the localized value.

# Example:
```
{
"bfg.cupSize.loose.description": "If the cup edge of your bra sticks out or the material wrinkles, they are too big.",
"bfg.cupSize.loose.title": "The cups are too big and the cup edge stands off",
}
```

### Placeholders
Placeholders are represented by a percentage symbol and curly braces `%{}`. They can be used to insert dynamic values into the localized string.
The placeholder name should be a descriptive word.

```
{
"bfg.cupSize.loose.description": "Hello %{world}",
}
```
