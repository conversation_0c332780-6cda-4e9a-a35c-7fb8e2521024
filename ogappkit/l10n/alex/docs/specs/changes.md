# Localization Change Log Specification

## Format Description
The **changes** file contains a chronological record of changes made to localization keys and values. Each entry consists of:

1. **Timestamp Line**
    - Begins with `#`
    - Contains date and time in format `YYYY-MM-DD HH:MM:SS`

2. **Action Line**
    - Specifies the operation performed on a localization key
    - Contains prefix, key, and optionally a value
    - Text values are wrapped inside `"`

## Action Prefixes
- `+`: Addition of a new key-value pair
- `-`: Deletion of an existing key
- `~`: Modification of a key's value
- `±`: Renaming of a key

## Entry Example
```
# 2025-04-01 09:05:44
+ test.key "Help"
# 2025-04-01 09:05:52
- test.key
# 2025-04-01 09:26:24
~ Test.Key "Great product"
# 2025-04-01 14:40:40
± AppBuild dfsd
```
