from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional

@dataclass
class TranslationEntry:
    text: str
    verified: bool = False
    verified_at: Optional[datetime] = None
    verified_by: Optional[str] = None

    def to_dict(self) -> dict:
        return {
            "text": self.text,
            "verified": self.verified,
            "verified_at": self.verified_at,
            "verified_by": self.verified_by
        }

    @staticmethod
    def from_dict(data: dict) -> 'TranslationEntry':
        return TranslationEntry(
            text=data["text"],
            verified=data.get("verified", False),
            verified_at=data.get("verified_at"),
            verified_by=data.get("verified_by")
        )

@dataclass
class Translation:
    key: str
    translations: Dict[str, TranslationEntry]
    deprecated: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def to_dict(self) -> dict:
        data = {
            "key": self.key,
            "translations": {k: v.to_dict() for k, v in self.translations.items()},
            "deprecated": self.deprecated
        }
        if self.created_at is not None:
            data["created_at"] = self.created_at
        if self.updated_at is not None:
            data["updated_at"] = self.updated_at
        return data

    @staticmethod
    def from_dict(data: dict) -> 'Translation':
        translations = {
            k: TranslationEntry.from_dict(v)
            for k, v in data["translations"].items()
        }
        return Translation(
            key=data["key"],
            translations=translations,
            deprecated=data.get("deprecated", False),
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at")
        )
