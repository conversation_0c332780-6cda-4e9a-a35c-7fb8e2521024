from typing import Optional
from firebase_admin import firestore
from Translation import Translation, TranslationEntry


class TranslationService:
    def __init__(self, db):
        self.db = db
        self.collection = db.collection("translations")

    def create_translation(self, id: str, key: str, text: str = None) -> None:
        doc_ref = self.collection.document(id)
        translation = Translation(key=key, translations={}, created_at=firestore.SERVER_TIMESTAMP, updated_at=firestore.SERVER_TIMESTAMP)
        translation_dict = translation.to_dict()

        doc_ref.set(translation_dict, merge=True)

    def update_key(self, id: str, new_key: str, deprecated: bool) -> None:
        doc_ref = self.collection.document(id)
        doc_ref.update({
            "key": new_key,
            "deprecated": deprecated,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    def update_translation(self, id: str, key: str, locale: str, text: str, verified: bool, user_id: Optional[str] = None) -> None:
        doc_ref = self.collection.document(id)
        entry = TranslationEntry(text=text, verified=verified)
        translation = Translation(key=key, translations={locale: entry}, updated_at=firestore.SERVER_TIMESTAMP)
        doc_ref.set(translation.to_dict(), merge=True)

    def mark_deprecated(self, id: str, key: str, deprecated=True) -> None:
        doc_ref = self.collection.document(id)
        doc_ref.update({
            "deprecated": deprecated,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    def get_translation(self, id: str) -> Optional[Translation]:
        doc_ref = self.collection.document(id)
        doc = doc_ref.get()
        if not doc.exists:
            return None
        data = doc.to_dict()
        return Translation.from_dict(data)

    def create_copy(self, id: str, copy_id: str):
        doc_ref = self.collection.document(id)
        copy_ref = self.collection.document(copy_id)
        doc = doc_ref.get()
        if doc.exists:
            data = doc.to_dict()
            # Create a new translation object with the same data
            translation = Translation.from_dict(data)
            # Set the new ID for the copy
            translation.id = copy_id
            # Save the copy to Firestore
            copy_ref.set(translation.to_dict(), merge=True)

    def verify_translation(self, id: str, key: str, locale: str, user_id: str) -> None:
        doc_ref = self.collection.document(id)
        if not doc_ref.get().exists:
            # If the document does not exist, we cannot verify it
            # thats fine, as this indicates that the translation was never created
            # or has never been updated
            # or has been deleted
            # so we simply return
            return
        doc_ref.update({
            f"translations.{locale}.verified": True,
            f"translations.{locale}.verified_at": firestore.SERVER_TIMESTAMP,
            f"translations.{locale}.verified_by": user_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    def get_unverified_translations(self, locale: str):
        docs = self.collection.stream()
        return [
            Translation.from_dict(doc.to_dict())
            for doc in docs
            if locale in doc.get("translations", {})
            and not doc.get("translations")[locale].get("verified", False)
        ]

    def unverify_translation(self, id: str, key: str, locale: str, user_id: str) -> None:
        doc_ref = self.collection.document(id)
        if not doc_ref.get().exists:
            # If the document does not exist, we cannot unverify it
            # thats fine, as this indicates that the translation was never created
            # or has never been updated
            # or has been deleted
            # so we simply return
            return
        doc_ref.update({
            f"translations.{locale}.verified": False,
            f"translations.{locale}.verified_at": None,
            f"translations.{locale}.verified_by": user_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    def get_translation_status(self, id: str, key: str, locale: str) -> Optional[TranslationEntry]:
        doc = self.collection.document(id).get()
        if not doc.exists:
            return None
        data = doc.to_dict()
        return TranslationEntry.from_dict(data["translations"].get(locale, {}))

    def get_verified_translations(self, locale: str):
        """Get all verified translations for a specific locale"""
        try:
            docs = self.collection.stream()
            verified = []

            for doc in docs:
                data = doc.to_dict()
                translations = data.get("translations", {})

                if (locale in translations and
                        translations[locale].get("verified", False)):
                    verified.append(Translation.from_dict(data))

            return verified
        except Exception as e:
            print(f"Error getting verified translations: {e}")
            return []

    def get_all_verified_translations(self):
        """Get all verified translations grouped by locale"""
        docs = self.collection.stream()
        verified_translations = {}

        for doc in docs:
            data = doc.to_dict()
            translations = data.get("translations", {})

            for locale, translation in translations.items():
                if translation.get("verified", False):
                    if locale not in verified_translations:
                        verified_translations[locale] = []
                    verified_translations[locale].append(
                        Translation.from_dict(data)
                    )

        return {
            "translations": verified_translations,
            "total_count": sum(len(trans) for trans in verified_translations.values())
        }
