# Welcome to Cloud Functions for Firebase for Python!
# Deploy with `firebase deploy`

from firebase_functions import https_fn
from firebase_functions.params import SecretParam, StringParam
from firebase_admin import initialize_app
from firebase_admin import firestore
from TranslationService import TranslationService
from typing import Union, Optional, List
import json
import hmac
import hashlib
import base64

app = initialize_app()

PHRASE_SIGNATURE = SecretParam('PHRASE_SIGNATURE')
APP_ID = StringParam('APP_ID')
PLATFORMS_JSON = StringParam('APP_PLATFORMS', default='["android","ios","web"]')

@https_fn.on_request(secrets=[PHRASE_SIGNATURE], region='europe-west1')
def on_update_translation(req: https_fn.Request) -> https_fn.Response:
    # Initialize Firebase app and Firestore client
    db = firestore.client()
    translation_service = TranslationService(db)
    # Only allow POST requests
    if req.method != 'POST':
        return https_fn.Response('Method not allowed', status=405)
    try:
        # Get raw body for signature verification
        raw_body = req.data.decode('utf-8')
        signature = req.headers.get('X-PhraseApp-Signature')

        webhook_secret = PHRASE_SIGNATURE.value
    # Verify webhook signature
        if not verify_phrase_signature(raw_body, signature, webhook_secret):
            return https_fn.Response('Invalid signature', status=401)
        # Parse webhook data
        data = json.loads(raw_body)
        event_name = data.get('event')

        if not event_name:
            return https_fn.Response('Invalid payload', status=400)

        result = {
            'processed': 0,
            'errors': []
        }

        # Handle different events
        if event_name == 'keys:create':
            current_key = format_key(data['key']['name'])
            try:
                translation_service.create_translation(
                    id=data['key']['id'],
                    key=current_key
                )
                result['processed'] += 1
            except Exception as e:
                result['errors'].append(f"Error processing {key}: {str(e)}")

        elif event_name == 'keys:update':
            is_deprecated = 'deprecated' in data['key']['tags']
            current_id = data['key']['id']
            current_key = format_key(data['key']['name'])
            try:
                if is_deprecated:
                    translation_service.mark_deprecated(
                        id=current_id,
                        key=current_key,
                        deprecated=True
                    )
                else:
                    # check if key was renamed
                    # if so, we need to create a compatibility deprecated item for the old key
                    # in order to keep this backward compatible
                    translation = translation_service.get_translation(id=current_id)
                    if translation and translation.key != current_key:
                        compat_id = f"{current_id}_{translation.key}"
                        translation_service.create_copy(id=current_id, copy_id=compat_id)
                        translation_service.mark_deprecated(
                            id=compat_id,
                            key=translation.key,
                            deprecated=True
                        )

                    translation_service.update_key(id=current_id, new_key=current_key, deprecated=is_deprecated)

                result['processed'] += 1
            except Exception as e:
                result['errors'].append(f"Error processing {current_key}: {str(e)}")

        elif event_name == 'translations:update':
            current_key = format_key(data['translation']['key']['name'])
            try:
                translation_service.update_translation(
                    id=data['translation']['key']['id'],
                    key=current_key,
                    locale=data['translation']['locale']['code'],
                    text=data['translation']['content'],
                    verified=not data['translation']['unverified'],
                )
                result['processed'] += 1
            except Exception as e:
                result['errors'].append(f"Error processing {key}: {str(e)}")

        elif event_name == 'translations:verify':
            current_key = format_key(data['translation']['key']['name'])
            try:
                translation_service.verify_translation(
                    id=data['translation']['key']['id'],
                    key=current_key,
                    locale=data['translation']['locale']['code'],
                    user_id=data['user']['username']
                )
                result['processed'] += 1
            except Exception as e:
                result['errors'].append(f"Error verifying {key}: {str(e)}")

        elif event_name == 'translations:unverify':
            current_key = format_key(data['translation']['key']['name'])
            try:
                translation_service.unverify_translation(
                    id=data['translation']['key']['id'],
                    key=current_key,
                    locale=data['translation']['locale']['code'],
                    user_id=data['user']['username']
                )
                result['processed'] += 1
            except Exception as e:
                result['errors'].append(f"Error verifying {key}: {str(e)}")

        elif event_name == 'translations:batch_verify':
            for translation in data['translations']:
                current_key = format_key(translation['key']['name'])
                try:
                    translation_service.verify_translation(
                        id=data['key']['id'],
                        key=current_key,
                        locale=translation['locale']['code'],
                        user_id=data['user']['username']
                    )
                    result['processed'] += 1
                except Exception as e:
                    result['errors'].append(f"Error batch verifying {key}/{translation['locale']['code']}: {str(e)}")

        elif event_name == 'translations:batch_unverify':
            for translation in data['translations']:
                current_key = format_key(translation['key']['name'])
                try:
                    translation_service.unverify_translation(
                        id=data['key']['id'],
                        key=current_key,
                        locale=translation['locale']['code'],
                        user_id=data['user']['username']
                    )
                    result['processed'] += 1
                except Exception as e:
                    result['errors'].append(f"Error batch unverifying {key}/{translation['locale']['code']}: {str(e)}")

        else:
            return https_fn.Response('Unknown event', status=400)

        return https_fn.Response(
            response=json.dumps(result),
            status=200,
            content_type='application/json'

        )
    except Exception as e:
        return https_fn.Response(f'Error: {str(e)}', status=500)

def format_key(name: str) -> str:
    """
    Convenience function to normalize the key name by applying the application ID
    and platform suffixes. This function uses the global application ID and
    platform suffixes defined in the environment variables.

    Args:
        name (str): The original key name to format.
    Returns:
        str: The formatted key name with the application ID and platform suffixes applied.
    """
    app_id = APP_ID.value
    platforms = json.loads(PLATFORMS_JSON.value)
    return _normalize_name(name, app_id, platforms)

def _normalize_name(name: str, app_id: str, platforms: List[str]) -> str:
    """
    Format the key name by normalizing it with the application ID and platforms.
    This function ensures that the key name is consistent across different platforms
    and includes the application identifier if necessary.

    Args:
        name (str): The original name of the key
        app_id (str): The application identifier to append
        platforms (List[str]): List of platform suffixes to check against
    Returns:
        str: The normalized name with the app_id appended if not already present
    """
    if not app_id:
        return name

    # if name ends with .android or .ios check for app_id before it.
    # then add app_id before it if not present
    if any(name.endswith(suffix) for suffix in platforms):
        parts = name.rsplit('.', 1)
        if len(parts) == 2 and not parts[0].endswith(f".{app_id}"):
            return f"{parts[0]}.{app_id}.{parts[1]}"
        else:
            return name
    elif name.endswith(f".{app_id}"):
        return name
    else:
        return f"{name}.{app_id}"

def verify_phrase_signature(request_body: Union[str, bytes], signature_header: str, verification_token: str) -> bool:
    """
        Verify the Phrase webhook signature.

        Args:
            verification_token (str): The webhook verification token from Phrase
            request_body (Union[str, bytes]): The raw request body
            signature_header (str): The value from X-PhraseApp-Signature header

        Returns:
            bool: True if signature is valid, False otherwise

        Example:
            >>> verification_token = "aaaaa"
            >>> request_body = '{"event":"test:event","message":"This is a test notification generated by Phrase"}'
            >>> signature_header = "GyyfKzqpkXpX5fBukeOuIjTrzeS2a3Xz3f/DlAQshP8="
            >>> verify_phrase_signature(request_body, signature_header, verification_token)
            True
        """
    if not verification_token or not signature_header:
        return False

    # Ensure body is bytes
    if isinstance(request_body, str):
        request_body = request_body.encode('utf-8')

    # Create HMAC digest using SHA256
    digest = hmac.new(
        verification_token.encode('utf-8'),
        request_body,
        digestmod=hashlib.sha256
    ).digest()

    # Base64 encode the digest and strip whitespace
    computed_signature = base64.b64encode(digest).decode('utf-8').strip()

    # Strip whitespace from the provided signature header
    provided_signature = signature_header.strip()

    # Compare signatures securely using constant-time comparison
    return hmac.compare_digest(computed_signature, provided_signature)
