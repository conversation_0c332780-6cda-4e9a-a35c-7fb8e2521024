# L10n

This module is responsible for the localization of the application.
It provides a way to load and manage localized strings for our apps, as well as a generator plugin that generates the access layer for our apps from a provided flat simple json file.
The main goal of this module is to provide a simple and easy way to manage localized strings for our apps independent of the platform.

## Structure

The module is structured in the following way:

```
.
├── ...
├── l10n                    # L10n feature module
│   ├── alex                # ALEX feature module
│   │   ├── deployment      # Deployment scripts for automatic transfering of translations to Phrase, Firestore, and back to Repository
│   │   ├── firebase        # Firebase Cloud function to intercept webhooks from Phrase and write updated translations into firestore
│   │   ├── projects        # Project configurations
│   │   └── src             # cli tool sources
│   ├── api                 # public sdk code, uses the processor and includes the generated access layer as well as bundles the translation files
│   ├── data                # Shared data between all the l10n features
│   │   ├── changes         # All changes that are done via alex cli for further processing in a proprietary format
│   │   ├── translations    # All our translations of all apps and all languages as simple json
│   │   └── infoPlist       # ios specific infoPlist files for all apps
│   └── internal            # internal sdk code to provide key mapping to locale and app
└── ...
```

## Concept

Each translation string must follow the following pattern:
`<feature>.<screen>.<element>`
Where `<feature>` is the name of the feature that the string belongs to.
`<screen>` is the name of the screen that the string belongs to.
`<element>` is the name of the element that the string belongs to.

Whenever we need an accessibility string, we can append the `accessibility` keyword to the end of the string, like this:
`<feature>.<screen>.<element>.accessibility`

We will unify all apps translations into a single file per language.
In order to differentiate the projects, we will add the project as suffix to those keys that should be overridden for a specific project.
The project suffix is the uppercase project name of the blossom apps (e.g. HEI,LAS,WIT).
This has been aligned between android and ios blossom projects.
The same id is used for the project configuration of alex.
This would look like this:

```json
{
  "feature1.screen1.element1": "translation for feature1 screen1 element1",
  "feature1.screen1.element2": "translation for feature1 screen1 element2",
  "feature1.screen1.element2.accessibility": "translation for feature1 screen1 element2 accessibility key",
  "feature1.screen1.element2.WIT": "translation for feature1 screen1 element2 for WIT project",
  "feature1.screen1.element2.HEI": "translation for feature1 screen1 element2 for HEI project"
}
```

Additionally we further want to allow override for specific platforms.
This is done by additionally adding the platform as suffix to the key.
This would look like this:

```json
{
  "feature1.screen1.element1": "translation for feature1 screen1 element1",
  "feature1.screen1.element2": "translation for feature1 screen1 element2",
  "feature1.screen1.element2.android": "translation for feature1 screen1 element2 for android",
  "feature1.screen1.element2.accessibility": "translation for feature1 screen1 element2 accessibility key",
  "feature1.screen1.element2.ios": "translation for feature1 screen1 element2 for ios",
  "feature1.screen1.element2.WIT": "translation for feature1 screen1 element2 for WIT project",
  "feature1.screen1.element2.HEI": "translation for feature1 screen1 element2 for HEI project",
  "feature1.screen1.element2.HEI.android": "translation for feature1 screen1 element2 for HEI project for android",
  "feature1.screen1.element2.HEI.ios": "translation for feature1 screen1 element2 for HEI project for ios"
}
```

For all of these translations we will generate an access layer that will be used to access the translations in the app.
The layer will logically group the translations by key.
Example: `OGL10n.Feature1.Screen1.Element1`

### Translation file

These json files will on the one hand be used to generate the access layer for the translations and on the other hand be used to load the translations into the app.
Therefore we bundle these files into the l10n module and resolve our access layer against these files.
This allows us to have a single source of truth for our translations and to have a single place to manage them.
Also we can replace those files with a different implementation at runtime to enable live updates of the translations.

### Placeholders

We can use placeholders in our translations. For easy understanding of the placeholders we enforce to use named placeholders.
The placeholder must be enclosed in curly braces prefixed with `%` here is an example: `%{userName}`.
Using named placeholders allows us to generate an access layer that enforces the correct usage of the placeholders and expect these as function parameters.

### Dynamic resolving

There sometimes is the need for resolving a certain translation key manually. Therefore you can invoke the `resolve` function directly and pass the translations key.
Example:

```
L10n.resolve("feature1.screen1.element2")
```

Note: The translation key is the value behind the property/fun of the generated access layer, but not the property itself.
For placeholders you can pass them as additional parameters to the `resolve` function like this:

```kotlin
L10n.resolve(key = "search.title", "%{foo}" to "bar", "%{bar}" to "foo")
```

```swift
L10n.resolve(key: "search.title", params:["%{foo}": "bar", "%{bar}": "foo"])
```

Note: You have to pass the whole placeholder as key.

## Usage

In order to use the l10n module you need to set it up correctly and pass the language changes to the module.

### Setup

How to use the l10n module.

#### Obtain the `L10n` instance

```kotlin
// Android
val l10n = OGAppKitSdk.init(androidApplication()).l10n()
```

```swift
// Swift
let l10n = OGAppKitSdk.shared.l10n()
```

Once the `L10n` instance has been initialized and configured you can access it easily by the global `OGL10n` property. This property is a singleton and can be accessed from anywhere in the app.

#### Configure it

Whenever the language change, you need to notify the l10n module about the change.
This can easily done by calling the `configure` method on the `L10n` instance with the new locale.
The locale requires the language and the region separated by `-`, e.g. `de-DE`.

```kotlin
l10n.configure(
    L10nConfig(
        project = "wit",
        locale = "de-DE"
    )
)
```

Where

- `project` defines the project the translations should be resolved for.
- `locale` defines the current selected language.

#### Access the translations

Once the l10n module is configured it will genereate an access layer for all the translations added as extensions on the L10n interface.
So you can access the translations like this:

```kotlin
OGL10n.Feature1.Screen1.Element1
```

#### Enable live updates

The l10n module supports live updates of the translations. Therefore we include a remote firestore instance that contains overrides for specific translations. In order to enable live updates you need to configure the l10n module with the FirestoreConfig.

```kotlin
l10n.configure(
    L10nConfig(
        //...
        firestoreConfig = FirestoreConfig()
    )
)
```

For local development you can use the [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite/connect_and_prototype). Which will allow you to test the live updates locally.
In order to use the emulator you need to configure the emulator host and port on the FirestoreConfig.
Use `********` for android and `127.0.0.1` for iOS host.

```kotlin
l10n.configure(
    L10nConfig(
        //...
        firestoreConfig = FirestoreConfig(
            host = "********",
            port = 8090,
            useEmulator = true
        )
    )
)
```

### Providing resources from shared modules

In case we need to bundle any additional resource that should be shared with both platforms we can simply apply the resource precompiled script by including the `ogAppKit.resources` plugin in the `build.gradle` file of the shared module that provides the resources.
Unfortunately there is no common solution to provide bundles resources to ios target. Therefore we implemented a workaround that will copy the resources to the ios target by compiling the files as sources. This will allow us to access the resources in the ios target.

You need to define the resources to be included via the `OGResources` extension like this:

```groovy
plugins {
    id("ogAppKit.resources")
}

OGResources{
    resources.register("Translations") {
        destPath = "translations"
        from = fileTree("../data/translations")
    }
}
```

where:

- `destPath` is the (sub)directory where the resources should be copied/bundles to. This is relevant for retrieving the resources in the app under a certain path.
- `from` is the source of the resources.

### Generator Plugin

The generator plugin is a gradle plugin that generates the access layer for the translations. It is located in the `processor` module.
The generator is a gradle plugin that can be applied the following:

```groovy
plugins {
    id("ogAppKit.l10n")
}

OGL10n {
    accessLayer {
        create("Base") {
            className = "OGL10nBase"
            inputFile = file("src/commonMain/resources/translations/en-US.json")
            packageName = "com.ottogroup.appkit.l10n"
        }
    }
}
```

This will generate a class `OGL10nBase` in the package `com.ottogroup.appkit.l10n` that contains an access layer for the translations in the file `src/commonMain/resources/translations/en-US.json`.
Since all the translations are generated as extension functions we can easily add multiple layers from different sources by simple providing additional configurations.
The plugin will register itself as a dependency to the `compileKotlin` task and will generate the access layer before the compilation of the kotlin sources and includes its files to the source set.

In order to correctly handle different projects and platforms, you need to configure the plugin accordingly.
This will remove duplicate entries that would be generated for the same key.

```groovy
OGL10n {
    projects = listOf("wit", "hei")
    platforms = listOf("android", "ios")
}
```

You can configure the plugin's outputDirectory which will also be added to the source set of the commonMain source set.

```groovy
OGL10n {
    outputDir = project.layout.buildDirectory.dir("generated/source/l10n")
}
```

The plugin is already integrated into the l10n api module as these generated access layer will be included in the final api module and thus be available for all apps.
Every module that want to extend the access layer with additional translations can do so by applying the plugin and providing the input file.
