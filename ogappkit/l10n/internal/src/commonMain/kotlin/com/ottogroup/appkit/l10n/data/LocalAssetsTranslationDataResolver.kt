package com.ottogroup.appkit.l10n.data

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.l10n.normalizedLocale
import com.ottogroup.appkit.resources.OGResources
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

internal class LocalAssetsTranslationDataResolver(
    coroutineScope: CoroutineScope,
    config: OGL10nConfigProvider,
    ogResources: OGResources
) : TranslationDataResolver {
    private val json = Json

    internal val localAssets = config.configState.map {
        runCatching {
            val assetFile = ogResources.resolve("translations/${it.normalizedLocale()}.json")
            val jsonString = assetFile.readUtf8()
            json.parseToJsonElement(jsonString).jsonObject
        }.getOrElse { JsonObject(emptyMap()) }
    }
        .flowOn(Dispatchers.IO)
        .stateIn(coroutineScope, SharingStarted.Eagerly, JsonObject(mapOf()))

    override fun getTranslation(key: String): String? {
        return try {
            localAssets.value[key]?.jsonPrimitive?.contentOrNull
        } catch (
            e: IllegalArgumentException
        ) {
            Logger.d(e) { "Error resolving translation $key" }
            null
        }
    }

    /**
     * This works with static bundled assets, so its data cannot change at runtime,
     * thus return empty flow
     */
    override val dataUpdatedFlow: Flow<Set<String>>
        get() = localAssets.map { it.keys }
}
