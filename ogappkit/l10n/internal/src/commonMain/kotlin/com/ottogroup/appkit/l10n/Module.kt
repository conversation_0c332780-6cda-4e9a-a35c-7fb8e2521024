package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.l10n.data.LocalAssetsTranslationDataResolver
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import com.ottogroup.appkit.l10n.resolver.DefaultResolver
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.module

public val l10nModule: Module = module {
    includes(l10nPlatformModule())
    single<OGL10n> { OGL10nImplDelegate(get(), get()) }
    single {
        LocalAssetsTranslationDataResolver(
            getCoroutineScope(),
            get(),
            get()
        )
    } bind TranslationDataResolver::class
    single { OGL10nConfigProvider() }
    factory { DefaultResolver(getAll()) }
}

public expect fun l10nPlatformModule(): Module
