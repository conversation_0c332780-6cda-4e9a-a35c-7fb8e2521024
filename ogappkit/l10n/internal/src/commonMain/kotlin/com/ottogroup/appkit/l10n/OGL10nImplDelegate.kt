package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.l10n.resolver.DefaultResolver

/**
 * Since we want to expose a pretty easy access layer that directly calls the resolving mechanism, we need to expose an public object.
 * Although this is not the best practice, it is the easiest way to provide a simple access layer.
 * As we still rely internally on the L10n interface, we will configure everything as usual and just delegate the calls to the actual implementation.
 *
 */
internal class OGL10nImplDelegate(
    private val configProvider: OGL10nConfigProvider,
    defaultResolver: DefaultResolver,
) : BaseOGL10n(resolver = defaultResolver) {

    init {
        setAsCurrentHolder()
    }

    override fun configure(config: OGL10nConfig) {
        super.configure(config)
        configProvider.update(config)
    }
}
