package com.ottogroup.appkit.l10n.resolver

import com.ottogroup.appkit.base.benchmark
import com.ottogroup.appkit.base.combine
import com.ottogroup.appkit.base.log
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.sample

internal class DefaultResolver(
    resolvers: List<TranslationDataResolver>
) : Resolver {
    private var sortedResolvers = resolvers.sortedBy { it.priority }

    override fun resolve(key: String, project: String?, platform: String?): String? {
        val benchmark = benchmark()
        sortedResolvers.forEach {
            val benchmarkResolver = benchmark()
            val translation = it.getTranslation(key)
            benchmarkResolver.log("${it::class.simpleName} resolve $key")
            if (translation != null) {
                benchmark.log("resolve $key")
                return translation
            }
        }
        benchmark.log("resolve $key")
        return null
    }

    @OptIn(FlowPreview::class)
    override val dataUpdatedFlow: Flow<Set<String>>
        get() = sortedResolvers
            .map { it.dataUpdatedFlow }
            .combine()
            .map { it.flatten().toSet() }
            .sample(1000.milliseconds)

    override fun addTranslationResolver(resolver: TranslationDataResolver) {
        sortedResolvers = (sortedResolvers + resolver).sortedBy { it.priority }
    }
}
