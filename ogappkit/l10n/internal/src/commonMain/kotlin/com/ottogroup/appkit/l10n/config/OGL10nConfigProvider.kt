package com.ottogroup.appkit.l10n.config

import com.ottogroup.appkit.l10n.OGL10nConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

internal class OGL10nConfigProvider {
    private val _configState = MutableStateFlow(OGL10nConfig())
    val configState = _configState.asStateFlow()

    fun update(config: OGL10nConfig) {
        _configState.value = config
    }
}
