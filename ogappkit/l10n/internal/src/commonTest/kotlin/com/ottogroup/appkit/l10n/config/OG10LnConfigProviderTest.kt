package com.ottogroup.appkit.l10n.config

import app.cash.turbine.test
import com.ottogroup.appkit.l10n.OGL10nConfig
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest

class OG10LnConfigProviderTest {

    @Test
    fun `update changes config state`() = runTest {
        val configProvider = OGL10nConfigProvider()

        configProvider.configState.test {
            assertEquals(
                OGL10nConfig(),
                awaitItem()
            )

            val newConfig = OGL10nConfig(
                project = "test",
                locale = "de-DE",
            )
            configProvider.update(newConfig)

            assertEquals(
                newConfig,
                awaitItem()
            )
        }
    }
}
