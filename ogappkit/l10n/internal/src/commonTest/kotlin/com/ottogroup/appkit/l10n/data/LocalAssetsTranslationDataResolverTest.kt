package com.ottogroup.appkit.l10n.data

import app.cash.turbine.test
import com.ottogroup.appkit.l10n.OGL10nConfig
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.resources.OGResource
import com.ottogroup.appkit.resources.OGResources
import com.ottogroup.appkit.resources.ResourceNotFoundException
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

class LocalAssetsTranslationDataResolverTest {

    @Test
    fun `provide default empty json object when failing reading resource file`() = runTest {
        val configProvider = OGL10nConfigProvider()
        backgroundScope.launch {
            configProvider.configState.collect {}
        }

        val resolver =
            LocalAssetsTranslationDataResolver(backgroundScope, configProvider, MockResources())

        resolver.localAssets.test {
            // default empty map
            assertEquals(
                JsonObject(emptyMap()),
                awaitItem()
            )
            configProvider.update(OGL10nConfig(locale = "de-DE"))

            assertEquals(
                JsonObject(mapOf("Account.Login" to JsonPrimitive("value1"))),
                awaitItem()
            )
            configProvider.update(OGL10nConfig(locale = "pl-PL"))

            assertEquals(
                JsonObject(emptyMap()),
                awaitItem()
            )
            assertNull(resolver.getTranslation("Account.Login"))
            expectNoEvents()
        }
    }

    @Test
    fun `return null when translations not found`() = runTest {
        val configProvider = OGL10nConfigProvider()
        backgroundScope.launch {
            configProvider.configState.collect {}
        }

        val resolver =
            LocalAssetsTranslationDataResolver(backgroundScope, configProvider, MockResources())

        resolver.localAssets.test {
            // default empty map
            assertEquals(
                JsonObject(emptyMap()),
                awaitItem()
            )

            configProvider.update(OGL10nConfig(locale = "nl-NL"))

            assertEquals(
                JsonObject(mapOf("Account.Not.Login" to JsonPrimitive("value1"))),
                awaitItem()
            )

            assertNull(resolver.getTranslation("Account.Login"))

            expectNoEvents()
        }
    }

    @Test
    fun `return value when translations found`() = runTest {
        val configProvider = OGL10nConfigProvider()
        backgroundScope.launch {
            configProvider.configState.collect {}
        }

        val resolver =
            LocalAssetsTranslationDataResolver(backgroundScope, configProvider, MockResources())

        resolver.localAssets.test {
            // default empty map
            assertEquals(
                JsonObject(emptyMap()),
                awaitItem()
            )

            configProvider.update(OGL10nConfig(locale = "de-DE"))

            assertEquals(
                JsonObject(mapOf("Account.Login" to JsonPrimitive("value1"))),
                awaitItem()
            )

            assertEquals("value1", resolver.getTranslation("Account.Login"))

            expectNoEvents()
        }
    }

    @Test
    fun `locale is normalized`() = runTest {
        val configProvider = OGL10nConfigProvider()
        backgroundScope.launch {
            configProvider.configState.collect {}
        }

        val resolver =
            LocalAssetsTranslationDataResolver(backgroundScope, configProvider, MockResources())

        resolver.localAssets.test {
            // default empty map
            assertEquals(
                JsonObject(emptyMap()),
                awaitItem()
            )

            configProvider.update(OGL10nConfig(locale = "de_DE"))

            assertEquals(
                JsonObject(mapOf("Account.Login" to JsonPrimitive("value1"))),
                awaitItem()
            )

            assertEquals("value1", resolver.getTranslation("Account.Login"))

            expectNoEvents()
        }
    }

    internal class MockResources() : OGResources {
        override fun list(path: String): List<OGResource> {
            return emptyList()
        }

        override fun resolve(path: String): OGResource {
            return MockResource(path)
        }
    }

    internal class MockResource(override val path: String) : OGResource {
        override val name: String
            get() = path.substringAfterLast("/").substringBeforeLast(".")
        override val extension: String
            get() = path.substringAfterLast(".")

        override fun readUtf8(): String {
            return when (name) {
                "en-US" -> {
                    throw ResourceNotFoundException("Resource not found")
                }

                "nl-NL" -> {
                    JsonObject(mapOf("Account.Not.Login" to JsonPrimitive("value1"))).toString()
                }

                "de-DE" -> {
                    JsonObject(mapOf("Account.Login" to JsonPrimitive("value1"))).toString()
                }

                else -> {
                    throw ResourceNotFoundException("Resource not found")
                }
            }
        }
    }
}
