package com.ottogroup.appkit.l10n.resolver

import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

class DefaultResolverTest {

    private lateinit var resolver: DefaultResolver
    private lateinit var translationDataResolver1: TranslationDataResolver
    private lateinit var translationDataResolver2: TranslationDataResolver
    private lateinit var translationDataResolver3: TranslationDataResolver

    @BeforeTest
    fun setUp() {
        translationDataResolver1 = object : TranslationDataResolver {
            override val priority: Int = 1
            override fun getTranslation(key: String): String? = if (key == "key1") "translation1" else null
            override val dataUpdatedFlow: Flow<Set<String>>
                get() = flowOf(emptySet())
        }
        translationDataResolver2 = object : TranslationDataResolver {
            override val priority: Int = 2
            override fun getTranslation(key: String): String? = if (key == "key2" || key == "key1") "translation2" else null
            override val dataUpdatedFlow: Flow<Set<String>>
                get() = flowOf(emptySet())
        }
        translationDataResolver3 = object : TranslationDataResolver {
            override val priority: Int = 0
            override fun getTranslation(key: String): String? = if (key == "key2" || key == "key1") "translation3" else null
            override val dataUpdatedFlow: Flow<Set<String>>
                get() = flowOf(emptySet())
        }

        resolver = DefaultResolver(listOf(translationDataResolver1, translationDataResolver2))
    }

    @Test
    fun `resolve returns translation from first resolver`() {
        val result = resolver.resolve("key1", null, null)
        assertEquals("translation1", result)
    }

    @Test
    fun `resolve returns translation from second resolver`() {
        val result = resolver.resolve("key2", null, null)
        assertEquals("translation2", result)
    }

    @Test
    fun `resolve returns null when no translation found`() {
        val result = resolver.resolve("key3", null, null)
        assertNull(result)
    }

    @Test
    fun `resolve returns translation from higher priority resolver`() {
        val result = resolver.resolve("key1", null, null)
        assertEquals("translation1", result)
    }

    @Test
    fun `resolve returns null when resolvers list is empty`() {
        resolver = DefaultResolver(emptyList())
        val result = resolver.resolve("key1", null, null)
        assertNull(result)
    }

    @Test
    fun `resolve includes later added translation resolver`() {
        resolver.addTranslationResolver(translationDataResolver3)
        val result = resolver.resolve("key1", null, null)
        assertEquals("translation3", result)
    }
}
