package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.l10n.resolver.DefaultResolver
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull

class DefaultOGL10NDelegateTest {

    private lateinit var configProvider: OGL10nConfigProvider
    private lateinit var defaultResolver: DefaultResolver
    private lateinit var delegate: OGL10nImplDelegate

    @BeforeTest
    fun setUp() {
        configProvider = OGL10nConfigProvider()
        defaultResolver = DefaultResolver(emptyList())
    }

    @Test
    fun `initializing class will set it as current holder`() {
        val oldL10n = OGL10n.shared()
        delegate = OGL10nImplDelegate(configProvider, defaultResolver)
        assertNotNull(oldL10n)
        assertNotEquals(delegate, oldL10n)
        assertEquals(delegate, OGL10n.shared())
    }

    @Test
    fun `configure passes the config to config provider`() {
        delegate = OGL10nImplDelegate(configProvider, defaultResolver)
        delegate.configure(OGL10nConfig("test", "de-DE"))
        assertEquals(OGL10nConfig("test", "de-DE"), configProvider.configState.value)
    }
}
