package com.ottogroup.appkit.resources

import android.content.res.AssetManager

internal class OGResourcesImpl(
    private val assetManager: AssetManager
) : OGResources {
    override fun list(path: String): List<OGResource> {
        return assetManager.list(path)?.map {
            AndroidResource(it, path, it.substringAfterLast('.'), assetManager)
        } ?: return emptyList()
    }

    override fun resolve(path: String): OGResource {
        return AndroidResource(path.substringAfterLast('/'), path, path.substringAfterLast('.'), assetManager)
    }
}

internal data class AndroidResource(
    override val name: String,
    override val path: String,
    override val extension: String,
    private val assetManager: AssetManager
) : OGResource {

    @Throws(IOException::class)
    override fun readUtf8(): String {
        assetManager.open(path).bufferedReader().use {
            return it.readText()
        }
    }
}
