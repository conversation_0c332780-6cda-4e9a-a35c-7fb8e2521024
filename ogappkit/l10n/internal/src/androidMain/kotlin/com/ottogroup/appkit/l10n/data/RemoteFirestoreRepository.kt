package com.ottogroup.appkit.l10n.data

import co.touchlab.kermit.Logger
import com.google.firebase.Firebase
import com.google.firebase.FirebaseApp
import com.google.firebase.app
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.MemoryCacheSettings
import com.google.firebase.firestore.firestoreSettings
import com.google.firebase.firestore.snapshots
import com.ottogroup.appkit.l10n.OGFirestoreConfig
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn

@OptIn(kotlinx.coroutines.ExperimentalCoroutinesApi::class)
internal class RemoteFirestoreRepository(
    coroutineScope: CoroutineScope,
    config: OGL10nConfigProvider,
    private val firebaseApp: FirebaseApp = Firebase.app
) : FirestoreRepository {

    private val translationsDataState: StateFlow<Translations>

    init {
        translationsDataState = config.configState
            .mapNotNull { it.firestore }
            .distinctUntilChanged()
            .setupFirestore()
            .flatMapLatest { firestore ->
                firestore.collection("translations")
                    .snapshots()
                    .mapNotNull { snapshot -> snapshot.documents.mapNotNull { document -> document.toTranslation() } }
                    .map { Translations(it) }
            }.stateIn(coroutineScope, SharingStarted.Eagerly, Translations(emptyList()))
    }

    private fun Flow<OGFirestoreConfig>.setupFirestore(): Flow<FirebaseFirestore> {
        return map { config ->
            FirebaseFirestore.getInstance(firebaseApp).apply {
                config.host?.let { host ->
                    if (config.useEmulator) {
                        useEmulator(host, config.port ?: 8080)
                        firestoreSettings = firestoreSettings {
                            setHost("$host:${config.port ?: 8080}")
                            setSslEnabled(false)
                            setLocalCacheSettings(MemoryCacheSettings.newBuilder().build())
                        }
                    } else {
                        firestoreSettings = firestoreSettings { setHost(host) }
                    }
                }
            }
        }
    }

    private fun DocumentSnapshot.toTranslation(): Translation? {
        return try {
            val key = getString("key") ?: return null
            val deprecated = getBoolean("deprecated") ?: false
            val translationMap = get("translations") as? Map<String, Map<String, *>>
                ?: emptyMap()
            val translations = translationMap.mapNotNull { (locale, value) ->
                val text = value["text"] as? String ?: return@mapNotNull null
                val verified = value["verified"] as? Boolean ?: false
                locale to TranslationValue(text, verified)
            }.toMap()
            Translation(
                key = key,
                deprecated = deprecated,
                translations = translations
            )
        } catch (e: Exception) {
            Logger.e("Error while parsing translation data", e)
            null
        }
    }

    override fun getAllTranslationData(): Flow<Translations> = translationsDataState

    override fun getTranslationDataFor(locale: String): Flow<Translations> {
        return translationsDataState
            .mapNotNull { allTranslations ->
                allTranslations.copy(
                    translations = allTranslations.translations.map { translationKeys ->
                        translationKeys.copy(translations = translationKeys.translations.filterKeys { it == locale })
                    }
                )
            }
    }
}
