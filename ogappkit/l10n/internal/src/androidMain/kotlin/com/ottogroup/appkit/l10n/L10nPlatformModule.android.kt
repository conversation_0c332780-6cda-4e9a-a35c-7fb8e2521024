package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.l10n.data.FirestoreRepository
import com.ottogroup.appkit.l10n.data.RemoteFirestoreRepository
import com.ottogroup.appkit.l10n.data.RemoteTranslationDataResolver
import com.ottogroup.appkit.l10n.data.ResourceTranslationDataResolver
import com.ottogroup.appkit.l10n.data.ResourceTranslationDataResolverImpl
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import org.koin.android.ext.koin.androidContext
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.binds
import org.koin.dsl.module

public actual fun l10nPlatformModule(): Module = module {
    factory {
        ResourceTranslationDataResolverImpl(androidContext())
    } binds arrayOf(TranslationDataResolver::class, ResourceTranslationDataResolver::class)

    single<FirestoreRepository> { RemoteFirestoreRepository(getCoroutineScope(), get()) }
    single {
        RemoteTranslationDataResolver(
            getCoroutineScope(),
            get(),
            get()
        )
    } bind TranslationDataResolver::class
}
