package com.ottogroup.appkit.l10n.data

import com.ottogroup.appkit.base.combineWithPreviousValue
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import com.ottogroup.appkit.l10n.normalizedLocale
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

@OptIn(ExperimentalCoroutinesApi::class)
internal class RemoteTranslationDataResolver(
    coroutineScope: CoroutineScope,
    config: OGL10nConfigProvider,
    private val firestoreRepository: FirestoreRepository
) : TranslationDataResolver {

    private val translations = config.configState
        .map { it.normalizedLocale() }
        .distinctUntilChanged()
        .flatMapLatest { locale ->
            // map latest locale to translation data for that locale
            firestoreRepository.getTranslationDataFor(locale)
                .map {
                    // transform translation data for easy lookup
                    val data = it.translations.associate { translationKey ->
                        translationKey.key to translationKey.translations[locale]
                    }
                    data
                }
        }
        .stateIn(
            coroutineScope,
            SharingStarted.Eagerly,
            emptyMap()
        )

    override fun getTranslation(key: String): String? {
        val translation = translations.value[key]
        return if (translation?.verified == true) translation.text else null
    }

    override val priority: Int
        get() = -10

    /**
     * This flow emits when the data is updated.
     */
    override val dataUpdatedFlow: Flow<Set<String>>
        get() = translations.combineWithPreviousValue().map { (old, new) ->
            val newKeys = new.keys
            val oldKeys = old?.keys ?: emptySet()
            val addedKeys = newKeys - oldKeys
            val removedKeys = oldKeys.filter { it !in newKeys }
            val changedKeys = newKeys.filter { new[it] != old?.get(it) }
            val updatedKeys = (addedKeys + removedKeys + changedKeys).toSet()
            updatedKeys
        }
}
