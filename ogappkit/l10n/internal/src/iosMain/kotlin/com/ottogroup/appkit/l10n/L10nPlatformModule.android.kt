package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.data.ResourceTranslationDataResolver
import com.ottogroup.appkit.l10n.data.ResourceTranslationDataResolverImpl
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import org.koin.core.module.Module
import org.koin.dsl.binds
import org.koin.dsl.module

public actual fun l10nPlatformModule(): Module = module {
    factory {
        ResourceTranslationDataResolverImpl()
    } binds arrayOf(TranslationDataResolver::class, ResourceTranslationDataResolver::class)
}
