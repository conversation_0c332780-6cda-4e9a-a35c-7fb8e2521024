package com.ottogroup.appkit.resources

internal class OGResourcesImpl(private val files: BundledFiles) : OGResources {

    override fun list(path: String): List<OGResource> {
        return files.getFiles(path).map { (_, file) ->
            IosResource(
                file.path.substringAfterLast("/").substringBeforeLast("."),
                file.path,
                file.path.substringAfterLast('.'),
                file.content
            )
        }
    }

    @Throws(ResourceNotFoundException::class)
    override fun resolve(path: String): OGResource {
        val file = files.getFile(path) ?: throw ResourceNotFoundException("Resource not found: $path")
        return IosResource(
            file.path.substringAfterLast("/").substringBeforeLast("."),
            file.path,
            file.path.substringAfterLast('.'),
            file.content
        )
    }
}

public class IosResource(
    override val name: String,
    override val path: String,
    override val extension: String,
    private val content: ByteArray
) : OGResource {

    @Throws(IOException::class)
    override fun readUtf8(): String {
        return content.decodeToString()
    }
}
