package com.ottogroup.appkit.l10n.data

import com.ottogroup.appkit.l10n.OGL10nConfig
import com.ottogroup.appkit.l10n.config.OGL10nConfigProvider
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

@OptIn(ExperimentalCoroutinesApi::class)
class RemoteTranslationDataResolverTest {

    private val testRepository = TestFirestoreRepository()
    private val configProvider = OGL10nConfigProvider()

    @Test
    fun `returns null when translation key does not exist`() = runTest {
        val resolver = RemoteTranslationDataResolver(
            coroutineScope = backgroundScope,
            config = configProvider,
            firestoreRepository = testRepository
        )
        assertNull(resolver.getTranslation("non_existent_key"))
    }

    @Test
    fun `returns null when translation is not verified`() = runTest {
        val resolver = RemoteTranslationDataResolver(
            coroutineScope = backgroundScope,
            config = configProvider,
            firestoreRepository = testRepository
        )
        testRepository.updateTranslations(
            listOf(
                Translation(
                    "key1",
                    deprecated = false,
                    mapOf("en" to TranslationValue("Hello", verified = false))
                )
            )
        )
        configProvider.update(OGL10nConfig(null, "en"))

        assertNull(resolver.getTranslation("key1"))
    }

    @Test
    fun `returns translation text when key exists and is verified`() = runTest {
        val resolver = RemoteTranslationDataResolver(
            coroutineScope = backgroundScope,
            config = configProvider,
            firestoreRepository = testRepository
        )
        testRepository.updateTranslations(
            listOf(
                Translation(
                    "key1",
                    deprecated = false,
                    mapOf("en" to TranslationValue("Hello", verified = true))
                )
            )
        )
        configProvider.update(OGL10nConfig(null, "en"))
        advanceTimeBy(200)
        assertEquals("Hello", resolver.getTranslation("key1"))
    }

    @Test
    fun `updates translations when locale changes`() = runTest {
        val resolver = RemoteTranslationDataResolver(
            coroutineScope = backgroundScope,
            config = configProvider,
            firestoreRepository = testRepository
        )
        testRepository.updateTranslations(
            listOf(
                Translation(
                    "key1",
                    deprecated = false,
                    mapOf(
                        "en" to TranslationValue("Hello", verified = true),
                        "de" to TranslationValue("Hallo", verified = true)
                    )
                )
            )
        )

        configProvider.update(OGL10nConfig(null, "en"))
        advanceTimeBy(200)
        assertEquals("Hello", resolver.getTranslation("key1"))

        configProvider.update(OGL10nConfig(null, "de"))
        advanceTimeBy(200)
        assertEquals("Hallo", resolver.getTranslation("key1"))
    }

    @Test
    fun `has expected priority`() {
        val resolver = RemoteTranslationDataResolver(
            coroutineScope = TestScope(),
            config = configProvider,
            firestoreRepository = testRepository
        )
        assertEquals(-10, resolver.priority)
    }
}

private class TestFirestoreRepository : FirestoreRepository {
    private val translationsFlow = MutableStateFlow(Translations(emptyList()))

    fun updateTranslations(translations: List<Translation>) {
        translationsFlow.value = Translations(translations)
    }

    override fun getAllTranslationData() = translationsFlow

    override fun getTranslationDataFor(locale: String) = translationsFlow
}
