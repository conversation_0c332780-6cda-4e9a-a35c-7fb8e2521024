package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.PlaceholderType
import org.junit.Assert.assertEquals
import org.junit.Test

class PlaceholderTypeTest {
    @Test
    fun `default named placeholder of string`() {
        val string = "Hallo %{username}"
        assertEquals(PlaceholderType.String, PlaceholderType.findAll(string).first().type)
        assertEquals("username", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%{username}", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `alternative named placeholder of string`() {
        val string = "Hallo %{{username}}"
        assertEquals(PlaceholderType.String, PlaceholderType.findAll(string).first().type)
        assertEquals("username", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%{{username}}", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `alternative 2 named placeholder of string`() {
        val string = "Hallo {{username}}"
        assertEquals(PlaceholderType.String, PlaceholderType.findAll(string).first().type)
        assertEquals("username", PlaceholderType.findAll(string).first().paramName)
        assertEquals("{{username}}", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `simple c style placeholder of string`() {
        val string = "Hallo %s"
        assertEquals(PlaceholderType.String, PlaceholderType.findAll(string).first().type)
        assertEquals("%s", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%s", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `positional c style placeholder of string`() {
        val string = "Hallo %1\$s"
        assertEquals(PlaceholderType.String, PlaceholderType.findAll(string).first().type)
        assertEquals("%1\$s", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%1\$s", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `simple c style placeholder of decimal`() {
        val string = "No #%d"
        assertEquals(PlaceholderType.Int, PlaceholderType.findAll(string).first().type)
        assertEquals("%d", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%d", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `positional c style placeholder of decimal`() {
        val string = "No #%1\$d"
        assertEquals(PlaceholderType.Int, PlaceholderType.findAll(string).first().type)
        assertEquals("%1\$d", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%1\$d", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `positional c style placeholder of floating point`() {
        val string = "No #%1\$f"
        assertEquals(PlaceholderType.Float, PlaceholderType.findAll(string).first().type)
        assertEquals("%1\$f", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%1\$f", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `simple c style placeholder of floating point`() {
        val string = "No #%f"
        assertEquals(PlaceholderType.Float, PlaceholderType.findAll(string).first().type)
        assertEquals("%f", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%f", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `positional c style placeholder of floating point with precision`() {
        val string = "No #%1\$.2f"
        assertEquals(PlaceholderType.Float, PlaceholderType.findAll(string).first().type)
        assertEquals("%1\$.2f", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%1\$.2f", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `simple c style placeholder of floating point with precision`() {
        val string = "No #%.2f"
        assertEquals(PlaceholderType.Float, PlaceholderType.findAll(string).first().type)
        assertEquals("%.2f", PlaceholderType.findAll(string).first().paramName)
        assertEquals("%.2f", PlaceholderType.findAll(string).first().placeholder)
    }

    @Test
    fun `multiple placeholder with same name can be found and treated as one`() {
        val string = "Hallo %{username} %{username} %{username}"
        assertEquals(3, PlaceholderType.findAll(string).size)
        assertEquals(1, PlaceholderType.findAll(string).toSet().size)
    }
}
