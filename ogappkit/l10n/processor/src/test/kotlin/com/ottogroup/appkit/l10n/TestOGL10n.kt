package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.L10nGenerator
import kotlin.test.assertEquals
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import org.intellij.lang.annotations.Language
import org.junit.Test

class TestOGL10n {

    @Language("JSON")
    val json = """
            {
    "deals.onboarding.title" : "Title",
    "deals.onboarding.description" : "Description",
    "deals.onboarding.message" : "Hallo %{username}",
    "deals.onboarding.message.ios" : "Hallo %{username2}",
    "deals.onboarding.message2" : "Hallo %{{username}}",
    "deals.onboarding.message3" : "<PERSON><PERSON> {{username}}",
    "deals.onboarding.message4" : "Hallo %s %s %s",
    "deals.onboarding.button" : "Logout %1${'$'}s %2${'$'}s %3${'$'}s",
    "deals.onboarding.button2" : "Login %s %d %f",
    "deals.onboarding.button2.android" : "Login %s %d %.0f",
    "deals.onboarding.button2.ios" : "Logout %1${'$'}s %1${'$'}s %5${'$'}s",
    "deals.onboarding.button2.wit" : "Login %.2f",
    "deals.onboarding.button2.wit.ios" : "Login %s %d %1${'$'}.5f %2${'$'}f",
    "deals.onboarding.Items" : [
    "AAA", "BBB"],
    "deals.title" : "DEalsTitle",
    "deals.gameScreen.title" : "GameTitle",
    "deals.gameScreen.cta" : "GameCta",
    "general.ok" : "OK",
    "general.cancel" : "CANCEL",
    "general.abort" : "ABORT"
    }
    """.trimIndent()

    @Language("JSON")
    val a = """
        {
        "deals": {
          "title":""
        },
        "deals.gameScreen": {
        "title":"",
        "cta":""
        },
        "general": {
          "ok":"",
          "cancel":"",
          "abort":""
        },
        "deals.onboarding": {
        "title" : "",
        "description" : "",
        "message" : "",
        "message2" : "",
        "message3" : "",
        "message4" : "",
        "button" : "",
        "button2" : "",
        "Items" : ""
        }
        }
    """.trimIndent()


    @Test
    fun `runtime keys are excluded`() {

        @Language("JSON")
        val json = """
            {
              "deals" : "",
              "deals.android": "",
              "deals.ios": "",
              "deals.wit.android": "",
              "deals.wit.ios": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nGenerator(listOf("android", "ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")

        assertEquals(
            """
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public object OGL10n {
              public val Deals: String
                get() = getString(key = "deals")
            }

            """.trimIndent(), kotlinFile.toString()
        )
    }

    @Test
    fun `runtime keys will add a base`() {
        @Language("JSON")
        val json = """
            {
              "deals.android": "",
              "deals.ios": "",
              "deals.wit.android": "",
              "deals.wit.ios": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nGenerator(listOf("android", "ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")

        assertEquals(
            """
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public object OGL10n {
              public val Deals: String
                get() = getString(key = "deals")
            }

            """.trimIndent(), kotlinFile.toString()
        )
    }

    @Test
    fun `placeholders will be extracted into function parameters`() {
        @Language("JSON")
        val json = """
            {
              "deals" : "Hallo %{lastName}",
              "welcome" : "Hallo %{firstName} %{lastName}",
              "onboarding" : "No of items: %d#",
              "multiple" : "%s %s %s %s",
              "positional" : "%1${'$'}s %2${'$'}s %4${'$'}s %3${'$'}s",
              "complex": "Hallo %{userName} this %s is your %1${'$'}f login %.4f"
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nGenerator(listOf("android", "ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals(
            """
            package com.ottogroup.appkit.l10n

            import kotlin.Float
            import kotlin.Int
            import kotlin.String

            public object OGL10n {
              public fun Complex(
                userName: String,
                param1: String,
                param2: Float,
                param3: Float,
              ): String = getString(key = "complex", "%{userName}" to userName, "%s" to param1, "%1${'$'}f" to param2, "%.4f" to param3)

              public fun Deals(lastName: String): String = getString(key = "deals", "%{lastName}" to lastName)

              public fun Multiple(
                param0: String,
                param1: String,
                param2: String,
                param3: String,
              ): String = getString(key = "multiple", "%s" to param0, "%s" to param1, "%s" to param2, "%s" to param3)

              public fun Onboarding(param0: Int): String = getString(key = "onboarding", "%d" to param0)

              public fun Positional(
                param0: String,
                param1: String,
                param2: String,
                param3: String,
              ): String = getString(key = "positional", "%1${'$'}s" to param0, "%2${'$'}s" to param1, "%4${'$'}s" to param2, "%3${'$'}s" to param3)

              public fun Welcome(firstName: String, lastName: String): String = getString(key = "welcome", "%{firstName}" to firstName, "%{lastName}" to lastName)
            }

            """.trimIndent(), kotlinFile.toString()
        )
    }

    @Test
    fun `nested keys will create nested objects`() {
        @Language("JSON")
        val json = """
            {
              "deals.onboarding": "",
              "deals.title": "",
              "deals.welcome.title": "",
              "deals.welcome.headline": "",
              "deals.welcome.message": "",
              "deals.welcome.child.tag": "",
              "deals.welcome.child.text": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nGenerator(listOf("android", "ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")

        assertEquals(
            """
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public object OGL10n {
              public object Deals {
                public val Onboarding: String
                  get() = getString(key = "deals.onboarding")

                public val Title: String
                  get() = getString(key = "deals.title")

                public object Welcome {
                  public val Headline: String
                    get() = getString(key = "deals.welcome.headline")

                  public val Message: String
                    get() = getString(key = "deals.welcome.message")

                  public val Title: String
                    get() = getString(key = "deals.welcome.title")

                  public object Child {
                    public val Tag: String
                      get() = getString(key = "deals.welcome.child.tag")

                    public val Text: String
                      get() = getString(key = "deals.welcome.child.text")
                  }
                }
              }
            }

            """.trimIndent(), kotlinFile.toString()
        )
    }

    @Test
    fun `nested and parent definition will add operator fun for parent case`() {
        @Language("JSON")
        val json = """
            {
              "deals": "",
              "deals.title": "",
              "deals.subtitle": "",
              "deals.child": "Hallo %{username}",
              "deals.child.button": "",
              "deals.child.message": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nGenerator(listOf("android", "ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals(
            """
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public object OGL10n {
              public object Deals {
                public val Subtitle: String
                  get() = getString(key = "deals.subtitle")

                public val Title: String
                  get() = getString(key = "deals.title")

                public operator fun invoke(): String = getString(key = "deals")

                public object Child {
                  public val Button: String
                    get() = getString(key = "deals.child.button")

                  public val Message: String
                    get() = getString(key = "deals.child.message")

                  public operator fun invoke(username: String): String = getString(key = "deals.child", "%{username}" to username)
                }
              }
            }

            """.trimIndent(), kotlinFile.toString()
        )
    }


}

