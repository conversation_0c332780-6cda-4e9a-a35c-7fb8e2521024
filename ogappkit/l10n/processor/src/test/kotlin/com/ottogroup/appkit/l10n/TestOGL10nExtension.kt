package com.ottogroup.appkit.l10n

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import org.intellij.lang.annotations.Language
import org.junit.Assert.assertEquals
import org.junit.Test

class TestOGL10nExtension {

    @Test
    fun `runtime keys are excluded`() {

        @Language("JSON")
        val json = """
            {
              "deals" : "",
              "deals.android": "",
              "deals.ios": "",
              "deals.wit.android": "",
              "deals.wit.ios": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nExtensionGenerator(listOf("android","ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals("""
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public val OGL10n.Deals: String
              get() = getString(key = "deals")

            """.trimIndent(), kotlinFile.toString())
    }

    @Test
    fun `runtime keys will add a base`() {
        @Language("JSON")
        val json = """
            {
              "deals.android": "",
              "deals.ios": "",
              "deals.wit.android": "",
              "deals.wit.ios": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nExtensionGenerator(listOf("android","ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals("""
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public val OGL10n.Deals: String
              get() = getString(key = "deals")

            """.trimIndent(), kotlinFile.toString())
    }

    @Test
    fun `placeholders will be extracted into function parameters`() {
        @Language("JSON")
        val json = """
            {
              "deals" : "Hallo %{lastName}",
              "welcome" : "Hallo %{firstName} %{lastName}",
              "onboarding" : "No of items: %d#",
              "multiple" : "%s %s %s %s",
              "positional" : "%1${'$'}s %2${'$'}s %4${'$'}s %3${'$'}s",
              "complex": "Hallo %{userName} this %s is your %1${'$'}f login %.4f"
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nExtensionGenerator(listOf("android","ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")

        assertEquals("""
            package com.ottogroup.appkit.l10n

            import kotlin.Float
            import kotlin.Int
            import kotlin.String

            public fun OGL10n.Complex(
              userName: String,
              param1: String,
              param2: Float,
              param3: Float,
            ): String = getString(key = "complex", "%{userName}" to userName, "%s" to param1, "%1\${'$'}f" to param2, "%.4f" to param3)

            public fun OGL10n.Deals(lastName: String): String = getString(key = "deals", "%{lastName}" to lastName)

            public fun OGL10n.Multiple(param0: String): String = getString(key = "multiple", "%s" to param0)

            public fun OGL10n.Onboarding(param0: Int): String = getString(key = "onboarding", "%d" to param0)

            public fun OGL10n.Positional(
              param0: String,
              param1: String,
              param2: String,
              param3: String,
            ): String = getString(key = "positional", "%1\${'$'}s" to param0, "%2\${'$'}s" to param1, "%4\${'$'}s" to param2, "%3\${'$'}s" to param3)

            public fun OGL10n.Welcome(firstName: String, lastName: String): String = getString(key = "welcome", "%{firstName}" to firstName, "%{lastName}" to lastName)

            """.trimIndent(), kotlinFile.toString())
    }

    @Test
    fun `nested keys will create nested objects`() {
        @Language("JSON")
        val json = """
            {
              "deals.onboarding": "",
              "deals.title": "",
              "deals.welcome.title": "",
              "deals.welcome.headline": "",
              "deals.welcome.message": "",
              "deals.welcome.child.tag": "",
              "deals.welcome.child.text": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nExtensionGenerator(listOf("android","ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals("""
            package com.ottogroup.appkit.l10n

            import kotlin.String

            public val OGL10nDeals.Onboarding: String
              get() = getString(key = "deals.onboarding")

            public val OGL10nDeals.Title: String
              get() = getString(key = "deals.title")

            public val OGL10nDealsWelcomeChild.Tag: String
              get() = getString(key = "deals.welcome.child.tag")

            public val OGL10nDealsWelcomeChild.Text: String
              get() = getString(key = "deals.welcome.child.text")

            public object OGL10nDealsWelcomeChild

            public val OGL10nDealsWelcome.Child: OGL10nDealsWelcomeChild
              get() = OGL10nDealsWelcomeChild

            public val OGL10nDealsWelcome.Headline: String
              get() = getString(key = "deals.welcome.headline")

            public val OGL10nDealsWelcome.Message: String
              get() = getString(key = "deals.welcome.message")

            public val OGL10nDealsWelcome.Title: String
              get() = getString(key = "deals.welcome.title")

            public object OGL10nDealsWelcome

            public val OGL10nDeals.Welcome: OGL10nDealsWelcome
              get() = OGL10nDealsWelcome

            public object OGL10nDeals

            public val OGL10n.Deals: OGL10nDeals
              get() = OGL10nDeals

            """.trimIndent(), kotlinFile.toString())
    }

    @Test
    fun `nested and parent definition will add operator fun for parent case`() {
        @Language("JSON")
        val json = """
            {
              "deals": "",
              "deals.title": "",
              "deals.subtitle": "",
              "deals.child": "Hallo %{username}",
              "deals.child.button": "",
              "deals.child.message": ""
            }
            """
        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val kotlinFile = L10nExtensionGenerator(listOf("android","ios", "wit"))
            .generateKotlinFile(jsonObject, "OGL10n", "com.ottogroup.appkit.l10n")
        assertEquals("""
            package com.ottogroup.appkit.l10n

            import kotlin.OptIn
            import kotlin.String
            import kotlin.experimental.ExperimentalObjCName
            import kotlin.native.ObjCName

            @OptIn(ExperimentalObjCName::class)
            @ObjCName("callAsFunction")
            public operator fun OGL10nDeals.invoke(): String = getString(key = "deals")

            @OptIn(ExperimentalObjCName::class)
            @ObjCName("callAsFunction")
            public operator fun OGL10nDealsChild.invoke(username: String): String = getString(key = "deals.child", "%{username}" to username)

            public val OGL10nDealsChild.Button: String
              get() = getString(key = "deals.child.button")

            public val OGL10nDealsChild.Message: String
              get() = getString(key = "deals.child.message")

            public object OGL10nDealsChild

            public val OGL10nDeals.Child: OGL10nDealsChild
              get() = OGL10nDealsChild

            public val OGL10nDeals.Subtitle: String
              get() = getString(key = "deals.subtitle")

            public val OGL10nDeals.Title: String
              get() = getString(key = "deals.title")

            public object OGL10nDeals

            public val OGL10n.Deals: OGL10nDeals
              get() = OGL10nDeals

            """.trimIndent(), kotlinFile.toString())
    }


}

