package com.ottogroup.appkit.l10n

import javax.inject.Inject
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.Project
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.model.ObjectFactory
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.kotlin.dsl.listProperty

public abstract class L10nPluginExtension(project: Project, objectFactory: ObjectFactory) {
    val projects: ListProperty<String> = objectFactory.listProperty<String>().convention(
        listOf(
            "BON",
            "BPX",
            "CRE",
            "HEI",
            "LAS",
            "LNL",
            "MAN",
            "ONL",
            "SAN",
            "SHE",
            "WIT",
            "YLFL",
            "YLFLNL",
            "YLFLSE"
        )
    )
    val platforms: ListProperty<String> = objectFactory.listProperty<String>().convention(
        listOf("android", "ios", "web")
    )
    abstract val accessLayer: NamedDomainObjectContainer<L10nAccessGenConfiguration>
    val outputDir: DirectoryProperty = objectFactory.directoryProperty().convention(
            project.layout.buildDirectory.dir("generated/source/l10n")
    )
}

public abstract class L10nAccessGenConfiguration
@Inject constructor(
    val name: String
) {
    abstract val className: Property<String>
    abstract val packageName: Property<String>
    abstract val inputFile: RegularFileProperty
}
