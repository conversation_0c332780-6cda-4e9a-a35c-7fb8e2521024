package com.ottogroup.appkit.l10n

import kotlin.reflect.KClass

internal data class Placeholder(
    val placeholder: String,
    val paramName: String,
    val type: PlaceholderType
)


internal sealed interface PlaceholderType {
    val patterns: List<Regex>
    val clazz: KClass<*>

    object String : PlaceholderType {
        override val patterns: List<Regex>
            get() = listOf(
                Regex("%\\{(\\w+)\\}"),  // %{username},
                Regex("%?\\{\\{(\\w+)\\}\\}"),  // %{{username}}, {{username}}
                Regex("(%\\d+\\\$s)"),  // %1$s
                Regex("(%s)")  // %s
            )
        override val clazz = kotlin.String::class
    }

    object Int : PlaceholderType {
        override val patterns: List<Regex>
            get() = listOf(
                Regex("(%\\d+\\\$d)"),  // %1$d
                Regex("(%d)") // %d

            )
        override val clazz = kotlin.Int::class

    }

    object Float : PlaceholderType {
        override val patterns: List<Regex>
            get() = listOf(
                Regex("(%\\d+\\\$(\\.\\d+)?f)"),  // %1$f, %1$.0f
                Regex("(%(\\.\\d+)?f)") // %f, %.2f
            )
        override val clazz = kotlin.Float::class

    }


    companion object {

        fun findAll(string: kotlin.String): List<Placeholder> {
            return listOf(
                String,
                Int,
                Float
            ).flatMap { type ->
                type.patterns.flatMap { pattern ->
                    pattern.findAll(string).map {
                        val paramName = it.groupValues[1]

                        Placeholder(it.value, paramName, type)
                    }
                }
            }
        }
    }
}
