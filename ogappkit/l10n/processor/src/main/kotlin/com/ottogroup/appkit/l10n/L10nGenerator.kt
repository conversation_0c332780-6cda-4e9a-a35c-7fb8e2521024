package com.ottogroup.appkit.l10n

import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.MemberName
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.PropertySpec
import com.squareup.kotlinpoet.TypeSpec
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

interface Generator {
    fun generateKotlinFile(json: JsonObject, className: String, packageName: String): FileSpec
}

internal class L10nGenerator(
    private val runtimeResolvedIdentifiers: List<String> = emptyList(),
    private val keyDelimiter: String = ".",
    private val resolverFun: String = "getString",
    private val resolverPackage: String = "com.ottogroup.appkit.l10n",
) : Generator {
    private val resolve = MemberName(resolverPackage, resolverFun)

    override fun generateKotlinFile(
        json: JsonObject,
        className: String,
        packageName: String
    ): FileSpec {
        val classBuilder = TypeSpec.objectBuilder(className)
        val cleaned = excludeRuntimevalidationKeys(json)
        val grouped = groupJsonKeys(cleaned)

        processJsonObject(grouped, "", classBuilder)

        return FileSpec.builder(packageName, className)
            .addType(classBuilder.build())
            .build()
    }

    private fun excludeRuntimevalidationKeys(json: JsonObject): JsonObject {
        val cleaned = mutableMapOf<String, JsonElement>()
        json.entries.sortedBy { it.key }.forEach { (key, value) ->
            if (!runtimeResolvedIdentifiers.any { key.endsWith(it) }) {
                cleaned[key] = value
            } else {
                val baseKey = key.split(keyDelimiter)
                    .dropLastWhile { runtimeResolvedIdentifiers.contains(it) }
                    .joinToString(keyDelimiter)
                if (!cleaned.contains(baseKey)) {
                    cleaned[baseKey] = value
                }
            }
        }
        return JsonObject(cleaned)
    }

    private fun groupJsonKeys(json: JsonObject): JsonObject {
        val groupedJson = mutableMapOf<String, JsonElement>()
        json.entries.forEach { (key, value) ->
            val keys = key.split(keyDelimiter)
            addToGroupedJson(keys, value, groupedJson)
        }
        return JsonObject(groupedJson)
    }

    private fun addToGroupedJson(
        keys: List<String>,
        value: JsonElement,
        currentObject: MutableMap<String, JsonElement>
    ) {
        val key = keys.first()
        if (keys.size == 1) {
            currentObject[key] = value
        } else {
            val nextObject =
                currentObject.getOrPut(key) { JsonObject(mutableMapOf()) }.jsonObjectOrNull?.toMutableMap()
                    ?: mutableMapOf("_OPERATOR" to currentObject[key]!!)
            addToGroupedJson(keys.drop(1), value, nextObject)
            currentObject[key] = JsonObject(nextObject)
        }
    }

    private val JsonElement.jsonObjectOrNull: JsonObject?
        get() = this as? JsonObject

    private fun processJsonObject(
        jsonObject: JsonObject,
        path: String,
        classBuilder: TypeSpec.Builder
    ) {
        jsonObject.entries.forEach { (key, value) ->
            when (value) {
                is JsonObject -> {
                    value.injectIntoClass(key, path, classBuilder)
                }

                is JsonArray -> {
                    value.injectIntoClass(key, path, classBuilder)
                }

                is JsonPrimitive -> {
                    value.injectIntoClass(key, path, classBuilder)
                }

                else -> {}
            }
        }
    }

    private fun JsonObject.injectIntoClass(
        name: String,
        path: String,
        classBuilder: TypeSpec.Builder
    ) {
        val nestedClassBuilder = TypeSpec.objectBuilder(name.capitalize())
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}$name"

        processJsonObject(this, nestedPath, nestedClassBuilder)
        classBuilder.addType(nestedClassBuilder.build())
    }

    private fun JsonArray.injectIntoClass(
        name: String,
        path: String,
        classBuilder: TypeSpec.Builder
    ) {
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}$name"

        classBuilder.addProperty(
            PropertySpec.builder(
                name.capitalize(),
                List::class.parameterizedBy(String::class)
            )
                .getter(
                    FunSpec.getterBuilder()
                        .addStatement("return %M(\"$nestedPath\")", resolve)
                        .build()
                )
                .build()
        )
    }

    private fun JsonPrimitive.injectIntoClass(
        name: String,
        path: String,
        classBuilder: TypeSpec.Builder
    ) {

        val placeholder: List<Placeholder> = extractPlaceholder().mapIndexed { index, placeholder ->
            if (placeholder.paramName.contains("%")) {
                Placeholder(placeholder.placeholder, "param$index", placeholder.type)
            } else {
                placeholder
            }
        }
        val resolveFunc = createResolveFunction(path, placeholder)
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}${name}"
        if (name == "_OPERATOR") {
            val resolverParams = placeholder.map { "\"${it.placeholder}\" to ${it.paramName}" }
                .joinToString(separator = ", ", prefix = ", ") { it }
            classBuilder.addFunction(FunSpec.builder("invoke")
                .addModifiers(KModifier.OPERATOR)
                .apply {
                    placeholder.forEachIndexed { _, (_, paramName, type) ->
                        addParameter(paramName, type.clazz)
                    }
                }
                .returns(String::class)
                .apply {
                    if (placeholder.isNotEmpty()) {
                        addStatement(
                            "return %M(%N = %S%L)",
                            resolve,
                            resolveFunc.parameters.first(),
                            nestedPath.removeSuffix("._OPERATOR"),
                            resolverParams
                        )
                    } else {
                        addStatement(
                            "return %M(%N = %S)",
                            resolve,
                            resolveFunc.parameters.first(),
                            nestedPath.removeSuffix("._OPERATOR")
                        )
                    }
                }
                .build())
        } else if (placeholder.isEmpty()) {
            classBuilder.addProperty(
                PropertySpec.builder(
                    name.capitalize(),
                    String::class
                )
                    .getter(
                        FunSpec.getterBuilder()
                            .addStatement(
                                "return %M(%N = %S)",
                                resolve,
                                resolveFunc.parameters.first(),
                                nestedPath
                            )
                            .build()
                    )
                    .build()
            )
        } else {
            val resolverParams = placeholder.map { "\"${it.placeholder}\" to ${it.paramName}" }
                .joinToString(separator = ", ", prefix = ", ") { it }
            classBuilder.addFunction(
                FunSpec.builder(name.capitalize())
                    .apply {
                        placeholder.forEachIndexed { _, (_, paramName, type) ->
                            addParameter(paramName, type.clazz)
                        }
                    }
                    .returns(String::class)
                    .addStatement(
                        "return %M(%N = %S%L)",
                        resolve,
                        resolveFunc.parameters.first(),
                        nestedPath,
                        resolverParams
                    )
                    .build()
            )
        }


    }

    private fun createResolveFunction(path: String, placeholder: List<Placeholder>): FunSpec {
        return FunSpec.builder("resolve")
            .addParameter("key", String::class)
            .apply {
                placeholder.forEach {
                    addParameter(it.paramName, it.type.clazz)
                }
            }
            .build()
    }

    private fun JsonPrimitive.extractPlaceholder(): List<Placeholder> {
        val findType = PlaceholderType.findAll(content)
        return findType
    }
}

