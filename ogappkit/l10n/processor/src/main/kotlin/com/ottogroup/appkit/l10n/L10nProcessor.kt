package com.ottogroup.appkit.l10n


import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.tasks.TaskAction
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.OutputDirectory
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject

abstract class GenerateTranslationAccessLayerTask : DefaultTask() {
    @get:InputFile
    abstract val inputFile: RegularFileProperty

    @get:OutputDirectory
    abstract val outputDir: DirectoryProperty

    @get:Input
    abstract val className: Property<String>

    @get:Input
    abstract val projects: ListProperty<String>

    @get:Input
    abstract val platforms: ListProperty<String>

    @get:Input
    abstract val packageName: Property<String>

    @get:Input
    abstract val asExtension: Property<Boolean>

    @get:Input
    abstract val reduceEmptyNesting: Property<Boolean>

    @TaskAction
    fun generate() {
        val json = Json.decodeFromString<JsonObject>(inputFile.get().asFile.readText())
        val runtimeIdentifiers = (platforms.get() + projects.get())

        val generator =
            if (asExtension.get()) {
                L10nExtensionGenerator(
                    runtimeResolvedIdentifiers = runtimeIdentifiers,
                    reduceNesting = reduceEmptyNesting.get()
                )
            } else {
                L10nGenerator(
                    runtimeResolvedIdentifiers = runtimeIdentifiers,
                )
            }

        generator.generateKotlinFile(json, className.get(), packageName.get())
            .writeTo(outputDir.get().asFile)
        project.logger.info("Generated Access layer: ${outputDir.get().asFile}")
    }

}
