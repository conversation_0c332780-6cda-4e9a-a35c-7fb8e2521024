package com.ottogroup.appkit.l10n

import com.squareup.kotlinpoet.AnnotationSpec
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.MemberName
import com.squareup.kotlinpoet.PropertySpec
import com.squareup.kotlinpoet.TypeName
import com.squareup.kotlinpoet.TypeSpec
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

internal class L10nExtensionGenerator(
    private val runtimeResolvedIdentifiers: List<String> = emptyList(),
    private val keyDelimiter: String = ".",
    private val resolverFun: String = "getString",
    private val resolverPackage: String = "com.ottogroup.appkit.l10n",
    private val extensionClass: String = "OGL10n",
    private val extensionPackage: String = "com.ottogroup.appkit.l10n",
    private val reduceNesting: Boolean = false,
) : Generator {
    private val resolve = MemberName(resolverPackage, resolverFun)

    override fun generateKotlinFile(
        json: JsonObject,
        className: String,
        packageName: String
    ): FileSpec {
        val cleaned = excludeRuntimevalidationKeys(json)
        val grouped = groupJsonKeys(cleaned)
        val reduced = if (reduceNesting) reduceNesting(grouped) else grouped
        val fileBuilder = FileSpec.builder(packageName, className)
        val rootClassName = ClassName(extensionPackage, extensionClass)
        processJsonObjectOnFileLevel(reduced, "", fileBuilder, rootClassName)

        return fileBuilder.build()
    }

    private fun excludeRuntimevalidationKeys(json: JsonObject): JsonObject {
        val cleaned = mutableMapOf<String, JsonElement>()
        json.entries.sortedBy { it.key }.forEach { (key, value) ->
            if (!runtimeResolvedIdentifiers.any { key.endsWith(it) }) {
                cleaned[key] = value
            } else {
                val baseKey = key.split(keyDelimiter)
                    .dropLastWhile { runtimeResolvedIdentifiers.contains(it) }
                    .joinToString(keyDelimiter)
                if (!cleaned.contains(baseKey)) {
                    cleaned[baseKey] = value
                }
            }
        }
        return JsonObject(cleaned)
    }

    private fun groupJsonKeys(json: JsonObject): JsonObject {
        val groupedJson = mutableMapOf<String, JsonElement>()
        json.entries.forEach { (key, value) ->
            val keys = key.split(keyDelimiter)
            addToGroupedJson(keys, value, groupedJson)
        }
        return JsonObject(groupedJson)
    }

    private fun addToGroupedJson(
        keys: List<String>,
        value: JsonElement,
        currentObject: MutableMap<String, JsonElement>
    ) {
        val key = keys.first()
        if (keys.size == 1) {
            currentObject[key] = value
        } else {
            val nextObject =
                currentObject.getOrPut(key) { JsonObject(mutableMapOf()) }.jsonObjectOrNull?.toMutableMap()
                    ?: mutableMapOf("_OPERATOR" to currentObject[key]!!)
            addToGroupedJson(keys.drop(1), value, nextObject)
            currentObject[key] = JsonObject(nextObject)
        }
    }

    private val JsonElement.jsonObjectOrNull: JsonObject?
        get() = this as? JsonObject

    private fun reduceNesting(json: JsonObject): JsonObject {
        val reducedJson = mutableMapOf<String, JsonElement>()
        json.entries.forEach { (key, value) ->
            if (value is JsonObject) {
                val reducedChild = reduceNesting(value)
                if (reducedChild.size == 1) {
                    val nestedKey = reducedChild.entries.first().key
                    val nestedValue = reducedChild.entries.first().value
                    reducedJson.put("$key$keyDelimiter$nestedKey", nestedValue)
                } else {
                    reducedJson.put(key, reducedChild)
                }
            } else {
                reducedJson.put(key, value)
            }
        }
        return JsonObject(reducedJson)
    }

    private fun processJsonObjectOnFileLevel(
        jsonObject: JsonObject,
        path: String,
        fileBuilder: FileSpec.Builder,
        parentClassName: ClassName
    ) {
        jsonObject.entries.forEach { (key, value) ->
            when (value) {
                is JsonObject -> {
                    value.injectIntoFile(key, path, fileBuilder, parentClassName)
                }

                is JsonArray -> {
                    value.injectIntoFile(key, path, fileBuilder, parentClassName)
                }

                is JsonPrimitive -> {
                    value.injectIntoFile(key, path, fileBuilder, parentClassName)
                }

                else -> {}
            }
        }
    }

    private fun JsonObject.injectIntoFile(
        key: String,
        path: String,
        fileBuilder: FileSpec.Builder,
        parentClassName: ClassName
    ) {
        val name = key.camelCaseName()
        val currentClassName =
            ClassName(extensionPackage, "${parentClassName.simpleName}${name}")
        val currentClass = TypeSpec.objectBuilder(currentClassName)
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}$key"

        processJsonObjectOnFileLevel(this, nestedPath, fileBuilder, currentClassName)

        fileBuilder.addType(currentClass.build())
        val classAccess = PropertySpec.builder(
            name, currentClassName,
            KModifier.PUBLIC,
        )
            .receiver(parentClassName)
            .getter(
                FunSpec.getterBuilder()
                    .addStatement("return %T", currentClassName)
                    .build()
            )
            .build()
        fileBuilder.addProperty(classAccess)

    }

    private fun String.capitalize(): String {
        return this.replaceFirstChar { it.uppercase() }
    }

    private fun String.camelCaseName(): String {
        return this.split(keyDelimiter).joinToString("") { it.capitalize() }
    }

    private fun JsonArray.injectIntoFile(
        key: String,
        path: String,
        fileBuilder: FileSpec.Builder,
        parentClassName: ClassName
    ) {
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}$key"

        fileBuilder.addProperty(
            buildProperty(key.camelCaseName(), nestedPath, parentClassName).build()
        )

    }

    private fun JsonArray.buildProperty(
        name: String,
        path: String,
        receiver: TypeName? = null
    ): PropertySpec.Builder {
        return PropertySpec.builder(
            name,
            String::class,
            KModifier.PUBLIC,
        ).apply {
            if (receiver != null) {
                this.receiver(receiver)
            }
        }
            .getter(resolveArrayFunction(path).build())
    }

    private fun resolveArrayFunction(path: String): FunSpec.Builder {
        return FunSpec.getterBuilder()
            .addStatement("return %M(\"$path\")", resolve)
    }

    private fun JsonPrimitive.injectIntoFile(
        key: String,
        path: String,
        fileBuilder: FileSpec.Builder,
        parentClassName: ClassName
    ) {
        val nestedPath = "$path${if (path.isNotEmpty()) keyDelimiter else ""}$key"
        if (content.hasPlaceholders()) {
            fileBuilder.addFunction(
                buildFunction(key.camelCaseName(), nestedPath, parentClassName).build()
            )

        } else if (key == "_OPERATOR") {

            fileBuilder.addFunction(
                buildFunction(key, nestedPath, parentClassName).build()
            )
        } else {

            fileBuilder.addProperty(
                buildProperty(key.camelCaseName(), nestedPath, parentClassName).build()
            )
        }

    }

    private fun String.hasPlaceholders(): Boolean {
        return PlaceholderType.findAll(this).isNotEmpty()
    }

    private fun JsonPrimitive.buildFunction(
        name: String,
        path: String,
        receiver: TypeName? = null
    ): FunSpec.Builder {
        val placeholder: List<Placeholder> = extractPlaceholder().mapIndexed { index, placeholder ->
            if (placeholder.paramName.contains("%")) {
                Placeholder(placeholder.placeholder, "param$index", placeholder.type)
            } else {
                placeholder
            }
        }
        val resolveFunc = createResolveFunction(path, placeholder)

        return if (name == "_OPERATOR") {
            val resolverParams =
                placeholder.map { "\"${it.placeholder.replace("$", "\\\$")}\" to ${it.paramName}" }
                    .joinToString(separator = ", ", prefix = ", ") { it }
            FunSpec.builder("invoke")
                .addModifiers(KModifier.OPERATOR)
                .addAnnotation(
                    AnnotationSpec.builder(ClassName("kotlin", "OptIn"))
                        .addMember(
                            "%T::class",
                            ClassName("kotlin.experimental", "ExperimentalObjCName")
                        )
                        .build()
                )
                .addAnnotation(
                    AnnotationSpec.builder(ClassName("kotlin.native", "ObjCName"))
                        .addMember("%S", "callAsFunction").build()
                )
                .apply {
                    placeholder.forEachIndexed { _, (_, paramName, type) ->
                        addParameter(paramName, type.clazz)
                    }
                }
                .apply {
                    if (receiver != null) {
                        this.receiver(receiver)
                    }
                }
                .returns(String::class)
                .apply {
                    if (placeholder.isNotEmpty()) {
                        addStatement(
                            "return %M(%N = %S%L)",
                            resolve,
                            resolveFunc.parameters.first(),
                            path.removeSuffix("._OPERATOR"),
                            resolverParams
                        )
                    } else {
                        addStatement(
                            "return %M(%N = %S)",
                            resolve,
                            resolveFunc.parameters.first(),
                            path.removeSuffix("._OPERATOR")
                        )
                    }
                }
        } else {
            val resolverParams =
                placeholder.map { "\"${it.placeholder.replace("$", "\\\$")}\" to ${it.paramName}" }
                    .joinToString(separator = ", ", prefix = ", ") { it }
            FunSpec.builder(name)
                .apply {
                    placeholder.forEachIndexed { _, (_, paramName, type) ->
                        addParameter(paramName, type.clazz)
                    }
                }
                .apply {
                    if (receiver != null) {
                        this.receiver(receiver)
                    }
                }
                .returns(String::class)
                .addStatement(
                    "return %M(%N = %S%L)",
                    resolve,
                    resolveFunc.parameters.first(),
                    path,
                    resolverParams
                )
        }
    }

    private fun JsonPrimitive.buildProperty(
        name: String,
        path: String,
        receiver: TypeName? = null

    ): PropertySpec.Builder {

        val placeholder: List<Placeholder> = extractPlaceholder().mapIndexed { index, placeholder ->
            if (placeholder.paramName.contains("%")) {
                Placeholder(placeholder.placeholder, "param$index", placeholder.type)
            } else {
                placeholder
            }
        }
        val resolveFunc = createResolveFunction(path, placeholder)

        return PropertySpec.builder(
            name.capitalize(),
            String::class
        )
            .getter(
                FunSpec.getterBuilder()
                    .addStatement(
                        "return %M(%N = %S)",
                        resolve,
                        resolveFunc.parameters.first(),
                        path
                    )
                    .build()
            )
            .apply {
                if (receiver != null) {
                    this.receiver(receiver)
                }
            }
    }

    private fun createResolveFunction(path: String, placeholder: List<Placeholder>): FunSpec {
        return FunSpec.builder(resolve)
            .addParameter("key", String::class)
            .apply {
                placeholder.forEach {
                    addParameter(it.paramName, it.type.clazz)
                }
            }
            .build()
    }

    private fun JsonPrimitive.extractPlaceholder(): Set<Placeholder> {
        val findType = PlaceholderType.findAll(content)
        return findType.toSet()
    }
}
