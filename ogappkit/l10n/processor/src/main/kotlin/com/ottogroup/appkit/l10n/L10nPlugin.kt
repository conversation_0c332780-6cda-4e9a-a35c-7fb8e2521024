package com.ottogroup.appkit.l10n

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.create
import org.jetbrains.kotlin.gradle.dsl.kotlinExtension

public class OGL10nPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        val extension = project.extensions.create<L10nPluginExtension>("OGL10n")
        val generateAllTask = project.tasks.register("generateAccessLayer")
        generateAllTask.configure {
            group = "ogappkit"
            description = "Generates the access layer for the translations"
        }

        extension.accessLayer.all {
            val config = this
            val generateTask = project.tasks.register(
                "generateAccessLayerFor${name}",
                GenerateTranslationAccessLayerTask::class.java
            ) {
                group = "ogappkit"
                inputFile.set(config.inputFile)
                outputDir.set(extension.outputDir)
                className.set(config.className)
                projects.set(extension.projects)
                platforms.set(extension.platforms)
                packageName.set(config.packageName)
                asExtension.set(true)
                reduceEmptyNesting.set(false)
            }

            project.kotlinExtension.sourceSets.named("commonMain") {
                this.kotlin.srcDir(generateTask)
            }

            generateAllTask.configure {
                dependsOn(generateTask)
            }
        }
    }
}
