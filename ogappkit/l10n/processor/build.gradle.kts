plugins {
    `kotlin-dsl`
    `java-gradle-plugin`
}

gradlePlugin {
    plugins {
        create("translationGeneratorPlugin") {
            id = "ogAppKit.l10n"
            implementationClass = "com.ottogroup.appkit.l10n.OGL10nPlugin"
        }
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation(kotlin("stdlib"))
    implementation(libs.kotlinx.serialization)
    implementation(libs.kotlinpoet)
    implementation(libs.kotlinGradlePlugin)
    testImplementation(libs.kotlinTestJUnit)
    testImplementation(libs.turbine)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(kotlin("test-common"))
    testImplementation(kotlin("test-annotations-common"))

}
