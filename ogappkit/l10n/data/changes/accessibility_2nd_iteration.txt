# 2025-05-21 16:52:14
+ search.shortcuts.accountOrders "My account balance"
# 2025-05-21 16:53:47
± search.shortcuts.accountOrders search.shortcuts.accountBalance
# 2025-05-21 16:54:29
+ search.shortcuts.accountOrders "My Orders"
# 2025-05-21 16:55:46
+ search.shortcuts.accountAddress "My addresses"
# 2025-05-21 17:00:52
+ search.shortcuts.accountNewIn "News"
# 2025-05-21 17:02:09
+ search.shortcuts.sale "Sale"
# 2025-05-21 17:03:23
+ search.shortcuts.account "My account"
# 2025-05-21 17:06:49
+ search.shortcuts.service "Service"
# 2025-05-21 17:09:02
- search.shortcuts.account
# 2025-05-21 17:09:57
- search.shortcuts.accountNewIn
# 2025-05-22 09:22:18
- assortment.services.title
# 2025-05-22 09:22:35
± navigation.assortment.section.moreServices.title assortment.services.title
# 2025-05-22 16:59:26
~ onboarding.welcome.buttonCountryChoose.accessibility "Choose your region"
# 2025-05-23 11:42:39
~ general.segmentedControl.selectionPrefix.accessibility "Selected %{element}"
# 2025-05-23 11:42:50
- bfg.segmentedControl.selectionPrefix.accessibility
# 2025-05-26 11:34:29
~ assortment.tabList.activeTab.accessibility "Active %{tabName} - Tab %{tabNumber} of %{tabCount}"
# 2025-05-26 11:55:42
+ navigation.forMe.title.SHE "sheego FOR ME"
# 2025-05-26 12:00:41
+ search.shortcuts.accountOverview "Account overview"
# 2025-05-26 12:50:16
+ account.settings.sectionTitle "Settings"
# 2025-05-26 13:56:39
+ assortment.newsletter.title "Newsletter"
# 2025-05-26 13:56:58
+ assortment.outlet.title "Outlet"

# 2025-05-27 09:05:09
~ assortment.tabList.inactiveTab.accessibility "%{tabName} - Tab %{tabNumber} of %{tabCount} Inactive"
# 2025-05-27 11:54:08
- deals.dealVariant.timer.title
# 2025-05-27 11:04:13
~ assortment.tabList.inactiveTab.accessibility "%{tabName} - Tab %{tabNumber} of %{tabCount}"
# 2025-05-29 13:15:03
~ assortment.tabList.inactiveTab.accessibility "%{tabName} - Tab %{tabNumber} von %{tabCount} – Zum Aktivieren doppeltippen"