# 2025-04-22 14:19:49
+ tabBar.tabItem.accessibility.plural "articles"
# 2025-04-22 14:21:25
+ tabBar.tabItem.accessibility.singular "article"
# 2025-04-24 11:33:49
+ search.history.deleted.accessibility "Search history deleted"
# 2025-04-24 12:19:09
+ assortment.list.items.accessibility "%{amount} items\n"
# 2025-04-24 12:46:03
~ catalogScanner.scan.title "The catalog page must be placed within the specified frame below"
# 2025-04-24 12:46:42
+ catalogueScanner.scan.description "If the scan was successful, results will be displayed automatically\n"
# 2025-04-25 10:23:07
~ assortment.categoriesList.item.accessibility "%{itemName} - %{itemNumber} of %{itemCount} - in the categories list"
# 2025-04-29 14:23:20
+ search.suggestions.emptyText "Try a different search term or check your spelling"
# 2025-04-29 14:26:43
+ search.suggestions.emptyTitle "No results for %{searchTerm}"
# 2025-04-29 14:32:00
+ search.suggestions.keyword.accessibility "Search suggestion"
# 2025-04-29 14:32:29
+ search.suggestions.category.accessibility "Category suggestion"
# 2025-04-29 14:32:49
+ search.suggestions.product.accessibility "Product suggestion"
# 2025-04-29 14:41:07
+ search.suggestions.listTitle.accessibility "Search suggestions"
# 2025-04-29 14:41:39
+ search.suggestions.header.accessibility "Suggestions"
# 2025-04-29 14:42:06
+ search.shortcuts.listTitle.accessibility "Account shortcuts"
# 2025-04-29 14:42:59
+ search.suggestions.results.accessibility "%{amount} results available"
# 2025-04-29 14:43:26
+ search.suggestions.noResults.accessibility "No results available"
# 2025-04-29 16:17:53
+ onboarding.welcome.buttonCountryChoose.accessibility "Choose region"
# 2025-04-29 16:19:56
+ onboarding.welcome.bulletPointListTitle.accessibility "List with advantages"
# 2025-04-29 16:21:41
+ pushPromotion.optIn.pageTitle.accessibility "Overlay to accept push notifications"
# 2025-04-29 16:22:46
+ general.back "Back"
# 2025-04-29 16:23:40
+ pushPromotion.optIn.closeButton.accessibility "Close"
# 2025-04-29 16:24:02
+ pushPromotion.optIn.uspListTitle.accessibility "List with advantages"
# 2025-04-29 16:35:12
+ inbox.overview.message.read.contentDescription "Read message"
# 2025-04-29 16:40:27
+ inbox.overview.message.unread.contentDescription "Unread message"
# 2025-04-29 16:41:00
+ inbox.message.deleted.accessibility "Message deleted"
# 2025-04-29 16:42:39
~ search.clear.description "Clear current search"
# 2025-04-29 16:47:46
+ assortment.listTitle.accessibility "Categories list"
# 2025-04-29 16:48:22
+ assortment.categories.accessibility "Categories"
# 2025-04-29 16:49:33
+ assortment.services.accessibility "Services"
# 2025-04-29 17:03:10
+ countrychange.regionChanged.accessibility "Region changed"
# 2025-04-29 17:03:33
+ tabBar.loggedIn.accessibility "Logged in"
# 2025-04-29 17:06:01
+ login.successful.accessibility "Login successful"
# 2025-04-29 17:16:54
+ logout.successful.accessibility "Logout successful"
# 2025-04-29 17:19:21
+ error.screen.accessibility "Error screen"
# 2025-04-29 17:19:46
+ error.screen.reload.accessibility "Screen correctly reloaded"
# 2025-04-29 17:32:53
+ shopfinder.overview.storesList.accessibility "Stores list"
# 2025-04-29 17:35:54
+ shopfinder.listView.accessibility "List view"
# 2025-04-29 17:36:26
+ shopfinder.mapView.accessibility "Map view"
# 2025-04-29 17:36:57
+ shopfinder.overview.storesSuggestionsList.accessibility "Store suggestions list"
# 2025-04-29 17:38:36
+ shopfinder.overview.storesSuggestionsItem.accessibility "Store suggestion"
# 2025-04-29 17:50:22
+ shopfinder.overview.distance.accessibility "away"
# 2025-04-29 17:50:58
+ shopfinder.searchHint.accessibility "Postleitzahl / Ort oder Adresse"
# 2025-04-29 17:59:56
+ shopfinder.overview.floatingButtonList.accessibility "Sort stores by user location"
# 2025-04-29 18:00:39
+ shopfinder.overview.floatingButtonMap.accessibility "Focus user on map"
# 2025-04-29 18:01:49
+ shopfinder.map.locationIcon.accessibility "Your current location"
# 2025-04-29 18:01:59
+ shopfinder.map.zoomIn.accessibility "Zoom in map"
# 2025-04-29 18:02:16
+ shopfinder.map.zoomOut.accessibility "Zoom out map"
# 2025-04-29 18:02:36
+ shopfinder.map.zoomLevel.accessibility "Zoom level: %d"
# 2025-04-29 18:03:02
+ shopfinder.map.accessibility "Map with store locations"
# 2025-04-29 18:03:16
+ shopfinder.map.detailsHint.accessibility "Double tap to see more details"
# 2025-04-29 18:03:29
+ shopfinder.detail.title.accessibility "Store information"
# 2025-04-29 18:04:18
~ shopfinder.map.zoomLevel.accessibility "Zoom level: %{amount}"
# 2025-04-29 18:07:43
+ bfg.moreInfo.accessibility "Open a popup with more information"
# 2025-04-29 18:08:08
+ bfg.page.title.accessibility "Bra fitting guide Step %{step} of 4"
# 2025-04-29 18:08:27
+ bfg.result.title.accessibility "Bra fitting guide result"
# 2025-04-29 18:08:43
+ bfg.fitHelper.underbust.accessibility "Helper overlay for underbust band fit"
# 2025-04-29 18:09:00
+ bfg.fitHelper.cupSize.accessibility "Helper overlay for cup fit"
# 2025-04-29 18:09:12
+ bfg.segmentedControl.selectionPrefix.accessibility "Selected category"
# 2025-04-29 18:09:25
+ bfg.fitHelper.resultInfo.tip.accessibility "Tip"
# 2025-04-29 18:09:41
+ bfg.fitHelper.resultInfo.title.accessibility "Helper overlay for result info"
# 2025-05-07 12:17:16
+ countryList.regionChangeHint.accessibility "On tap, the region will be changed and you will be redirected to home screen"
# 2025-05-07 12:19:43
+ general.segmentedControl.buttonLabel.accessibility "button"
# 2025-05-07 12:21:35
+ shopfinder.details.storeType.partner "Partner store"
# 2025-05-07 12:23:16
+ general.segmentedControl.selectionPrefix.accessibility "Selected category"
# 2025-05-07 12:23:29
- bfg.segmentedControl.selectionPrefix.accessibility
# 2025-05-07 11:55:48
+ general.back "Back"
# 2025-05-07 12:03:56
+ general.doubleTabToEdit.accessibility "Double tap to edit"
# 2025-05-07 12:09:51
+ inbox.button.accessibility "News"
# 2025-05-07 12:10:13
+ inbox.button.unreadCount.accessibility "%@ - %d new notifications"
# 2025-05-07 12:11:28
+ search.searchbar.clear.button.accessibility "Delete Text"
# 2025-05-07 12:14:35
+ search.suggestions.emptyTitle "No Results for \"%s\""
# 2025-05-07 12:15:36
+ search.suggestions.results.accessibility "%d results available"
# 2025-05-07 12:20:26
+ bfg.slider.instructions.accessibility "Swipe up or down with one finger to adjust the value"
# 2025-05-07 12:21:07
+ bfg.slider.valueChanged.accessibility "New value: %@"
# 2025-05-07 12:21:31
+ bfg.moreInfo.accessibility "Tap for more information"
# 2025-05-07 12:21:45
+ bfg.navigation.back.accessibility "back"
# 2025-05-07 12:22:01
+ bfg.loading.isLoaded.accessibility "Is loaded"
# 2025-05-07 12:23:20
+ bfg.result.title.accessibility "Helper for underbust"
# 2025-05-07 12:23:34
+ bfg.fitHelper.cupSize.accessibility "Helper for cupSize"
# 2025-05-07 12:23:51
+ bfg.fitHelper.resultInfo.title.accessibility "Helper for result info"
# 2025-05-07 12:24:06
+ bfg.fitHelper.resultInfo.tip.accessibility "Tipp %@"
# 2025-05-07 12:24:24
+ bfg.page.title.accessibility "Bra fitting guide step %@ of %@"
# 2025-05-07 12:24:36
+ bfg.segmentedControl.selectionPrefix.accessibility "Selected"
# 2025-05-07 14:21:35
± search.suggestion.product.description search.suggestion.product.image.description.accesibility

# 2025-05-08 10:35:13
~ bfg.sizeSelection.underbustSize "My Underbust Size"
# 2025-05-08 13:57:15
~ shopfinder.openHours.accessibility "%{from} to %{to} o'clock"
# 2025-05-08 13:58:07
~ general.segmentedControl.selectionPosition.accessibility "Selection %{item} of %{items}"
# 2025-05-08 13:58:40
~ shopfinder.details.storeType.company "%{company} Store"
# 2025-05-08 12:44:57
+ bfg.segmentedControl.underbust.accessibility "the underbust band"
# 2025-05-08 12:47:06
+ toast.hint.accessibility.android "Toast message will be shown"
# 2025-05-08 12:47:23
+ catalogScanner.scan.title "The catalog page must be placed within the specified frame below"
# 2025-05-08 12:47:36
+ catalogScanner.scan.description "If the scan was successful, results will be displayed automatically"
# 2025-05-08 12:47:49
+ videoPlayer.playButton.accessibility "Tap to start the video"

# 2025-05-08 14:41:26
~ inbox.button.unreadCount.accessibility "%{title} - %{value} new notifications"
# 2025-05-08 14:43:08
~ inbox.button.unreadCount.accessibility "%{title} - %{value} new notifications"
# 2025-05-08 14:43:31
~ search.suggestions.emptyTitle "No Results for "%{text}""
# 2025-05-08 14:43:50
~ search.suggestions.results.accessibility "%{value} results available"
# 2025-05-08 14:44:04
~ bfg.slider.valueChanged.accessibility "New value: %{newValue}"
# 2025-05-08 14:44:17
~ bfg.fitHelper.resultInfo.tip.accessibility "Tipp %{tipNumber}"
# 2025-05-08 14:44:34
~ bfg.page.title.accessibility "Bra fitting guide step %{currentStep} of %{totalStep}"

# 2025-05-09 14:06:15
± ftue.pushOptIn.firstArgument pushPromotion.optIn.firstArgument
# 2025-05-09 14:06:35
± ftue.pushOptIn.footnote pushPromotion.optIn.footnote

# 2025-05-12 14:54:47
+ error.view.appUpdate.title "App update required!\n"
# 2025-05-12 14:55:45
+ error.view.noInternet.title "The page could not be loaded!"
# 2025-05-12 14:57:00
+ error.view.generic.title "Oops!"
# 2025-05-12 14:58:00
+ error.view.emptyList.title "No notifications yet"
# 2025-05-12 14:58:48
+ general.back "Back"
# 2025-05-12 15:00:33
+ error.screen.reload.accessibility "Screen correctly reloaded"
# 2025-05-12 15:01:10
+ inbox.message.deleted.accessibility "Message deleted"
# 2025-05-12 15:01:26
+ inbox.list.editing.delete "Delete\n"

# 2025-05-12 13:57:13
+ general.motionSetting.toggle.title "Use mobile shake funcationality"
# 2025-05-12 13:57:53
+ general.motionSetting.sheet.title "Issues with motion activation?"
# 2025-05-12 13:58:45
+ general.motionSetting.sheet.copy "You can permanently disable the shake function if you have problems."

# 2025-05-13 07:27:32
- inbox.list.editing.delete
# 2025-05-13 07:34:18
± error.screen.accessibility error.generic.accessibility
# 2025-05-13 07:34:33
- error.view.generic.title
# 2025-05-13 07:34:50
- error.view.appUpdate.title
# 2025-05-13 07:35:13
- error.view.noInternet.title
# 2025-05-13 07:38:21
- error.view.emptyList.title
# 2025-05-13 09:01:02
± error.screen.reload.accessibility error.generic.reload.accessibility
# 2025-05-13 09:30:55
± assortment.list.items.accessibility general.list.items.accessibility

# 2025-05-13 10:49:12
- bfg.navigation.back.accessibility
# 2025-05-13 10:49:57
- checkout.quitDialog.decline
# 2025-05-13 10:52:23
- pushPromotion.optIn.closeButton.accessibility

# 2025-05-13 13:27:39
+ deals.revealDeal.shake
# 2025-05-13 13:29:22
+ deals.revealDeal.scratch
# 2025-05-13 13:41:06
~ general.segmentedControl.selectionPosition.accessibility "Selection %{itemPosition} of %{totalItems}"
# 2025-05-13 14:24:21
+ bfg.segmentedControl.cupSize.accessibility "the cup size"
# 2025-05-13 16:29:55
± shopfinder.detail.title.accessibility shopFinder.detail.title.accessibility
# 2025-05-13 16:37:47
± shopfinder.details.storeType.company shopFinder.details.storeType.company
# 2025-05-13 16:38:05
± shopfinder.details.storeType.partner shopFinder.details.storeType.partner
# 2025-05-13 16:38:19
± shopfinder.listView.accessibility shopFinder.listView.accessibility
# 2025-05-13 16:38:30
± shopfinder.map.accessibility shopFinder.map.accessibility
# 2025-05-13 16:38:40
± shopfinder.map.detailsHint.accessibility shopFinder.map.detailsHint.accessibility
# 2025-05-13 16:38:51
± shopfinder.map.locationIcon.accessibility shopFinder.map.locationIcon.accessibility
# 2025-05-13 16:39:01
± shopfinder.map.zoomIn.accessibility shopFinder.map.zoomIn.accessibility
# 2025-05-13 16:39:11
± shopfinder.map.zoomLevel.accessibility shopFinder.map.zoomLevel.accessibility
# 2025-05-13 16:39:21
± shopfinder.map.zoomOut.accessibility shopFinder.map.zoomOut.accessibility
# 2025-05-13 16:39:32
± shopfinder.mapView.accessibility shopFinder.mapView.accessibility
# 2025-05-13 16:39:41
± shopfinder.openHours.accessibility shopFinder.openHours.accessibility
# 2025-05-13 16:39:51
± shopfinder.overview.distance.accessibility shopFinder.overview.distance.accessibility
# 2025-05-13 16:40:02
± shopfinder.overview.floatingButtonList.accessibility shopFinder.overview.floatingButtonList.accessibility
# 2025-05-13 16:40:11
± shopfinder.overview.floatingButtonMap.accessibility shopFinder.overview.floatingButtonMap.accessibility
# 2025-05-13 16:40:27
± shopfinder.overview.storesList.accessibility shopFinder.overview.storesList.accessibility
# 2025-05-13 16:40:40
± shopfinder.overview.storesSuggestionsItem.accessibility shopFinder.overview.storesSuggestionsItem.accessibility
# 2025-05-13 16:40:50
± shopfinder.overview.storesSuggestionsList.accessibility shopFinder.overview.storesSuggestionsList.accessibility
# 2025-05-13 16:40:59
± shopfinder.searchHint.accessibility shopFinder.searchHint.accessibility
# 2025-05-13 16:41:13
± storeFinder.alert.locationNotAuthorize.cancel shopFinder.alert.locationNotAuthorize.cancel
# 2025-05-13 16:41:31
± storeFinder.alert.locationNotAuthorize.default shopFinder.alert.locationNotAuthorize.default
# 2025-05-13 16:41:49
± storeFinder.alert.locationNotAuthorize.message shopFinder.alert.locationNotAuthorize.message
# 2025-05-13 16:42:02
± storeFinder.alert.locationNotAuthorize.title shopFinder.alert.locationNotAuthorize.title
# 2025-05-13 16:42:22
± storeFinder.cellDetailInfo.button.title shopFinder.cellDetailInfo.button.title
# 2025-05-13 16:42:33
± storeFinder.cellDetailInfo.header.text shopFinder.cellDetailInfo.header.text
# 2025-05-13 16:42:47
± storeFinder.openingHours.today shopFinder.openingHours.today
# 2025-05-13 21:40:57
± search.searchbar.clear.button.accessibility search.searchBar.clear.button.accessibility
# 2025-05-13 21:41:57
± assortment.tablist.activeTab.accessibility assortment.tabList.activeTab.accessibility
# 2025-05-13 21:42:18
± assortment.tablist.inactiveTab.accessibility assortment.tabList.inactiveTab.accessibility
# 2025-05-16 16:03:07
+ navigation.assortment.entry.bfg.title "BH-Berater"
# 2025-05-20 11:59:01
+ navigation.rate.title "Rate the app"
# 2025-05-20 12:24:11
± navigation.rate.title account.rate.title
# 2025-05-20 12:28:34
+ assortment.services.title "Other services"
# 2025-05-20 12:31:06
+ assortment.orderForm.title "Order form"
# 2025-05-22 06:53:53
+ navigation.static.section.title "Weitere Services"
# 2025-05-22 06:59:31
± navigation.static.section.title navigation.assortment.section.moreServices.title
