{"AppBuild": "Az alkalmazás fejlesztése", "AppVersion": "App Version", "NSCameraUsageDescription": "Igazold vissza a kamerához való hozzáférést, hogy be tudd szkennelni a termékeket.", "NSLocationAlwaysAndWhenInUseUsageDescription": "A helymeghatározás a jobb felhasználói élmény eléréséhez szükséges.", "NSLocationAlwaysUsageDescription": "A helymeghatározás a jobb felhasználói élmény eléréséhez szükséges.", "NSLocationUsageDescription": "A helymeghatározás a jobb felhasználói élmény eléréséhez szükséges.", "NSMicrophoneUsageDescription": "Ha bekapcsolod a beszédfelismerést, akkor a keresett kulcsszavakat be is mondhatod.", "NSPhotoLibraryAddUsageDescription": "A képernyőkép lementéséhez igazold vissza a multimédiás könyvtárhoz való hozzáférést.", "NSPhotoLibraryUsageDescription": "Az alkalmazás fotókat és videókat tárol a fotókönyvtárába.", "NSSpeechRecognitionUsageDescription": "A beszédfelismerés a hangod felismerésére has<PERSON>.", "NSUserTrackingUsageDescription": "Ennek köszönhetően az érdeklődésednek megfelelő reklámok jelennek meg neked. Bármikor megváltoztathatod a beállításokat.", "account.customerAccountEntry.subtitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, számlák", "account.customerAccountEntry.title": "Fiókom", "account.customerAccountEntry.title.SHE.ios": "Számlám", "account.loginButton.title": "Jelentkezz be", "account.loginButton.title.BON.ios": "Belépés", "account.loginButton.title.BPX.ios": "Belépés", "account.logout": "Kilépés", "account.rate.title": "Alkalmazás értékelése", "account.settings.sectionTitle": "Beállítások", "account.settings.title": "Az alkalmazás beállításai", "account.settings.title.BON.android": "Beállítások", "account.settings.title.BON.ios": "Az alkalmazás beállításai", "account.settings.title.BPX.android": "Beállítások", "account.settings.title.BPX.ios": "Az alkalmazás beállításai", "account.title": "Fiókom", "assortment.categories.accessibility": "Kate<PERSON><PERSON><PERSON><PERSON>", "assortment.categoriesList.item.accessibility": "%{itemName} - %{itemNumber} %{itemCount} - A kategóriák listájában", "assortment.listTitle.accessibility": "Kategóriák <PERSON>", "assortment.newsletter.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assortment.orderForm.title": "<PERSON><PERSON><PERSON>", "assortment.outlet.title": "<PERSON><PERSON><PERSON> kimen<PERSON>", "assortment.services.accessibility": "Szolgáltatás", "assortment.services.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assortment.services.title.BON": "<PERSON><PERSON><PERSON><PERSON>", "assortment.services.title.BPX": "<PERSON><PERSON><PERSON><PERSON>", "assortment.tabList.activeTab.accessibility": "Aktív %{tabName} - Tab %{tabNumber} %{tabCount}", "assortment.tabList.inactiveTab.accessibility": "%{tabName} - Tab %{tabNumber} a %{tabCount} -tól - Double Top Koppintson", "assortment.title": "Választék", "bfg.category.button": "Méretaj<PERSON><PERSON><PERSON>", "bfg.contentDescription.closeButton": "Ikon a bezáráshoz", "bfg.contentDescription.infoButton": "Tov<PERSON>bbi információkat megjelenítő ikon", "bfg.continue.from.to.description": "Folytatás a(z) %{to} lépésre a(z) %{of} k<PERSON><PERSON>ül", "bfg.continue.lastStep.description": "Folytatás az utolsó lépésre", "bfg.cup.details.text.loose": "Ha a melltartód k<PERSON>árszéle kiáll vagy az anyag ráncolódik, akkor túl nagyok.", "bfg.cupFit.title": "<PERSON>gyan illeszkednek a kosarak?", "bfg.cupSize.loose.description": "Ha a melltartód k<PERSON>árszéle kiáll vagy az anyag ráncolódik, akkor túl nagyok.", "bfg.cupSize.loose.title": "A kosarak túl nagy<PERSON>, és a kosárszél kiáll", "bfg.cupSize.tight": "t<PERSON>", "bfg.cupSize.tight.description": "A melltartód kosarai túl k<PERSON>, ha a mell nem megfelel<PERSON><PERSON> van körülölelve, és felülről és oldalról is kidudorodik. A merevítős melltartók nyomáspontokat okozhatnak a mellkason.", "bfg.cupSize.tight.title": "A kosarak túl k<PERSON>ik", "bfg.cupSize.underbust": "<PERSON>z alsó mell alatti méretem", "bfg.cupSize.well": "j<PERSON><PERSON>", "bfg.cupSize.well.description": "A jól illeszkedő kosarak teljesen körülölelik a mellet. Semmi nem nyom vagy áll ki.", "bfg.cupSize.well.title": "A kosarak jól illeszkednek", "bfg.cupSizeFit.loose": "túl laza", "bfg.cupSizeFit.title": "<PERSON><PERSON><PERSON> mé<PERSON> visels<PERSON>?", "bfg.cupSizeFit.wide": "t<PERSON> nagy", "bfg.error.recommendation.description": "Sajnos nem lehetett méretajánlást meghatározni. Csak nézz körül.", "bfg.fitHelper.cupSize.accessibility": "Segítő a cupsize -hez", "bfg.fitHelper.resultInfo.tip.accessibility": "Tipp %{tipNumber}", "bfg.fitHelper.resultInfo.title.accessibility": "Helper az eredményinformációért", "bfg.fitHelper.underbust.accessibility": "Helper overlay az alsó sávú zenekar illeszkedéséhez", "bfg.loading.isLoaded.accessibility": "Bet<PERSON>lt<PERSON>tt", "bfg.moreInfo.accessibility": "Érintse meg a további információkat", "bfg.outcome.findStore.title": "Üzlet keresése", "bfg.outcome.inspiration.title": "Hagyjuk, hogy inspiráljunk", "bfg.outcome.result.title": "M<PERSON><PERSON><PERSON><PERSON><PERSON>", "bfg.outcome.showMatchingProducts": "Megfelelő termékek megjelenítése", "bfg.outcome.showProducts.title": "Megfelelő termékek megjelenítése", "bfg.page.title.accessibility": "Melltartó illesztési útmutató %{currentStep} lépés %{totalStep}", "bfg.result.contentDescription.mainImage": "Melltartó kiválasztása kép", "bfg.result.details.title": "3 tipp a megfelelő melltartó illeszkedéshez", "bfg.result.hint.description": "Amikor felpróbálod a melltartódat, a tökéletes illeszkedés érdekében vedd figyelembe a következő dolgokat:", "bfg.result.hint.item1": "A mellnek a kosárban kell elhelyezkednie. Emeld meg a mellet (melleket) oldalról alulról a kosárba (a megfelelő pozícióba).", "bfg.result.hint.item2": "Állítsd be a pántok hosszát úgy, hogy a pántok ne vágjanak be a vállakba, és a kosár ne húzódjon felfelé <PERSON>ö<PERSON>, vagy ne legyen laza és ne c<PERSON>ús<PERSON> le.", "bfg.result.hint.item3": "Állítsd be az alsó mell alatti szélességet a kapocs segítségével. Válaszd ki az optimális kapocssort a kényelmes illeszkedés érdekében.", "bfg.result.title.accessibility": "Segítő az aluljárókhoz", "bfg.segmentedControl.cupSize.accessibility": "A csésze mérete", "bfg.segmentedControl.underbust.accessibility": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bfg.sizeSelection.contentDescription.sizeSelectionImage": "A méretválasztó képernyő képe", "bfg.sizeSelection.cupSize": "A kosárméretem", "bfg.sizeSelection.measure.button": "Szeretném megmérni a méretem", "bfg.sizeSelection.underbustSize": "A MELL ALATTI MÉRETEM", "bfg.slider.instructions.accessibility": "H<PERSON>zza fel az egyik ujjal felfelé vagy lefe<PERSON> az érték beállításához", "bfg.slider.valueChanged.accessibility": "<PERSON><PERSON>: %{newValue}", "bfg.type.all.title": "Érdekel/nminden melltartó típus", "bfg.type.attractive.title": "A reflektorfényben szeretnék\nlenni", "bfg.type.comfort.title": "Előnyben részesítem a melltartókat\nmelltartó pántok nélkül.", "bfg.type.hide.title": "<PERSON> kellene rejtenie\negy kicsit.", "bfg.type.support.title": "Előnyben részesítem\na merevítős melltartókat.", "bfg.type.title": "<PERSON> k<PERSON><PERSON><PERSON><PERSON>sen fontos s<PERSON>modra a melltartódban?", "bfg.underbust.tight": "t<PERSON>", "bfg.underbust.title": "<PERSON><PERSON><PERSON> az alsó mell alatti p<PERSON>t?", "bfg.underbustFit.loose": "túl laza", "bfg.underbustFit.loose.description": "Ha az alsó mell alatti pánt nem illeszkedik tökéletesen a testhez, felcsúszik a h<PERSON>, és nem nyújt támaszt a mellnek.", "bfg.underbustFit.loose.title": "<PERSON>z alsó mell alatti pánt túl laza", "bfg.underbustFit.tight.description": "<PERSON>gy túl szoros alsó mell alatti pánt nyomokat hagyhat a mell alatt és a háton.", "bfg.underbustFit.tight.title": "<PERSON>z alsó mell alatti pánt túl szoros", "bfg.underbustFit.well": "j<PERSON><PERSON>", "bfg.underbustFit.well.description": "<PERSON>z optimálisan k<PERSON>lasztott alsó mell alatti pánt kényelmesen simul a testhez. Semmi nem vág be.", "bfg.underbustFit.well.title": "<PERSON>z al<PERSON>ó mell alatti pánt jól illeszkedik", "catalogScanner.cameraPermission.settings.button": "Beállítások módosítása", "catalogScanner.cameraPermission.text": "Kameraengedély szükséges ehhez a funkcióhoz", "catalogScanner.cameraPermission.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catalogScanner.errorDialog.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catalogScanner.errorDialog.message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catalogScanner.errorDialog.title": "A katalógus nem tölthető be", "catalogScanner.hint.activity": "<PERSON><PERSON><PERSON><PERSON>, légy türel<PERSON>mel egy pillanatra.\nA katalógus töltődik", "catalogScanner.hint.message": "Tippünk: G<PERSON><PERSON>ződj meg ró<PERSON>, hogy elegendő fény és sík felület áll rendelkezésre a katalógushoz", "catalogScanner.navigation.title": "Katalógus <PERSON>", "catalogScanner.noProducts.hint": "<PERSON>zen az oldalon ninc<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg egy másikat.", "catalogScanner.onboarding.message": "<PERSON><PERSON>e meg a katalógus termékeket most az online boltban", "catalogScanner.onboarding.orderbutton": "Comandă catalogul", "catalogScanner.onboarding.scanButton": "Katalógus<PERSON><PERSON>", "catalogScanner.onboarding.scanPage.title": "Katalógusoldal rögzítése", "catalogScanner.onboarding.text": "Ta<PERSON><PERSON>ld meg a katalógus termékeit most az online áruházban.", "catalogScanner.onboarding.title": "S<PERSON>ess bele, szkenneld be, rendeld meg!", "catalogScanner.products.title": "Eredmények a(z) %{catalogPage} oldalhoz", "catalogScanner.scan.description": "Ha a szkennelés sikeres volt, az eredmények automatikusan megjelennek", "catalogScanner.scan.title": "A katalógus oldalt az alábbiakban a megadott keretbe kell he<PERSON>ni", "catalogScanner.search.button": "Cikk beolvasása a katalógusban", "catalogScanner.title": "Katalógus <PERSON>", "catalogScanner.video.accessibility": "Kampányvideó", "checkout.quitDialog.confirm": "Vásárl<PERSON>", "checkout.quitDialog.title": "T<PERSON><PERSON>g le akarja mondani a vásárlást?", "checkout.title": "Vásárlás", "countryList.regionChangeHint.accessibility": "A csapnál a régió megváltozik, és átirányítja a kezdőképernyőre", "countrySelection.dialog.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "countrySelection.dialog.message": "A linked a %{name} boltba %{newShop} vezet. Az alkalmazásban jelenleg a %{currentShop} van kiválasztva. Szeretne átváltani a %{currentShop}-ról a %{name} shop %{newShop}-ra, vagy me<PERSON> a linket a böngészőben?", "countrySelection.dialog.message.BON.android": "A linked a %{newShop} webáruházra mutat. Az alkalmazásban jelenleg kiválasztott ország %{currentShop}. Szeretnéd megváltoztatni az országot vagy a linket a böngészőben megnyitni?", "countrySelection.dialog.message.BON.ios": "A link a %{newShop} boltra mutat. A jelenleg kiválasztott ország %{currentShop}. Szeretnéd megváltoztatni az országot vagy megnyitni a linket az alkalmazáson kívül?", "countrySelection.dialog.message.BPX.android": "A linked a %{newShop} webáruházra mutat. Az alkalmazásban jelenleg kiválasztott ország %{currentShop}. Szeretnéd megváltoztatni az országot vagy a linket a böngészőben megnyitni?", "countrySelection.dialog.message.BPX.ios": "A link a %{newShop} boltra mutat. A jelenleg kiválasztott ország %{currentShop}. Szeretnéd megváltoztatni az országot vagy megnyitni a linket az alkalmazáson kívül?", "countrySelection.dialog.neutralButton": "Nyisd meg a böngészőben", "countrySelection.dialog.positiveButton": "Országváltás", "countrySelection.dialog.positiveButton.BON.android": "Országváltás", "countrySelection.dialog.positiveButton.BON.ios": "Ország megváltoztatása", "countrySelection.dialog.positiveButton.BPX.android": "Országváltás", "countrySelection.dialog.positiveButton.BPX.ios": "Ország megváltoztatása", "countrySelection.dialog.title": "<PERSON><PERSON><PERSON><PERSON>", "countrySelection.dialog.title.BON.android": "<PERSON><PERSON><PERSON><PERSON>", "countrySelection.dialog.title.BON.ios": "Change country", "countrySelection.dialog.title.BPX.android": "<PERSON><PERSON><PERSON><PERSON>", "countrySelection.dialog.title.BPX.ios": "Change country", "countrySelection.hint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vá<PERSON>za ki <PERSON>, ahol tartózkodik. <PERSON>e feledje, hogy ha módosítja ezt a beállítást, a nyelv és a választék megváltozhat, és a mentett elemek elveszhetnek.", "countrySelection.list.isSelected.accessibility": "<PERSON>zt<PERSON>", "countrySelection.listTitle.accessibility": "Régiók listája", "countrySelection.title": "Ország megváltoztatása", "countrySelection.title.BON.ios": "Country selection", "countrySelection.title.BPX.ios": "Country selection", "countrychange.regionChanged.accessibility": "A régió megvá<PERSON>ott", "customerCard.card.barCode.accessibility": "Kódgrafika a szkenneléshez a pénztárnál", "customerCard.card.customerCard.accessibility": "Ügyfélkártya a szkenneléshez a pénztárnál", "customerCard.card.description": "Ügyfélkártyájával kezelheti vásárlásait üzletből és online - beleértve a digitális nyugtákat is.", "customerCard.card.name.accessibility": "Név", "customerCard.card.number.accessibility": "Ügyfélszám", "customerCard.card.receiptButton": "<PERSON><PERSON><PERSON><PERSON>", "deals.dealVariant.carousel.accessibility": "A körhinta üzletek", "deals.dealVariant.carousel.amount.accessibility": "%{amount} üzletek", "deals.dealVariant.code.acceptButton": "Fedezze fel az ajánlatokat", "deals.dealVariant.code.banner.title": "Használja a kupont és takarítson meg most!", "deals.dealVariant.code.banner.title.BON": "Aktiv<PERSON>ld a kupont és csapj le a kedvezményre!", "deals.dealVariant.code.banner.title.BPX": "Aktiv<PERSON>ld a kupont és csapj le a kedvezményre!", "deals.dealVariant.code.copiedMessage.title": "Másolt kód", "deals.dealVariant.code.copyButton": "COPY:", "deals.dealVariant.code.copyButton.accessibility": "Kód %{code} - <PERSON><PERSON><PERSON><PERSON> kód", "deals.dealVariant.image.accessibility": "Az üzlet megjelenítésének képe", "deals.dealVariant.legal.sheet.accessibility": "N<PERSON>t egy lapot további információkkal.", "deals.dealVariant.product.acceptButton": "Biz<PERSON><PERSON><PERSON>", "deals.dealVariant.product.acceptButton.BON": "Tekintsd meg az aján<PERSON>ot", "deals.dealVariant.product.acceptButton.BPX": "Tekintsd meg az aján<PERSON>ot", "deals.dealVariant.product.accessibility": "Deal %{dealNumber} \n %{label} \n Eladás %{discount} \n %{altText} \n A termék besorolása %{stars} az 5 csillagból, %{voters} szavazta meg \n %{title} \n Az új ár %{newPrice}, a régi ár %{oldPrice}", "deals.dealVariant.product.pricePrefix": "Katalógus rende<PERSON>", "deals.dealVariant.product.pricePrefix.BON": "most", "deals.dealVariant.product.pricePrefix.BPX": "most", "deals.dealVariant.product.priceRangePrefix": "-tól", "deals.dealVariant.promo.acceptButton": "Fedezze fel most", "deals.dealVariant.promo.acceptButton.BON": "Fedezze fel az ajánlatokat", "deals.dealVariant.promo.acceptButton.BPX": "Fedezze fel az ajánlatokat", "deals.dealVariant.timer": "%{hours}: %{minutes}: %{seconds}", "deals.dealVariant.timer.accessibility": "%{hours} óra: %{minutes} perc: %{seconds} másodperc", "deals.dealVariant.timer.days": "%{days}: %{hours}: %{minutes}: %{seconds}", "deals.dealVariant.timer.days.accessibility": "%{days} napok: %{hours} Órák: %{minutes} percek: %{seconds} másodperc", "deals.dealVariant.timer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deals.dealVariant.timer.title.BON.android": "érvényes:", "deals.dealVariant.timer.title.BON.ios": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deals.dealVariant.timer.title.BPX.android": "érvényes:", "deals.dealVariant.timer.title.BPX.ios": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deals.dealVariant.voucher.acceptButton": "Aktiválja a kódot és takarítson meg", "deals.dealVariant.voucher.acceptButton.BON": "Aktiváld és fedezd fel az ajánlataidat!", "deals.dealVariant.voucher.acceptButton.BPX": "Aktiváld és fedezd fel az ajánlataidat!", "deals.gameScreen.navigationTitle": "Az ügyleteid", "deals.gameScreen.navigationTitle.BPX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deals.gameScreen.scratch.image.accessibility": "<PERSON><PERSON><PERSON>, amely a kép karcolásához szükséges utasításokat mutatja, hogy felfedje az üzletet", "deals.gameScreen.shake.image.accessibility": "A képen látható utasítások a telefon megrázásához, hogy felfedje az aján<PERSON>ot", "deals.gameScreen.tapADeal.image.accessibility": "A kép a következő utasításokat mutatja tap a deal", "deals.revealDeal.scratch": "Felfedje az üzletet karcolás nélkül", "deals.revealDeal.shake": "Nyárja meg az üzletet remegés nélkül", "error.contentDescription.close": "A párbeszédpanel bezárására szolgáló gomb", "error.contentDescription.tutorialImage": "Hibaikont ábrá<PERSON><PERSON>ó k<PERSON>", "error.deviceSupport.button": "Vissza a kezdőoldalra", "error.deviceSupport.button.BON.android": "Vissza a kezdőoldalra", "error.deviceSupport.button.BON.ios": "Nyisd meg az App Store-t", "error.deviceSupport.button.BPX.android": "Vissza a kezdőoldalra", "error.deviceSupport.button.BPX.ios": "Nyisd meg az App Store-t", "error.deviceSupport.text": "Sajnos az alkalmazás ezen funkcióját a készülék nem támogatja.", "error.deviceSupport.text.BON.android": "Sajnos az alkalmazás ezen funkcióját a készülék nem támogatja.", "error.deviceSupport.text.BON.ios": "Jelenleg nem tudjuk betölteni a leveleket.", "error.deviceSupport.text.BPX.android": "Sajnos az alkalmazás ezen funkcióját a készülék nem támogatja.", "error.deviceSupport.text.BPX.ios": "Jelenleg nem tudjuk betölteni a leveleket.", "error.deviceSupport.title": "<PERSON><PERSON><PERSON>, valami nem stimmel", "error.generic.accessibility": "Hiba képernyő", "error.generic.description": "Próbálkozz újra.", "error.generic.description.BON.android": "Próbálkozz újra.", "error.generic.description.BON.ios": "Valami nem sikerült.\nPróbálkozz újra!\n", "error.generic.description.BPX.android": "Próbálkozz újra.", "error.generic.description.BPX.ios": "Valami nem sikerült.\nPróbálkozz újra!\n", "error.generic.reload.accessibility": "A képernyő helyesen újratöltve", "error.generic.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error.generic.title": "<PERSON><PERSON><PERSON>, valami nem stimmel", "error.generic.title.BON.android": "<PERSON><PERSON><PERSON>, valami nem stimmel", "error.generic.title.BON.ios": "HOPPÁ,", "error.generic.title.BPX.android": "<PERSON><PERSON><PERSON>, valami nem stimmel", "error.generic.title.BPX.ios": "HOPPÁ,", "error.instructions.connection": "Ellen<PERSON><PERSON><PERSON> az internetkap<PERSON>ola<PERSON>t, majd pr<PERSON><PERSON><PERSON><PERSON> meg <PERSON>", "error.instructions.update": "Sok új funkciót adtunk hozzá, és kijavítottunk néhán<PERSON> hibát, hogy a lehető legzökkenőmentesebb élményt nyújtsuk", "error.noInternet.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error.noInternet.description": "Ellen<PERSON><PERSON><PERSON> az internetkap<PERSON>ola<PERSON>t, majd pr<PERSON><PERSON><PERSON><PERSON> meg <PERSON>", "error.noInternet.description.BON.android": "Ellen<PERSON><PERSON><PERSON> az internetkap<PERSON>ola<PERSON>t, majd pr<PERSON><PERSON><PERSON><PERSON> meg <PERSON>", "error.noInternet.description.BON.ios": "Ellenőrizd az internetkapcsolatot és próbáld ú<PERSON>.", "error.noInternet.description.BPX.android": "Ellen<PERSON><PERSON><PERSON> az internetkap<PERSON>ola<PERSON>t, majd pr<PERSON><PERSON><PERSON><PERSON> meg <PERSON>", "error.noInternet.description.BPX.ios": "Ellenőrizd az internetkapcsolatot és próbáld ú<PERSON>.", "error.noInternet.title": "<PERSON><PERSON> betölteni az oldalt", "error.noInternet.title.BON.android": "<PERSON><PERSON> betölteni az oldalt", "error.noInternet.title.BON.ios": "Nincs internetkapcsolat", "error.noInternet.title.BPX.android": "<PERSON><PERSON> betölteni az oldalt", "error.noInternet.title.BPX.ios": "Nincs internetkapcsolat", "error.title": "Hiba az alkalmazásban", "error.title.BON.android": "Hiba az alkalmazásban", "error.title.BON.ios": "Ellenőrizd az internetkapcsolatot és próbáld ú<PERSON>.", "error.title.BPX.android": "Hiba az alkalmazásban", "error.title.BPX.ios": "Ellenőrizd az internetkapcsolatot és próbáld ú<PERSON>.", "error.title.connection": "<PERSON><PERSON> betölteni az oldalt", "error.title.update": "Frissítés szükséges", "error.update.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "error.update.button.BON.android": "Nyisd meg a Google Play-t", "error.update.button.BON.ios": "Nyisd meg az App Store-t", "error.update.button.BPX.android": "Nyisd meg a Google Play-t", "error.update.button.BPX.ios": "Nyisd meg az App Store-t", "error.update.description": "Töltse le a legújabb verziót, és élvezze az új funkciók és termékek előnyeit.", "error.update.description.BON.android": "Fedezd fel a legfrissebb verziót a Google Play-ben és élvezd a még hatékonyabb működést", "error.update.description.BON.ios": "Az alkalmazást frissíteni kell. Töltsd le a legújabb verziót az App Store-ból, hogy minden funkciót használhass.", "error.update.description.BPX.android": "Fedezd fel a legfrissebb verziót a Google Play-ben és élvezd a még hatékonyabb működést", "error.update.description.BPX.ios": "Az alkalmazást frissíteni kell. Töltsd le a legújabb verziót az App Store-ból, hogy minden funkciót használhass.", "error.update.title": "Frissítés szükséges", "general.back": "<PERSON><PERSON><PERSON>", "general.banner.code.accessibility": "%{promoText} - Kód %{promoCode} <PERSON><PERSON><PERSON><PERSON> kód", "general.banner.code.copy": "MÁSOLAT:", "general.banner.code.didCopy": "MÁSOLT KÓD", "general.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general.cancel.BON.android": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general.cancel.BON.ios": "Megszakítás", "general.cancel.BPX.android": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general.cancel.BPX.ios": "Megszakítás", "general.close": "Bezárás", "general.collapsed.accessibility": "<PERSON><PERSON><PERSON>", "general.doubleTabToEdit.accessibility": "<PERSON><PERSON><PERSON><PERSON> duplán a szerkesztéshez", "general.expanded.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general.list.items.accessibility": "%{amount} tételek", "general.motionSetting.sheet.copy": "<PERSON> problém<PERSON><PERSON>, véglegesen letilthatja a Shake funk<PERSON>t.", "general.motionSetting.sheet.title": "Problémák a mozgás aktiválásával?", "general.motionSetting.toggle.title": "Használja a mobil shake funkciókat", "general.notification": "Bejelentés", "general.overlay": "Átfedés", "general.segmentedControl.buttonLabel.accessibility": "gomb", "general.segmentedControl.selectionPosition.accessibility": "Választás %{itemPosition} %{totalItems}", "general.segmentedControl.selectionPrefix.accessibility": "Kiválasztott %{element}", "general.video.accessibility": "Videotartalom", "general.webview.accessibility.ios": "webnézet", "general.yesterday": "tegnap", "inbox.button.accessibility": "<PERSON><PERSON><PERSON>", "inbox.button.unreadCount.accessibility": "%{title} - %{value} <PERSON>j <PERSON>", "inbox.dialog.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inbox.dialog.delete": "Törlés", "inbox.empty.title": "<PERSON><PERSON><PERSON> h<PERSON>", "inbox.message.deleteDialog.text": "Törölni szeretné ezt az üzenetet?", "inbox.message.deleted.accessibility": "Üzenet törölte", "inbox.message.error.title": "Nem tudta betölteni az üzenetet", "inbox.noMessages.pushNotActive.button": "Értesítések engedélyezése", "inbox.noMessages.pushNotActive.footnote": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> v<PERSON>hat<PERSON> ho<PERSON>ájárul<PERSON>.", "inbox.noMessages.pushNotActive.privacyPolicy": "adatvédelmi politika", "inbox.noMessages.pushNotActive.text": "Engedélyezze az értesítéseket, hogy soha ne maradjon le semmilyen ajánlatról", "inbox.noMessages.text": "Sajnos az alkalmazás ezen funkcióját a készülék nem támogatja.", "inbox.overview.message.read.contentDescription": "Olvassa el az üzenetet", "inbox.overview.message.unread.contentDescription": "Olvasatlan üzenet", "inbox.overview.title": "Üzenetek", "inbox.title": "<PERSON><PERSON><PERSON>", "login.successful.accessibility": "Bejelentkezés si<PERSON>es", "logout.successful.accessibility": "Jelentkezés si<PERSON>es", "motionSetting.toggle.copy": "A deaktiváláshoz kapcsolja ki", "navigation.assortment.entry.bfg.title": "BH tanácsadó", "navigation.assortment.section.moreServices.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigation.assortment.title": "Termékk<PERSON><PERSON>", "navigation.assortment.title.BON": "Szortiment", "navigation.assortment.title.BPX": "Szortiment", "navigation.cart.title": "<PERSON><PERSON><PERSON><PERSON>", "navigation.forMe.title.SHE": "<PERSON><PERSON> ne<PERSON>", "navigation.profile.title": "Fiókom", "navigation.recommendation.title": "Alkalmazás ajánlása", "navigation.shop.title": "Home", "navigation.toastNoSupportedAppForLinkFound.BON.android": "<PERSON><PERSON>", "navigation.toastNoSupportedAppForLinkFound.BPX.android": "<PERSON><PERSON>", "navigation.toastNoSupportedAppForLinkFound.android": "<PERSON><PERSON>", "navigation.wishlist.title": "Kedvencek", "onboarding.welcome.bulletPointListTitle.accessibility": "Lista az előnyökkel", "onboarding.welcome.buttonCountryChoose.accessibility": "Válassza ki a régióját", "onboarding.welcome.countryChooser.button.accessibility": "válassz régiót", "onboarding.welcome.login.button": "Jelentkezzen be", "onboarding.welcome.login.button.BON": "Bejelentkezés", "onboarding.welcome.login.button.BPX": "Bejelentkezés", "onboarding.welcome.register.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding.welcome.shop.button": "a webáruházba", "onboarding.welcome.shop.button.BON.ios": "a boltba", "onboarding.welcome.shop.button.BPX.ios": "a boltba", "onboarding.welcome.title": "Üdvözöljük a %{name}", "onboarding.welcome.title.BON.android": "A bonprix alkalmazásod", "onboarding.welcome.title.BON.ios": "A bonprix alkalmazásod ", "onboarding.welcome.title.BPX.android": "A bonprix alkalmazásod", "onboarding.welcome.title.BPX.ios": "A bonprix alkalmazásod ", "onboarding.welcome.title.accessibility": "Üdvözlő képernyő", "onboarding.welcome.uspList": "- gyors vásárlás!\n- akciók kizárólag az alkalmazásban!\n- az egész áruház kéznyújtásnyira!", "onboarding.welcome.uspList.SAN": "- A MINDEN MÉRET 1 ÁR \n- Vásárlás a számlán \n- 100 nap ingyenes hozam", "onboarding.welcome.uspList.accessibility": "lista az előnyökkel", "productDetail.availability.days": "Munkanapok", "productDetail.availability.deliveryTimeRange": "", "productDetail.availability.inStock": "Elérhető", "productDetail.availability.lowStock": "<PERSON><PERSON><PERSON>", "productDetail.availability.months": "Hónapok", "productDetail.availability.permanentlyOutOfStock": "<PERSON><PERSON><PERSON><PERSON>", "productDetail.availability.preOrderable": "Előzetes rendelés", "productDetail.availability.quantity": "Rendkívül népszerű: csak %{count}x <PERSON>rhe<PERSON><PERSON> el", "productDetail.availability.quantity.short": "Csak %{count}x elérhet<PERSON>", "productDetail.availability.temporarilyOutOfStock": "<PERSON><PERSON> sokkal v<PERSON> a raktáron", "productDetail.availability.unknown": "Ismeretlen", "productDetail.availability.weeks": "Hetek", "productDetail.basket.button.title.accessibility": "<PERSON><PERSON><PERSON><PERSON> be a kosárba", "productDetail.basketSuccessView.continueShopping": "Folytassa a vásárlást", "productDetail.basketSuccessView.goToBasket": "A goood kosár<PERSON>z", "productDetail.basketSuccessView.title": "<PERSON><PERSON><PERSON><PERSON>", "productDetail.button.basket.title": "Add a CARK -hoz", "productDetail.button.basket.withPrice.title": "A kosárban %{price}", "productDetail.close.button.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "productDetail.color.title": "Szín:", "productDetail.colors.accessibility": "%{name} - Szín %{numberOfCurrentColor} %{numberOfColors} - %{availabilityInfo}", "productDetail.colors.changed.accessibility": "A szín megváltozott", "productDetail.colors.title.accessibility": "Színválasztás", "productDetail.dimension.unavailable.accessibility": "<PERSON>em <PERSON> el", "productDetail.dynamicYieldBanner.info.button.accessibility": "<PERSON><PERSON><PERSON> meg egy felugró ablakot további információkkal", "productDetail.dynamicYieldBanner.sheet.discount.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productDetail.error.addToBasket": "Elnézést! A cikket nem lehetett hozzáadni.", "productDetail.error.addToWishlist": "Elnézést! A cikket nem lehetett hozzáadni.", "productDetail.error.giftCardAlreadyInBasket": "<PERSON><PERSON><PERSON> már van a bevásárlókosárban.", "productDetail.error.giftCardNameTooLong": "<PERSON>z ajándékutalvány vége túl hosszú.", "productDetail.error.giftCardProductAlreadyInBasket": "Az ajándékutalvány vásárlása nem kombinálható az elem megrendelésével. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> töltse ki az aktuális beszerzési folyamatot", "productDetail.error.giftCardSameValueAlreadyInBasket": "Ez az ajándékutalvány már a bevásárlókosárban található. Ugyanaz az utalvány csak egyszer adható be a kosáronként.", "productDetail.error.itemCountExceeded": "Ez a cikk már a bevásárlókosárban található. Sajnos a tételenkénti bevásárlókosár nem adható hozzá 3 -nál nagyobb mennyiséghez.", "productDetail.error.productUnavailable": "Elnézést! A cikk nem érhető el.", "productDetail.error.removeFromWishlist": "Elnézést! A cikket nem lehetett eltávolítani.", "productDetail.error.slowLoadingBannerText": "A kapcsolat lassú. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> egy <PERSON>.", "productDetail.gallery.count.accessibility": "Kép %{numberOfCurrentImage} %{numberOfImages} -tól", "productDetail.gallery.flag.mixMatch": "Mix & Match", "productDetail.gallery.flag.new": "<PERSON><PERSON>", "productDetail.gallery.flag.pack": "csomag", "productDetail.gallery.flag.sale": "Eladás", "productDetail.gallery.flag.set": "<PERSON><PERSON><PERSON><PERSON>", "productDetail.information.articleStandards.categories.animalWelfare.detail": "Az ezzel a szimbólummal rendelkező cikkek meghaladják az állati anyagokkal kapcsolatos minimumkövetelményeinket. Ezeket az a tény j<PERSON>, hogy az állati komponensek (péld<PERSON><PERSON> toll, lefe<PERSON> vagy rostok) extrahálásakor különös figyelmet fordítanak a fajok számára megfelelő hozzáállásra és az állatok kezelésére. Az ezzel a szimbólummal rendelkező cikkek független szabvány szerint vannak tanúsítva.", "productDetail.information.articleStandards.categories.animalWelfare.title": "Fontolja meg az á<PERSON>t", "productDetail.information.articleStandards.categories.improvedProduction.detail": "Az ezzel a szimbólummal rendelkező cikkeket egy bizonyíthatóan továbbfejlesztett gyártási folyamat jellemzi. B. Erőforrás -befogadási eljárás és / vagy a vegyi anyagok csökkentett felhasználása. Egy cikk ebbe a kategóriába tartozik, amint elérhető a következő márkás rostok vagy szabványok egyike.", "productDetail.information.articleStandards.categories.improvedProduction.title": "Továbbfejlesztett termelés", "productDetail.information.articleStandards.categories.organicMaterials.detail": "Az ezzel a szimbólummal rendelkező cikkek olyan természetes szálakat tartalmaznak, mint a pamut, kender vagy vászon, amelyeket biológiailag lebontottak. A hagyományos tenyésztéssel ellentétben a kémiai-szintetikus növényvédelem és a műtrágyák felhasználása elárasztódik, ami védi a természetes talaj termékenységét és a biodiverzitást. Az ezzel a szimbólummal rendelkező cikkek független szabvány szerint vannak tanúsítva.", "productDetail.information.articleStandards.categories.organicMaterials.title": "Biológiailag le<PERSON>", "productDetail.information.articleStandards.categories.recycledMaterials.detail": "Az ezzel a szimbólummal rendelkező cikkek olyan anyagokat tartalmaznak, amelyek vagy kimaradnak a gyártás során fennmaradó vagy már <PERSON>, majd hozzáadták az újrahasznosítási ciklushoz. Ez lehetővé teszi az értékes forrásokat, például a vizet és az energiát. Az ezzel a szimbólummal rendelkező cikkek elismert márkás rostokat tartalmaznak, vagy független szabvány szerint tanúsítják.", "productDetail.information.articleStandards.categories.recycledMaterials.title": "Újrahasznosított anyagok", "productDetail.information.articleStandards.categories.responsiblySourcedMaterials.detail": "Az ezzel a szimbólummal rendelkező cikkek növényi alapú nyersanyagokból készülnek, amelyeket felelősségteljes erdészeti (például viszkóz) vagy mezőgazdasági maradványokból nyernek. Ennek során figyelmet fordított a talaj termékenységének és a biodiverzitásnak a megőrzésére. Az ezzel a szimbólummal rendelkező cikkek elismert márkás rostokat tartalmaznak, vagy független szabvány szerint tanúsítják.", "productDetail.information.articleStandards.categories.responsiblySourcedMaterials.title": "Javított nyersanyagok beszerzése", "productDetail.information.articleStandards.categories.supportingSocialInitiatives.detail": "Az ezzel a szimbólummal rendelkező cikkeket bizonyították az a tény, hogy a vásárlás támogatja a társadalmi kezdeményezést. Például ez elősegíti a nyersanyagok jobb növekedési feltételeit vagy a helyszínen lévő emberek számára nyújtott oktatási ajánlatokat.", "productDetail.information.articleStandards.categories.supportingSocialInitiatives.title": "A társadalmi kezdeményezések előmozdítása", "productDetail.information.articleStandards.seals.bioRe.detail": "A \"Bior® fenntartható textil\" pecséttel rendelkező termékeink megfelelnek a magas fenntartható igényeknek. Előmozdítják a biológiai mezőgazdaságot, az ökológiai termelést, és kódon keresztül nyomon követhetők a ruházatban.", "productDetail.information.articleStandards.seals.bioRe.title": "Bior fenntartható textil", "productDetail.information.articleStandards.seals.blauerEngel.detail": "Az ezzel a pecséttel rendelkező cikkek megfelelnek a környezeti kompatibilitás, a használhatóság és az egészség iránti nagy követelményeknek. A SEAL -ot a Szövetségi Környezetvédelmi, Természetvédelmi és Nukleáris Biztonsági Minisztérium adja meg a független \"Környezeti jelek zsűri\" -vel együttműködve. Aktívan hozzájárul a környezetvédelemhez.", "productDetail.information.articleStandards.seals.blauerEngel.title": "<PERSON>", "productDetail.information.articleStandards.seals.bluesignProduct.detail": "Ezt a pecsétet csak a textil<PERSON>z rendelik, amelynek előállításában a környezet minimálisan hangsúlyozódik, és az erőforrások maximálisan megkímélik. A cikk teljesíti a környezeti és egészségvédelem, valamint a biztonság szigorú kritériumait. Ez a pecsét példát mutat az emberek és a természet védelmére.", "productDetail.information.articleStandards.seals.bluesignProduct.title": "Bluesign® termék", "productDetail.information.articleStandards.seals.cottonMadeInAfrica.detail": "A cikk megvásárlásával támogatja az afrikai pamut kezdeményezést, és elkötelezett a fenntartható és tisztességes pamuttermesztés mellett az Afrikában. Ezt a kezdeményezést már több mint egymillió mezőgazdasági termelő aktívan támogatta. Az Afrikában Siegelben készített pamutal készített cikkek vásárlásával automatikusan hozzájárul ehhez a fontos aggodalomhoz.", "productDetail.information.articleStandards.seals.cottonMadeInAfrica.title": "Támogatja a pamutot Afrikában", "productDetail.information.articleStandards.seals.downpass.detail": "Az ezzel a pecséttel rendelkező cikkek le és tollai vissza lehet nyomon követni, és a megfigyelt ellátási láncokból származhatnak. A cuccmast és az életgörbék tilos. A független tesztintézetek ellenőrzik mind a szigorú állatok jóléti, mind a magas színvonalú követelményeknek való megfelelést", "productDetail.information.articleStandards.seals.downpass.title": "<PERSON><PERSON><PERSON>", "productDetail.information.articleStandards.seals.econyl.detail": "Fedezze fel a hagyományos nejlon fenntartható alternatíváját az Econyl® -vel. Ez a textilfonal nem olajból készül, hanem a termelés és a szövet maradványaiból, valamint a tengeri eltávolított halászhálókból. Ennek eredményeként a szálak 100% -ban újrahasznosulnak, és segítenek a hulladékmennyiség egy részének további csökkentésében.", "productDetail.information.articleStandards.seals.econyl.title": "Econyl®", "productDetail.information.articleStandards.seals.ecovero.detail": "Az ebben a cikkben használt viszkóz kizárólag Európából származó Certified Woodból készül. Ezzel a fenntartható termeléssel a kibocsátások és a vízfogyasztás 50% -a megmenthető a viszkóz előállításában. A szálak megfelelnek az EU ECO címke magas környezetvédelmi előírásainak, és környezetbarát módon készülnek.", "productDetail.information.articleStandards.seals.ecovero.title": "Lenzing ™ Ecovero ™", "productDetail.information.articleStandards.seals.euecolabel.detail": "Az \"EU ökolabel\" pecsétjével rendelkező cikkek aktívan hozzájárulnak a környezet védelméhez. Ezek a termékek kevesebb hulladékot, szennyezést és CO2 -kibocsát<PERSON>t okoznak, mivel ezek szigorú követelményeknek felelnek meg a veszélyes vegyi anyagok elkerülése érdekében, és hatékonyan kezelik az energiát, a víz és az alapanyagokat. Ezeket tartósságuk, javíthatóságuk és újrahasznosíthatóságuk is jellemzi. Válasszon az \"EU ökolabel\" pecséttel rendelkező cikkekhez, és hozzájáruljon a fenntartható és környezetbarát termékek előmozdításához.", "productDetail.information.articleStandards.seals.euecolabel.title": "EU -ökolabel", "productDetail.information.articleStandards.seals.fairtradeCotton.detail": "Ezzel a pecsé<PERSON>l garan<PERSON>ljuk, hogy Ázsiában és Afrikában meglehetősen kereskedett pamutot tanúsított szövetkezetek. A kis gazdálkodók tisztességes minimális árakat kapnak a betakarításukért, és további bónuszt kapnak a közös projektek támogatására. Ezen túlmenően a termesztés során a legszigorúbb társadalmi és ökológiai szabványokat figyeljük meg.", "productDetail.information.articleStandards.seals.fairtradeCotton.title": "Tisztességes kereskedelem pamut", "productDetail.information.articleStandards.seals.goodCashmere.detail": "Az ABTF által a Good Cashmere Standard® -t a Traide Alapítvány indította el a kasmír célok védelme érdekében, a mezőgazdasági termelők és a munkavállalók életének javítása és a környezet megőrzése érdekében. Ez a szabvány elkötelezett a fenntartható és felelősségteljes termelés mellett.", "productDetail.information.articleStandards.seals.goodCashmere.title": "<PERSON><PERSON> ka<PERSON> standard®", "productDetail.information.articleStandards.seals.gotsMadeWithOrganic.detail": "A 95% -os bio -pecséttel rendelkező ruházat vásárlása elősegíti a fenntartható és a környezettudatos fogyasztást, mivel ez a pecsét garantálja, hogy a felhasznált természetes szálak legalább 95% -a biológiailag termeszthető, és a teljes termelési folyamat szigorú ökológiai és társadalmi kritériumoknak van kitéve.", "productDetail.information.articleStandards.seals.gotsMadeWithOrganic.title": "Globális organikus textil szabvány szerves anyagokkal készül", "productDetail.information.articleStandards.seals.gotsOrganic.detail": "Fedezze fel ruháinkat a 70%-os organikus pecséttel. A felhasznált pamut legalább 70% -a szerves termesztésből származik, míg a szigorú ökológiai szabványok és a tisztességes termelési feltételek garantáltak. Tudatosan válassza a fenntartható divatot, és mutat példát a környezetre és a társadalmi felelősségvállalásra.", "productDetail.information.articleStandards.seals.gotsOrganic.title": "Globális organikus textil s<PERSON>", "productDetail.information.articleStandards.seals.grs.detail": "Az ezzel a pecséttel rendelkező cikkeiben megtalálja az újrahasznosított anyagok egy részét, amely \"előzetes fogyasztó\" (a termelésből származó maradványok) és a \"poszt-fogyasztó\" (már kopott textil) anyagból áll. Ezenkívül szigorú ökológiai és társadalmi kritériumokat figyelnek meg a teljes termelési folyamat során.", "productDetail.information.articleStandards.seals.grs.title": "G<PERSON>b<PERSON><PERSON>nosí<PERSON>tt <PERSON>", "productDetail.information.articleStandards.seals.gruenerKnopf.detail": "Egyszerűen felismerje a fenntartható divatot a \"Zöld gomb\" alapján! Ez az állami pecsét a textiltermékeket jelenti, amelyekben szigorú követelményeket tesznek az emberek és a környezet védelme érdekében. Összesen 46 igényes társadalmi és környezeti kritériumot igényelnek, a szennyvíz korlátozott értékeitől a kényszermunka tilalmáig. E kritériumok betartását a független tesztközpontok ellenőrzik. A \"zöld gomb\" különleges dolga az, hogy nemcsak maga a cikk, hanem az egész társaságot is ellenőrzik.", "productDetail.information.articleStandards.seals.gruenerKnopf.title": "Zöldgomb", "productDetail.information.articleStandards.seals.ivn.detail": "Az ezzel a pecséttel készített cikkeink a biológiailag termesztett természetes szálak 100% -át tartalmazzák. Széles körű társadalmi és ökológiai kritériumokat figyelnek meg, például a peszticidek és a gyermek, valamint a kényszermunka tilalmát.", "productDetail.information.articleStandards.seals.ivn.title": "A természetes textil IVN tanúsítvánnyal rendelkezik a legjobban", "productDetail.information.articleStandards.seals.leatherWorkingGroup.detail": "A bőr munkacsoportjával elkötelezettek vagyunk a bőriparban a környezetvédelmi gyakorlatok javítása mellett. A pecséttel hordozó barnulás nagy jelentőséget tulajdonít a víz és az energia felelősségének, a munkahelyi biztonságnak és a vegyi anyagok használatának szigorú szabályozásának. A bőripar fenntartható gyakorlatát a pecséttel rendelkező bőr termékek támogatják.", "productDetail.information.articleStandards.seals.leatherWorkingGroup.title": "<PERSON><PERSON><PERSON>", "productDetail.information.articleStandards.seals.lyocell.detail": "A cikkben szereplő Tencel ™ Lyocell szálak tanúsított megújuló erdőkből származnak, ezért biológiailag lebonthatók. Nemcsak természetes illeszkedést k<PERSON>, hanem biztosítják az optimális nedvességtartalmat is, hogy biztosítsák a kellemes bőr éghajlatát.", "productDetail.information.articleStandards.seals.lyocell.title": "Tencel ™ lyocell", "productDetail.information.articleStandards.seals.madeInGreen.detail": "Ezzel a pecséttel garantáljuk a szennyező anyagok által tesztelt anyagokat, a környezetbarát vállalatokat és a biztonságos, társadalmilag elfogadható munkakörülményeket. Különleges dolog: Minden cikk egyértelmű termék -azonosít<PERSON>val van ellátva, amellyel a teljes termelést a részletekre nyomon követheti. Tudatosan válassza ki az ezzel a pecséttel rendelkező termékeket, és tapasztalja meg az átláthatóságot és a felelősséget.", "productDetail.information.articleStandards.seals.madeInGreen.title": "<PERSON><PERSON><PERSON> s<PERSON>ben készítette: oeko-tex®", "productDetail.information.articleStandards.seals.modal.detail": "A cikkben szereplő Tencel ™ modális szálak tanúsított megújuló erdőkből származnak. A termelési folyamat energia -befogadás és környezetbarát, amelyet az EU Ecolabel tanúsít. A magas színvonal miatt a cikk megőrzi csodálatosan lágy természetét, még a gyakran viselése után is.", "productDetail.information.articleStandards.seals.modal.title": "Tencel ™ modális", "productDetail.information.articleStandards.seals.nordicSwan.detail": "A skandináv pecséttel rendelkező cikkek környezetbarát termeléssel támogatják, amelyben az energia, a víz és a CO2 megmentésre kerül. A felhasznált anyagokat ellenőrzik az egészségkompatibilitása és a minőség szempontjából. Ezenkívül rendszeres ellenőrzéseket végeznek annak biztosítása érdekében, hogy a tanúsított társaságok megfeleljenek a magas színvonalnak. A skandináv pecséttel rendelkező cikkek aktívan hozzájárulnak a természet védelméhez.", "productDetail.information.articleStandards.seals.nordicSwan.title": "<PERSON><PERSON><PERSON> -ökolabel", "productDetail.information.articleStandards.seals.ocs100.detail": "A tömítéssel rendelkező cikkeink legalább 95% -ban biológiailag termesztett természetes szálakat, például pamutot és vászonot tartalmaznak. Az értéklánc mentén lévő független kezelőszervek révén biztosítjuk, hogy a végtermékben ne legyen hagyományos pamut.", "productDetail.information.articleStandards.seals.ocs100.title": "<PERSON><PERSON><PERSON> ta<PERSON> (OCS) 100", "productDetail.information.articleStandards.seals.ocsBlended.detail": "Fedezze fel a pecséttel rendelkező cikkeinket, amelyek legalább 70% -ban biológiailag termesztett természetes szálakat tartalmaznak. Nagy jelentőséggel bírunk a környezetbarát kritériumoknak, és figyelembe veszik az ökológiai és társadalmi szempontokat a termelés során.", "productDetail.information.articleStandards.seals.ocsBlended.title": "Szerves ta<PERSON> (OCS) keverve", "productDetail.information.articleStandards.seals.oekoTex.detail": "Az ezzel a pecséttel készített cikkeink organikus termesztésből származó pamutot tartalmaznak, amelyet az Oeko-Tex® Organic Cotton Standard szerint tanúsítanak. Ez a globálisan elismert szabvány az organikus pamutból vagy organikus pamut keverékekből készült textiltermékek átláthatóságát és nyomon követhetőségét kínálja.", "productDetail.information.articleStandards.seals.oekoTex.title": "Oeko-tex® organikus pamut", "productDetail.information.articleStandards.seals.organicCotton.detail": "A pamut hagyományos termesztése tartalmazza az emberek környezetét és egészségét a magas vízfogyasztás és a mérgező növényvédő termékek felhasználása miatt. Cikkeink viszont tanúsított bio pamutból (például GOTS) állnak, amikor a genetikailag manipulált magvak és a káros peszticidek termesztése tilos. Ezenkívül akár 90% -os víz menthető meg a hagyományos tenyésztéshez képest.", "productDetail.information.articleStandards.seals.organicCotton.title": "Organikus pamut", "productDetail.information.articleStandards.seals.rcs100.detail": "Az ezzel a pecséttel rendelkező termékek legalább 95% -os újrahasznosított anyagból állnak, amely<PERSON> magukban foglalják mind a \"előzetes fogyasztókat\" (a termelés maradványai), mind a \"poszt-fogyasztók\" (már kopott textil). Olyan cikkeket jellemezünk, amely<PERSON> fontos hozzájárulást nyújtanak a hulladék és az erőforrás -fogyasztás csökkentéséhez.", "productDetail.information.articleStandards.seals.rcs100.title": "Újrahasznosított igénylési standard (RCS) 100", "productDetail.information.articleStandards.seals.rcsBlended.detail": "Ez a termék legalább 30% -os újrahasznosított anyagot tartalmaz (20% pamut esetén). A független tesztintézetek biztosítják, hogy az újrahasznosító anyagok arányát a teljes ellátási lánc mentén ellenőrizzék. Felhívjuk figyelmét, hogy a cikkek elkészítésére nincsenek speciális ökológiai vagy társadalmi normák.", "productDetail.information.articleStandards.seals.rcsBlended.title": "Újrahasznosított igényelhető standard (RCS) keverve", "productDetail.information.articleStandards.seals.recycledMaterial.detail": "Ez a gyönyörű divatdarab legalább 30% -os újrahasznosító anyag<PERSON>ól (pamut 20%) áll, és elismert pecséttel vagy márkás rostból, például a Reborn ™ vagy a Recoot® tanúsítvánnyal rendelkezik. A nyersanyagokat, például a textil- vagy műanyag hulladékot már nem hasz<PERSON>l<PERSON>, nem készítik el és feldolgozzák új, újrahasznosított szálakba vagy műanyagokba. Ez az eljárás értékes forrásokat takarít meg, például a víz és az energia.", "productDetail.information.articleStandards.seals.recycledMaterial.title": "Újrahasznosított anyagok", "productDetail.information.articleStandards.seals.refibra.detail": "Tapasztalja meg a keringési ötletet: Ezt a cikket a Tencel ™ X Refibra ™ szálakkal készítették, amelyeket pamutmaradványokból nyernek. Innovatív folyamatban a maradványokat a cellulózzal együtt dolgozják fel új Tencel ™ Lyocell szálakba. Ez a keringési folyamat nyersanyagokat és energiát takarít meg, és csökkenti a szeméttermelést.", "productDetail.information.articleStandards.seals.refibra.title": "Tencel ™ a Refibra ™ technológiával", "productDetail.information.articleStandards.seals.repreve.detail": "A Repreze® rost segítségével a hagyományos poliészter környezetbarát alternatívája újrahasznosított poliészterből készül, amelyet műanyag palackokból nyernek. Ez a rost ugyanazokat a stabil tulajdonságokat kínálja, mint a hagyományos poliészter, míg a víz, az energia és az erőforrások egyszerre mentenek meg. A cikk megvásárlásával aktívan járul hozz<PERSON> a fenntarthatósághoz.", "productDetail.information.articleStandards.seals.repreve.title": "Repeve® Unifi, Inc.", "productDetail.information.articleStandards.seals.responsibleDown.detail": "Az állatok javára: Az ezzel a pecséttel rendelkező cikkek garantálják a fajok megfelelő hozzáállását és kezelését a libák és a kacsák kezelésére. Szigorú iránymutatások az élelmiszerminőségre és a szálláshelyekre, biztosítják, hogy a cucc árbocok és az élet dugó tilos legyen. Ez a pecsét biztosítja a lefelé és a tollak nyomon követhetőségét.", "productDetail.information.articleStandards.seals.responsibleDown.title": "Felelősségtel<PERSON><PERSON>", "productDetail.information.articleStandards.seals.responsibleWool.detail": "Ezzel a pecséttel az állatjólét a juhok támogatják. Meghatározzuk a fajok megfelelő hozzáállásának szigorú kritériumait, ideértve a tiszta ivóvíz rendelkezésre állását és a megfelelő takarmányt. Az állati kegyetlenség, mint például a fájdalmas \"museling\", szigorúan tilos. Ezenkívül a pecsét elkötelezett a tisztességes munkakörülmények mellett a nyírással és a biodiverzitás védelmével.", "productDetail.information.articleStandards.seals.responsibleWool.title": "Felelősségteljes g<PERSON>", "productDetail.information.articleStandards.seals.seaqual.detail": "Ez a cikk legalább 30% Seaqual ™ szálakat tartalmaz, amelyek közül legalább 10% -os tengeri műanyagból készül, a többi \"fogyasztó utáni\" poliészter. A Seaqual ™ innovatív technológiája fenntartható poliészter szálakat állít elő, újrahasznosított tengeri szobrászatból.", "productDetail.information.articleStandards.seals.seaqual.title": "Seaqual ™", "productDetail.information.articleStandards.seals.spinnova.detail": "A vállalat kitűzte a célt, hogy a textilipar fenntarthatóbbá tegye. A 100% -os természetes spinnova rostjával olyan anyagot k<PERSON>, amelyet vegyi anyagok használata nélkül gyártanak, és biológiailag lebonthatók. A szálak FSC® vagy PEFC tanúsítvánnyal rendelkező fából állnak, és a gyártáskor lényegesen kevesebb vizet fogyasztanak, mint a pamut.", "productDetail.information.articleStandards.seals.spinnova.title": "<PERSON><PERSON>", "productDetail.information.articleStandards.seals.sustainableViscose.detail": "Ennek az elegáns cikknek a viszkózját az Európából fenntartható, tanúsított fából szerezzük. A regionális fa használatával rövid távú szállítási útvonalak vannak lehetővé, és a fenntartható művelés biztosítható. Ez lehetővé teszi számunkra, hogy megtakarítsuk a vízfogyasztás és a kibocsátás 50% -át, amely akkor me<PERSON>ü<PERSON> fel, ha a viszkotikus teremtés szokásos. Szálainkat környezetbarát módon gyártjuk, és megfelelnek a magas környezetvédelmi előírásoknak.", "productDetail.information.articleStandards.seals.sustainableViscose.title": "Fenntartható viszkóz", "productDetail.information.articleStandards.title": "fenntarthat<PERSON>g", "productDetail.information.brand.title": "m<PERSON><PERSON>", "productDetail.information.description.articleNumber": "Cikkszám:", "productDetail.information.description.title": "Le<PERSON><PERSON><PERSON>", "productDetail.information.details.title": "Részletek", "productDetail.information.importantInformation.button.title": "A termékbiztonság részletei", "productDetail.information.importantInformation.productSafety.responsible.detail": "Az EU -ban s<PERSON><PERSON><PERSON><PERSON>el rendelkező gazdasági szereplő felelős ehhez a termékért:", "productDetail.information.importantInformation.productSafety.responsible.note": "Megtalálja a termékért felelős gazdasági szereplőt is a megfelelő terméken vagy csomagoláson, vagy a termé<PERSON>ez csatolt dokumentumban.", "productDetail.information.importantInformation.productSafety.responsible.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sze<PERSON>ly az EUért:", "productDetail.information.importantInformation.productSafety.title": "Termékbiztonság", "productDetail.information.importantInformation.title": "Fontos információk", "productDetail.loading.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "productDetail.notifyMe.button.title": "Memória -e -mail", "productDetail.payBack.title": "%{points} megtérülési pontok", "productDetail.payBack.title.accessibility": "%{points} megtérülési pontokat kapsz", "productDetail.price.accessibility": "Ár %{price}", "productDetail.price.new.accessibility": "Új ár %{price}", "productDetail.price.old.accessibility": "<PERSON><PERSON><PERSON>r %{oldPrice}", "productDetail.price.vat.link": "Szállítási költségek", "productDetail.price.vat.title": "beleértve HÉA Plus {szállítási költségek}", "productDetail.rating.stars.accessibility": "%{rating} az 5 csillagból", "productDetail.recommendation.matchingTitle": "<PERSON><PERSON> meg<PERSON>l<PERSON>", "productDetail.recommendation.moreFromTheSeriesSubTitle": "<PERSON><PERSON> meg<PERSON>l<PERSON>", "productDetail.recommendation.moreFromTheSeriesTitle": "Több a sorozatból", "productDetail.recommendation.priceDiscountTitle": "el", "productDetail.recommendation.product.accessibility": "Termék -ajánlás:", "productDetail.recommendation.product.count.accessibility": "Termék -ajánlás %{numberOfCurrentProduct} %{numberOfProducts} -tól", "productDetail.recommendation.product.title.count.accessibility": "%{title} %{numberOfCurrentProduct} a %{numberOfProducts} -tól", "productDetail.recommendation.recentlyViewedTitle": "Nemrégiben lá<PERSON>", "productDetail.recommendation.recommendationSubTitle": "Böngés<PERSON><PERSON><PERSON> most", "productDetail.recommendation.recommendationTitle": "<PERSON><PERSON><PERSON><PERSON> alternatív<PERSON>", "productDetail.recommendation.shopTheLook.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> - %{numberOfProducts} termékeket tartalmaz", "productDetail.recommendation.shopTheLookSubTitle": "Inspirációk az Ön számára", "productDetail.recommendation.shopTheLookTitle": "Vásárolja meg a megjelenést", "productDetail.review.bar.accessibility": "%{count} Az ügyfelek %{rating} csillaggal értékelték a cikket", "productDetail.review.button.plural.title": "Mutassa meg az összes %{count} véleményt", "productDetail.review.button.singular.title": "<PERSON> besorol<PERSON>", "productDetail.review.emptyTitle": "Ezt a cikket nem vizsgálták felül", "productDetail.review.navigation.accessibility": "Minden véleményhez", "productDetail.review.newReview.button.title": "<PERSON><PERSON><PERSON> be a <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productDetail.review.pagination.accessibility": "Látta %{count} %{max} <PERSON>rt<PERSON>kelésekr<PERSON><PERSON> - <PERSON><PERSON><PERSON> tö<PERSON>t", "productDetail.review.pagination.copy": "%{count} %{max} minősítések %{count}", "productDetail.review.pagination.loaded.accessibility": "%{reviewsPerPage} További vélemények betöltve", "productDetail.review.plural.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productDetail.review.rating.accessibility": "Termékértékelés %{rating} csillagok - Írva %{date}", "productDetail.review.reviewerName.accessibility": "%{reviewerName} -tól", "productDetail.review.showMoreReview.button.title": "<PERSON><PERSON><PERSON>", "productDetail.review.singular.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productDetail.review.summery.accessibility": "Az átlagos termék besorolása %{rating} 5 csillagból %{count} koordinál", "productDetail.review.title": "Ügyfél vélemények", "productDetail.share.button.accessibility": "Ossza meg a terméket", "productDetail.shopUsps.title.accessibility": "<PERSON>z Ön <PERSON>", "productDetail.shopUsps.usps": "Ingyenes szállítás 50 € -tól\nIngyenes visszatérés\nRészlet", "productDetail.size.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> méret %{variantName} - <PERSON><PERSON><PERSON> megv<PERSON>", "productDetail.size.advisor.button.title": "<PERSON><PERSON>ret <PERSON>tan<PERSON><PERSON>adó", "productDetail.size.title": "<PERSON><PERSON><PERSON>:", "productDetail.unavailable.overlay.accessibility": "<PERSON>em <PERSON> el", "productDetail.variant.priceDisclaimer": "Minden ár nem tartalmazza a HÉA és a szállítási költségeket", "productDetail.variant.title": "<PERSON><PERSON><PERSON><PERSON> egy méretet", "productDetail.variant.voucher.details": "", "productDetail.variant.voucher.recipient": "-Ra", "productDetail.variant.voucher.textFieldPlaceholder": "<PERSON><PERSON><PERSON> be a neveket", "productDetail.voucher.textField.count.accessibility": "%{count} %{max} karakterekből", "productDetail.wishlist.added.accessibility": "Hozzáadva a jegyzettömbnek", "productDetail.wishlist.button.title.isNotWishlisted.accessibility": "Adja hozzá a jegyzettömböt", "productDetail.wishlist.button.title.isWishlisted.accessibility": "Eltávolítás", "productDetail.wishlist.removed.accessibility": "Eltávolítva a jegyzettömbből", "productReview.Action.writeReview": "<PERSON><PERSON><PERSON>", "productReview.Card.ShowLessReview.Button.title": "<PERSON><PERSON><PERSON><PERSON>", "productReview.Card.authorSeparator": "•", "productReview.Card.showMoreReview.Button.title": "<PERSON><PERSON><PERSON>", "productReview.Filter.stars": "%{count} csillagok", "productReview.Pagination.ShowMore": "<PERSON><PERSON><PERSON>", "productReview.Rating.disclaimer": "Jelenleg nem tudjuk megérteni az összes minősítést, hogy a besorolt ​​termék valóban a recenzensek tulajdonában van -e. A 2024. március 1 -jétől származó vélemények mind ellenőrzött vásárlásokon alapulnak.", "productReview.Rating.reviewCount": "%{count} vélemények", "productReview.Rating.sortByLabel": "<PERSON><PERSON><PERSON>", "productReview.Rating.sortByLabel.accessibility": "Rendezve: %{sorting} - Változtassa meg a válogatást", "productReview.Rating.stars": "%{count} csillagok", "productReview.Sort.highestRated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productReview.Sort.lowestRated": "Legalac<PERSON><PERSON><PERSON> be<PERSON>", "productReview.Sort.mostRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productReview.Sort.oldest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productReview.Sort.title": "Osztályozás", "productReview.Sort.updated.accessibility": "A rendezés frissítve", "productReview.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push.deviceInfo.shopCountry": "shopCountryCode", "pushNotifications.pushOptInAfterCheckout.benefit.confirmation": "Szállítási visszaigazolás", "pushNotifications.pushOptInAfterCheckout.benefit.offers": "Különleges ajánlatok", "pushNotifications.pushOptInAfterCheckout.benefit.style": "Legújabb stílusok", "pushNotifications.pushOptinReavailability.benefit.discounts": "Exkluzív kedvezmények", "pushNotifications.pushOptinReavailability.benefit.reavailability": "Újra elérhető termékek", "pushNotifications.pushOptinReavailability.benefit.shipping": "Szállítási visszaigazolás", "pushNotifications.pushOptinReavailability.benefit.trends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pushNotifications.settings.checkbox.BON.android": "Értesítések fogadása", "pushNotifications.settings.checkbox.BPX.android": "Értesítések fogadása", "pushNotifications.settings.checkbox.android": "Értesítések fogadása", "pushNotifications.settings.description.BON.android": "Maradjon naprakész és kapjon értesítést exkluzív kedvezményekről, inspirációkról és a legújabb trendekről!", "pushNotifications.settings.description.BPX.android": "Maradjon naprakész és kapjon értesítést exkluzív kedvezményekről, inspirációkról és a legújabb trendekről!", "pushNotifications.settings.description.android": "Maradjon naprakész és kapjon értesítést exkluzív kedvezményekről, inspirációkról és a legújabb trendekről!", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.BON.android": "Beállítások", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.BPX.android": "Beállítások", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.android": "Beállítások", "pushNotifications.settings.dialogMissingSystemPermission.title.BON.android": "<PERSON><PERSON><PERSON><PERSON><PERSON> engedély", "pushNotifications.settings.dialogMissingSystemPermission.title.BPX.android": "<PERSON><PERSON><PERSON><PERSON><PERSON> engedély", "pushNotifications.settings.dialogMissingSystemPermission.title.android": "<PERSON><PERSON><PERSON><PERSON><PERSON> engedély", "pushNotifications.settings.moreInformation.BON.android": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pushNotifications.settings.moreInformation.BPX.android": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pushNotifications.settings.moreInformation.android": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pushNotifications.settings.moreInformation.openedText.legalLink.BON.android": "adatvédelmi politika", "pushNotifications.settings.moreInformation.openedText.legalLink.BPX.android": "adatvédelmi politika", "pushNotifications.settings.moreInformation.openedText.legalLink.android": "adatvédelmi politika", "pushNotifications.settings.title.BON.android": "Értesítések", "pushNotifications.settings.title.BPX.android": "Értesítések", "pushNotifications.settings.title.android": "Értesítések", "pushPromotion.banner.confirm": "Igen", "pushPromotion.banner.copy": "Szeretne azonnal tájékoztatni az árcsökkentésről?", "pushPromotion.banner.description.accessibility": "Szalaghirdetés a push értesítések engedélyezéséhez", "pushPromotion.banner.heading.accessibility": "cím", "pushPromotion.banner.title": "<PERSON>ha ne hagyja ki újra egy kedvezményt", "pushPromotion.footnote.labelLegalLink": "adatvédelmi politika", "pushPromotion.optIn.button": "Értesítések bekapcsolása", "pushPromotion.optIn.firstArgument": "Exkluzív kedvezmények", "pushPromotion.optIn.footNote.text": "Az engedé<PERSON>t bármikor v<PERSON>zavonhatod. <u>Itt elérheted az adatvédelmi irányelveket.", "pushPromotion.optIn.footNote.text.BON.android": "Az engedé<PERSON>t bármikor v<PERSON>zavonhatod. <u>Itt elérheted az adatvédelmi irányelveket.</u>", "pushPromotion.optIn.footNote.text.BON.ios": "Az engedélyt bármikor visszavonhatod. Itt elérheted az <u>adatvédelmi irányelveket</u>.", "pushPromotion.optIn.footNote.text.BPX.android": "Az engedé<PERSON>t bármikor v<PERSON>zavonhatod. <u>Itt elérheted az adatvédelmi irányelveket.</u>", "pushPromotion.optIn.footNote.text.BPX.ios": "Az engedélyt bármikor visszavonhatod. Itt elérheted az <u>adatvédelmi irányelveket</u>.", "pushPromotion.optIn.footnote": "You can revoke your consent at any time.", "pushPromotion.optIn.pageTitle.accessibility": "<PERSON><PERSON> overlay a push értesítések elfogadásához", "pushPromotion.optIn.title": "<PERSON><PERSON><PERSON><PERSON> be az értesítéseket, hogy naprakész legyél!", "pushPromotion.optIn.uspListTitle.accessibility": "Lista az előnyökkel", "pushPromotion.pushOptIn.caption": "Engedélyezze az értesítéseket és maradjon naprakész", "pushPromotion.pushOptInAfterCheckout.caption": "Stay up-to-date", "pushPromotion.pushOptinReavailability.title": "Köszönjük szépen! Önt e-mailben fogjuk tájékoztatni", "pushPromotion.uspList": "- Kedvezmények csak\naz app-ban\n- Legújabb\ntrendek- Információ a\nküldeményről", "pushPromotion.uspList.BON.android": "- Kedvezmények csak\naz app-ban\n- Legújabb\ntrendek\n- Információ a\nküldeményről\n", "pushPromotion.uspList.BON.ios": "- Kedvezmények csak\naz app-ban\n- Legújabb\ntrendek\n- Információ a\nküldeményről", "pushPromotion.uspList.BPX.android": "- Kedvezmények csak\naz app-ban\n- Legújabb\ntrendek\n- Információ a\nküldeményről\n", "pushPromotion.uspList.BPX.ios": "- Kedvezmények csak\naz app-ban\n- Legújabb\ntrendek\n- Információ a\nküldeményről", "recommendation.dialog.title": "Ajánld az alkalmazást", "recommendation.message.text": "<PERSON><PERSON>, ez az alkalmazás talán tetszeni fog neked. Szerezd meg most az Apple App Store-ban vagy a Google Play Store-ban:", "salutation.loggedIn": "Üdvözlöm, %{firstname} %{lastname}!", "salutation.loggedOut": "Üdvözöljük!", "salutation.loggedOut.BON.android": "<PERSON><PERSON><PERSON><PERSON>ntünk a bonprix-nél!", "salutation.loggedOut.BON.ios": "Üdvözlöm!", "salutation.loggedOut.BPX.android": "<PERSON><PERSON><PERSON><PERSON>ntünk a bonprix-nél!", "salutation.loggedOut.BPX.ios": "Üdvözlöm!", "salutation.login": "Üdvözöljük, %{loginLink}", "salutation.login.BON": "Üdvözlöm, %{loginLink}", "salutation.login.BPX": "Üdvözlöm, %{loginLink}", "salutation.loginLink": "Jelentkezzen be", "search.account.title": "Fiókod", "search.account.title.BON.android": "Fiókod", "search.account.title.BON.ios": "FIÓKJA", "search.account.title.BPX.android": "Fiókod", "search.account.title.BPX.ios": "FIÓKJA", "search.button.accessibility": "Keresés", "search.clear.description": "Tisztítsa meg az aktuális k<PERSON>ést", "search.empty.description": "<PERSON><PERSON><PERSON>, kiegészítők és más divatos cikkek keresése", "search.empty.description.BON.android": "<PERSON><PERSON><PERSON>, kiegészítők és más divatos cikkek keresése", "search.empty.description.BON.ios": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \nlakásberendezési tárgyakat és más csodás termékeket!", "search.empty.description.BPX.android": "<PERSON><PERSON><PERSON>, kiegészítők és más divatos cikkek keresése", "search.empty.description.BPX.ios": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \nlakásberendezési tárgyakat és más csodás termékeket!", "search.empty.title": "<PERSON><PERSON><PERSON><PERSON> meg a kedvenc termékedet!", "search.empty.title.BON.android": "<PERSON><PERSON><PERSON><PERSON> meg a kedvenc termékedet!", "search.empty.title.BON.ios": "Tal<PERSON><PERSON>j rá a kedvenceidre", "search.empty.title.BPX.android": "<PERSON><PERSON><PERSON><PERSON> meg a kedvenc termékedet!", "search.empty.title.BPX.ios": "Tal<PERSON><PERSON>j rá a kedvenceidre", "search.history.clear": "<PERSON><PERSON><PERSON><PERSON>", "search.history.delete": "Törlés", "search.history.delete.contentDescription": "Keresési előzmények Kép leírásának törlése", "search.history.deleted.accessibility": "A keresési előzményt törölték", "search.history.title": "U<PERSON><PERSON><PERSON> k<PERSON>és<PERSON>", "search.history.title.BON.android": "U<PERSON><PERSON><PERSON> k<PERSON>és<PERSON>", "search.history.title.BON.ios": "KERESD AZ ARHÍVUMBAN", "search.history.title.BPX.android": "U<PERSON><PERSON><PERSON> k<PERSON>és<PERSON>", "search.history.title.BPX.ios": "KERESD AZ ARHÍVUMBAN", "search.input.hint": "<PERSON>t keresel?", "search.keyboard.close.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search.removeHistory.button": "Töröld a kereséseket", "search.removeHistory.button.BON.android": "Töröld a kereséseket", "search.removeHistory.button.BON.ios": "<PERSON><PERSON><PERSON><PERSON>", "search.removeHistory.button.BPX.android": "Töröld a kereséseket", "search.removeHistory.button.BPX.ios": "<PERSON><PERSON><PERSON><PERSON>", "search.searchBar.clear.button.accessibility": "Szöveg törlése", "search.shortcuts.accountAddress": "A címeim", "search.shortcuts.accountBalance": "A számlaegyenlegem", "search.shortcuts.accountBalance.BON": "A parancsaim", "search.shortcuts.accountBalance.BPX": "A parancsaim", "search.shortcuts.accountNewIn": "<PERSON><PERSON>", "search.shortcuts.accountOrders": "A parancsaim", "search.shortcuts.accountOverview": "<PERSON><PERSON><PERSON>", "search.shortcuts.listTitle.accessibility": "Szá<PERSON>la parancsikonok", "search.shortcuts.sale": "Eladás", "search.shortcuts.service": "Szolgáltatás", "search.slot.placeholder": "<PERSON>t keres?", "search.suggestion.category.description": "Kategória kép le<PERSON>", "search.suggestion.keyword.description": "Kulcsszókép <PERSON>", "search.suggestion.product.image.description.accesibility": "Termékkép <PERSON>", "search.suggestions.category.accessibility": "Kategória j<PERSON>", "search.suggestions.category.prefix": "a %{category}", "search.suggestions.category.title": "Kate<PERSON><PERSON><PERSON><PERSON>", "search.suggestions.emptyText": "Próbáljon ki egy másik keresési kifejezést, v<PERSON><PERSON> a helyesírást", "search.suggestions.emptyTitle": "<PERSON><PERSON><PERSON> er<PERSON><PERSON>ny a \"%{text}\" -ra", "search.suggestions.header.accessibility": "Javaslat", "search.suggestions.keyword.accessibility": "Keresési j<PERSON>", "search.suggestions.listTitle.accessibility": "Keresés<PERSON> j<PERSON>", "search.suggestions.noResults.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "search.suggestions.price.prefix": "-tól", "search.suggestions.price.prefix.BON.android": " -tól", "search.suggestions.price.prefix.BON.ios": "-tól", "search.suggestions.price.prefix.BPX.android": " -tól", "search.suggestions.price.prefix.BPX.ios": "-tól", "search.suggestions.product.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "search.suggestions.products.title": "Termékek", "search.suggestions.results.accessibility": "%{value} Eredmények rendelkezésre állnak", "settings.history.deleted": "A keresések története törölve lett", "settings.info": "Információk", "settings.open.source.licences": "Open-source licenc", "settings.push.notification.summary": "Értesítések megjelenítése az akciókról és speciális ajánlatokról.", "settings.push.notification.title": "Értesítések", "settings.search.history.summary": "A keresések története megjegyzi a bírt szavakat. Nem kell azokat ismételten beírnod.", "settings.version": "<PERSON><PERSON><PERSON><PERSON>", "settings.version.copied": "Lemásolva a memóriába", "shakeADeal.deal.button.text": "Ez legyen az enyém!", "shakeADeal.deal.discountPrice": "Most", "shakeADeal.deal.discountPrice.BON.android": "​", "shakeADeal.deal.discountPrice.BON.ios": "Nwo", "shakeADeal.deal.discountPrice.BPX.android": "​", "shakeADeal.deal.discountPrice.BPX.ios": "Nwo", "shakeADeal.deal.promoCodeButton.text": "Ez legyen az enyém!", "shakeADeal.inAppMessage.invalidCode": "<PERSON><PERSON><PERSON>, valami nem stimmel. Az akciós kód nem lett aktiválva", "shakeADeal.inAppMessage.validCode": "<PERSON>z ak<PERSON>ós kó<PERSON> a<PERSON>ív, tegyél be árukat a kosárba és használd az engedményt", "shakeADeal.offBoarding.description": "<PERSON><PERSON><PERSON> viss<PERSON> holnap is az %{numberOfDeals} újabb akciókért!", "shakeADeal.offBoarding.description.BON.android": "<PERSON><PERSON><PERSON> viss<PERSON> holnap is az %{numberOfDeals} újabb akciókért!", "shakeADeal.offBoarding.description.BON.ios": "<PERSON><PERSON><PERSON> viss<PERSON> holnap is az %{numberOfDeals} újabb akciókért!", "shakeADeal.offBoarding.description.BPX.android": "<PERSON><PERSON><PERSON> viss<PERSON> holnap is az %{numberOfDeals} újabb akciókért!", "shakeADeal.offBoarding.description.BPX.ios": "<PERSON><PERSON><PERSON> viss<PERSON> holnap is az %{numberOfDeals} újabb akciókért!", "shakeADeal.offBoarding.title": "Mára ennyi volt", "shakeADeal.onBoarding.button": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>!", "shakeADeal.onBoarding.description": "1. <PERSON><PERSON><PERSON> meg a mobilt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy lássa az aj<PERSON>t.\n2. <PERSON><PERSON><PERSON><PERSON> el %{seconds} másod<PERSON><PERSON>n belü<PERSON>, hogy szeretne-e további részleteket látni.\n3. <PERSON>em <PERSON>? R<PERSON>zza meg újra a mobiltelefonját.\n4. <PERSON><PERSON><PERSON> %{numberOfOffers} aj<PERSON><PERSON>ot fog kapni. Minden nap!", "shakeADeal.onBoarding.description.BON.android": "1. <PERSON><PERSON><PERSON><PERSON> meg a telefonod, hogy megjelenjen az ajánlat\n2. %{seconds} m<PERSON><PERSON><PERSON><PERSON> van arra, hogy <PERSON>, tetszik-e.\n3. <PERSON><PERSON>? Rázd meg ismét a telefonod!\n4. Naponta %{numberOfOffers} akciót kínálunk. Minden nap.", "shakeADeal.onBoarding.description.BON.ios": "• R<PERSON>zd meg a telefonod, hogy megjelenjen az aj<PERSON>\n• %{seconds} m<PERSON><PERSON><PERSON><PERSON> van arra, hogy el<PERSON>, tetszik-e. \n• Nem <PERSON>? Rázd meg ismét a telefonod!\n• Naponta %{numberOfOffers} akciót kínálunk. Minden nap.\n", "shakeADeal.onBoarding.description.BPX.android": "1. <PERSON><PERSON><PERSON><PERSON> meg a telefonod, hogy megjelenjen az ajánlat\n2. %{seconds} m<PERSON><PERSON><PERSON><PERSON> van arra, hogy <PERSON>, tetszik-e.\n3. <PERSON><PERSON>? Rázd meg ismét a telefonod!\n4. Naponta %{numberOfOffers} akciót kínálunk. Minden nap.", "shakeADeal.onBoarding.description.BPX.ios": "• R<PERSON>zd meg a telefonod, hogy megjelenjen az aj<PERSON>\n• %{seconds} m<PERSON><PERSON><PERSON><PERSON> van arra, hogy el<PERSON>, tetszik-e. \n• Nem <PERSON>? Rázd meg ismét a telefonod!\n• Naponta %{numberOfOffers} akciót kínálunk. Minden nap.\n", "shakeADeal.onBoarding.title": "Hogy működik?", "shakeADeal.overlay.title": "Rázd meg az akcióért", "shopFinder.alert.locationNotAuthorize.cancel": "Nem, köszönöm.", "shopFinder.alert.locationNotAuthorize.default": "Menjen a beállításokhoz", "shopFinder.alert.locationNotAuthorize.message": "%{name} s<PERSON><PERSON><PERSON>rni a helyhez. A hely megadására az üzlet keresésének egyszerűsítése érdekében van szükség. A beállításokon keresztül újra aktiválhatja.", "shopFinder.alert.locationNotAuthorize.title": "<PERSON><PERSON><PERSON><PERSON>", "shopFinder.call.shop": "Hívja a  címet.", "shopFinder.cellDetailInfo.button.title": "ÚTKÉPEK MUTATÁSA", "shopFinder.cellDetailInfo.header.text": "CÍM ÉS ELÉRHETŐSÉG", "shopFinder.closed": "<PERSON><PERSON><PERSON>", "shopFinder.detail.title.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "shopFinder.details.storeType.company": "%{company} Store", "shopFinder.details.storeType.partner": "<PERSON><PERSON>", "shopFinder.friday": "Péntek", "shopFinder.listView.accessibility": "Lista nézet", "shopFinder.loading.accessibility": "Betöltődik", "shopFinder.map.accessibility": "Térkép a boltok helyszíneivel", "shopFinder.map.detailsHint.accessibility": "További részletek megtekintéséhez du<PERSON>.", "shopFinder.map.locationIcon.accessibility": "<PERSON><PERSON><PERSON> j<PERSON> he<PERSON>e", "shopFinder.map.zoomIn.accessibility": "Nagyítás a térképen", "shopFinder.map.zoomLevel.accessibility": "Zoom szint: %{amount}", "shopFinder.map.zoomOut.accessibility": "Kicsinyítse a térképet", "shopFinder.mapView.accessibility": "Térképnézet", "shopFinder.monday": "Hétfő", "shopFinder.more.information": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shopFinder.openHours.accessibility": "%{from} - %{to} órakor", "shopFinder.opening.hours": "Nyitva tartás", "shopFinder.openingHours.today": "Ma", "shopFinder.overview.distance.accessibility": "el", "shopFinder.overview.floatingButtonList.accessibility": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> rendezése felhasználói hely szerint", "shopFinder.overview.floatingButtonMap.accessibility": "Fókuszálja a felhasználót a térképre", "shopFinder.overview.storesList.accessibility": "Tárolási lista", "shopFinder.overview.storesSuggestionsItem.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "shopFinder.overview.storesSuggestionsList.accessibility": "Tárolási javaslatok listája", "shopFinder.saturday": "Szombat", "shopFinder.search.hint": "Irányí<PERSON>ós<PERSON> / vá<PERSON> vagy cím", "shopFinder.searchHint.accessibility": "<PERSON><PERSON><PERSON><PERSON> / ort oder adressse", "shopFinder.shopLocationOnMap": "Útvonal me<PERSON>", "shopFinder.showMapDialog.actionGoogleMaps": "Nyissa meg a Google Maps -ot", "shopFinder.showMapDialog.actionOk": "Megjeleníti a  címet.", "shopFinder.showMapDialog.actionOk.ios": "Nyitott k<PERSON>", "shopFinder.showMapDialog.message": "Térképek megnyitása az útvonalak megjelenítéséhez", "shopFinder.showMapDialog.message.ios": "Melyik kártyalkalmazással szeretné megjeleníteni az útvonalat?", "shopFinder.showMore.button": "<PERSON><PERSON><PERSON>", "shopFinder.showMore.countInfo": "%{current} %{total} tárolók", "shopFinder.sunday": "Vas<PERSON>rna<PERSON>", "shopFinder.switchToList": "Lista", "shopFinder.switchToMap": "Térkép", "shopFinder.thursday": "Csütörtök", "shopFinder.title": "Filialfinder", "shopFinder.tuesday": "<PERSON><PERSON>", "shopFinder.wednesday": "Szerda", "tabBar.badge.accessibility": "Cikk", "tabBar.loggedIn.accessibility": "Bejelentkezett", "tabBar.tabItem.accessibility.plural": "cikkek", "tabBar.tabItem.accessibility.singular": "cikk", "toast.hint.accessibility.BON.android": "A pirítós üzenet megjelenik", "toast.hint.accessibility.BPX.android": "A pirítós üzenet megjelenik", "toast.hint.accessibility.android": "A pirítós üzenet megjelenik", "toolbar.catalogScanner": "Katalógus s<PERSON>", "toolbar.inbox": "<PERSON><PERSON><PERSON>", "toolbar.search": "Keresés", "toolbar.sharing": "Megosztás", "videoPlayer.playButton.accessibility": "Koppintson a videó elindításához"}