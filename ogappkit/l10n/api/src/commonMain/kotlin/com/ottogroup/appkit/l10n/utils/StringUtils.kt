package com.ottogroup.appkit.l10n.utils

/**
 * Converts a string into a list of strings, split by a delimiter and optionally trimmed.
 *
 * @param delimiter The delimiter used to split the string. Default is "\n-".
 * @param trimChars Characters to be trimmed from the start of each entry. Default is '-', ' '.
 * @return A list of trimmed strings.
 */
public fun String.asList(
    delimiter: String = "\n-",
    vararg trimChars: Char = charArrayOf('-', ' ')
): List<String> =
    if (isEmpty())
        emptyList()
    else split(delimiter.toRegex()).map {
        it.trimStart(*trimChars)
    }
