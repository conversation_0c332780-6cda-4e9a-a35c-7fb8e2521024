package com.ottogroup.appkit.l10n.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

public interface TranslationDataResolver {
    public fun getTranslation(key: String): String?

    @get:Suppress("MagicNumber")
    public val priority: Int
        get() = 0

    /**
     * Flow that emits a set of keys that have been updated.
     * This can be used to trigger UI updates or other actions
     * when the translation data changes.
     * This requires at least one emission to function properly.
     */
    public val dataUpdatedFlow: Flow<Set<String>>
}

/**
 * This is a base implementation which exposes a simple function to notify about data updates.
 * This is a workaround as ios has difficulties in working with flows directly.
 * Therefore we implemented this simple interface to be used by iOS.
 */
public abstract class BaseTranslationResolver(
    override val priority: Int = 0,
) : TranslationDataResolver {

    private val _dataUpdateFlow = MutableSharedFlow<Set<String>>()

    override val dataUpdatedFlow: Flow<Set<String>>
        get() = _dataUpdateFlow.asSharedFlow()

    public suspend fun onDataUpdate(keys: Set<String>) {
        _dataUpdateFlow.emit(keys)
    }
}
