package com.ottogroup.appkit.l10n

public data class OGL10nConfig(
    val project: String? = null,
    val locale: String = "en-US",
    val firestore: OGFirestoreConfig? = null
)

public data class OGFirestoreConfig(
    val database: String? = null,
    val useEmulator: Boolean = false,
    val host: String? = null,
    val port: Int? = null,
)

/**
 * normalized locale strings
 * @return normalized locale string
 */
public fun OGL10nConfig.normalizedLocale(): String {
    return locale.replace("_", "-")
}
