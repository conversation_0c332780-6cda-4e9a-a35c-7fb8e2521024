package com.ottogroup.appkit.l10n

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import com.ottogroup.appkit.l10n.resolver.NoopResolver
import com.ottogroup.appkit.l10n.resolver.Resolver
import kotlin.concurrent.Volatile
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

public interface OGL10n {
    public fun configure(config: OGL10nConfig)
    public fun overrideResolver(resolver: Resolver)
    public fun resolve(key: String, vararg params: Pair<String, Any>): String
    public fun dataUpdatedFlow(): Flow<Set<String>>
    public fun registerTranslationResolver(resolver: TranslationDataResolver)

    public companion object {
        /**
         * You have to be aware that the sdk's internals have to be running in order for this to be working.
         * This will fail otherwise.
         * This is a limitation of the current implementation in order
         * to provide an easy direct access without any additional setup.
         */
        public fun shared(): OGL10n {
            return _l10nHolder
        }
    }
}

/**
 * Provides a Flow of L10n instances that emits whenever the localization data is updated.
 * This is useful for observing changes in localization data and updating the UI accordingly.
 */
public fun OGL10n.asFlow(): Flow<OGL10n> = dataUpdatedFlow().map { this }

/**
 * Provides a Flow of resolved localized string that emits whenever the localization data is updated.
 * This is useful for observing changes in localization data and updating the UI accordingly.
 * The resolve function is applied to each emitted L10n instance to get the localized string.
 */
public fun OGL10n.asFlow(resolve: (OGL10n.() -> String)): Flow<String> =
    asFlow().map(resolve).distinctUntilChanged()

internal expect val platform: String

/**
 * We need a global access to the L10n instance in order to use that in our generated access layer.
 * This is not the best practice, but as we want to also allow to override the L10n instance globally,
 * for e.g. Unit tests, need to have a way to replace the global instance.
 */
internal fun getString(
    key: String,
    vararg params: Pair<String, Any>
): String {
    return _l10nHolder.resolve(key, *params)
}

@Volatile
private var _l10nHolder: OGL10n = BaseOGL10n()

internal fun overrideL10nHolder(newL10n: OGL10n) {
    _l10nHolder = newL10n
}

public open class BaseOGL10n(private var resolver: Resolver = NoopResolver()) : OGL10n {
    private var config: OGL10nConfig = OGL10nConfig()

    protected fun setAsCurrentHolder() {
        overrideL10nHolder(this)
    }

    override fun configure(config: OGL10nConfig) {
        this.config = config
    }

    override fun overrideResolver(resolver: Resolver) {
        this.resolver = resolver
    }

    override fun dataUpdatedFlow(): Flow<Set<String>> {
        return resolver.dataUpdatedFlow
    }

    override fun registerTranslationResolver(translationResolver: TranslationDataResolver) {
        resolver.addTranslationResolver(translationResolver)
    }

    override fun resolve(
        key: String,
        vararg params: Pair<String, Any>
    ): String {
        val keys = generateKeysToLookup(key, config.project, platform)
        for (k in keys) {
            val translation = resolver.resolve(k, config.project, platform)
            if (translation != null) {
                return resolveTranslation(translation, params)
            } else {
                Logger.d("Resource not found: $key - $k")
            }
        }
        return key
    }

    private fun resolveTranslation(
        translation: String,
        params: Array<out Pair<String, Any>>
    ): String {
        return if (params.isNotEmpty()) {
            params.fold(translation) { acc, (placeholder, value) ->
                acc.replace(
                    placeholder,
                    value.toString()
                )
            }
        } else {
            translation
        }
    }

    private fun generateKeysToLookup(
        key: String,
        project: String?,
        platform: String
    ): List<String> {
        return buildList {
            project?.let {
                add("$key.$project.$platform")
                add("$key.$project")
            }
            add("$key.$platform")
            add(key)
        }
    }
}
