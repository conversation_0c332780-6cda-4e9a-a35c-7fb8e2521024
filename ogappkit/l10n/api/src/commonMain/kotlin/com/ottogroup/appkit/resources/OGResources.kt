package com.ottogroup.appkit.resources

/**
 * A resource provider that can list and resolve resources.
 *
 * Android: will by default use the assets folder
 *
 * iOS: will by default use the Resources folder
 */
public interface OGResources {
    /**
     * List all resources in the given path
     * @param path the path to list. path is relative to platform resource directory (android = assets, ios = Resources)
     * @return a list of resources
     */
    public fun list(path: String): List<OGResource>

    /**
     * Resolve a resource by path
     * @param path the path to resolve. path is relative to platform resource directory (android = assets, ios = Resources)
     * @return the resource
     */
    @Throws(ResourceNotFoundException::class)
    public fun resolve(path: String): OGResource
}

public interface OGResource {
    public val name: String
    public val path: String
    public val extension: String

    @Throws(IOException::class)
    public fun readUtf8(): String
}

public open class IOException(message: String) : Exception(message)
public class ResourceNotFoundException(message: String) : IOException(message)
