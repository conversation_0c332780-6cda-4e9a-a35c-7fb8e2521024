package com.ottogroup.appkit.l10n.data

import kotlinx.serialization.Serializable

@Serializable
public data class Translations(
    val translations: List<Translation>
)

public typealias Locale = String

@Serializable
public data class Translation(
    val key: String,
    val deprecated: Boolean,
    val translations: Map<Locale, TranslationValue>
)

@Serializable
public data class TranslationValue(
    val text: String,
    val verified: <PERSON>olean
)
