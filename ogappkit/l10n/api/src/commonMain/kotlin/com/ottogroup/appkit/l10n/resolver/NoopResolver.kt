package com.ottogroup.appkit.l10n.resolver

import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * A resolver that does not resolve anything.
 * It just returns the key as is.
 */
public class NoopResolver : Resolver {
    override fun resolve(key: String, project: String?, platform: String?): String {
        return key
    }

    override val dataUpdatedFlow: Flow<Set<String>>
        get() = flowOf(emptySet())

    override fun addTranslationResolver(resolver: TranslationDataResolver) {}
}
