package com.ottogroup.appkit.l10n.resolver

import com.ottogroup.appkit.l10n.data.ResourceTranslationDataResolver
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow

public class LocalResourceOnlyResolver(private val localResources: ResourceTranslationDataResolver) : Resolver {
    override fun resolve(key: String, project: String?, platform: String?): String? {
        return localResources.getTranslation(key)
    }

    override val dataUpdatedFlow: Flow<Set<String>>
        get() = emptyFlow()

    override fun addTranslationResolver(resolver: TranslationDataResolver) {}
}
