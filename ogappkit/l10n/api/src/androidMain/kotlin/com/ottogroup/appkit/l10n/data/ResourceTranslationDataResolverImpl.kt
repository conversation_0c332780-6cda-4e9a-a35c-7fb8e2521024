package com.ottogroup.appkit.l10n.data

import android.content.Context
import com.ottogroup.appkit.resources.ResourceNotFoundException

public class ResourceTranslationDataResolverImpl(private val context: Context) :
    ResourceTranslationDataResolver {
    private val idMap = mutableMapOf<String, Int>()

    override fun getTranslation(key: String): String? {
        val id = idMap.getOrPut(key) {
            val resId = context.resources.getIdentifier(
                key,
                "string",
                context.packageName
            )
            return@getOrPut if (resId == 0) {
                context.resources.getIdentifier(
                    // In XML resource files, . and _ are treated as equivalent when defining string keys.
                    // However, when using getIdentifier to retrieve a resource by its key, . and _ are not interchangeable.
                    // This means a key defined with _ in the XML cannot be found using . in the lookup, and vice versa.
                    // To ensure compatibility, we replace . with _ if the initial lookup fails.
                    // This increases the chances of finding the actual resource.
                    key.replace(
                        ".",
                        "_"
                    ),
                    "string",
                    context.packageName
                )
            } else resId
        }
        return try {
            if (id > 0) {
                context.getString(id)
            } else
                null
        } catch (_: ResourceNotFoundException) {
            null
        }
    }
}
