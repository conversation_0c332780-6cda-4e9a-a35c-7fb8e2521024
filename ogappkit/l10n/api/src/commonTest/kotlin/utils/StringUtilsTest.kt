package com.ottogroup.appkit.l10n.utils

import kotlin.test.Test
import kotlin.test.assertEquals

class StringUtilsTest {

    @Test
    fun `asList returns empty list when input is empty`() {
        // given
        val input = ""

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, emptyList<String>())
    }

    @Test
    fun `asList returns single element list when no delimiters`() {
        // given
        val input = "foo"

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, listOf("foo"))
    }

    @Test
    fun `asList returns empty element list when multiple empty parts`() {
        // given
        val input = "-\n-\n-"

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, listOf("", "", ""))
    }

    @Test
    fun `asList splits string by commas`() {
        // given
        val input = "foo,bar,baz"

        // when
        val actual = input.asList(delimiter = ",")

        // then
        assertEquals(actual, listOf("foo", "bar", "baz"))
    }

    @Test
    fun `asList splits default string`() {
        // given
        val input = "-foo\n-bar\n-baz"

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, listOf("foo", "bar", "baz"))
    }

    @Test
    fun `asList trims whitespace before elements`() {
        // given
        val input = "- foo\n- bar\n- baz"

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, listOf("foo", "bar", "baz"))
    }

    @Test
    fun `asList handles extra trim characters`() {
        // given
        val input = "- - - - - foo\n-      bar\n----- baz"

        // when
        val actual = input.asList()

        // then
        assertEquals(actual, listOf("foo", "bar", "baz"))
    }

    @Test
    fun `asList handles regex split`() {
        // givens
        val input = "1. foo\n2. bar\n3000. baz"

        // when
        val actual = input.asList(
            delimiter = "\n\\d+\\.",
            trimChars = charArrayOf(' ', '.', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0')
        )

        // then
        assertEquals(actual, listOf("foo", "bar", "baz"))
    }
}
