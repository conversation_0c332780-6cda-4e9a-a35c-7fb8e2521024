package com.ottogroup.appkit.l10n

import com.ottogroup.appkit.l10n.data.BaseTranslationResolver
import com.ottogroup.appkit.l10n.data.TranslationDataResolver
import com.ottogroup.appkit.l10n.resolver.Resolver
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

class OGL10nTests {

    private lateinit var resolver: Resolver
    private var translationResolver: TranslationDataResolver? = null

    @BeforeTest
    fun setUp() {
        resolver = object : Resolver {
            override fun resolve(key: String, project: String?, platform: String?): String? {
                return when (key) {
                    "key1" -> "translation1"
                    "key1.project" -> "translation1_project"
                    "key1.project.android" -> "translation1_project_android"
                    "key1.project.ios" -> "translation1_project_ios"
                    "key2" -> "translation2"
                    "key2.project" -> "translation2_project"
                    "key3.android" -> "translation3_android"
                    "key3.ios" -> "translation3_ios"
                    "key4" -> "translation4"
                    "key5" -> "%{param1} translation %{param2}"
                    "key6" -> "%s %s translation %s %1${'$'}s %1${'$'}s %1${'$'}s %2${'$'}s %{param1} %{param1} %{param2}"

                    else -> null
                }
            }
            override val dataUpdatedFlow: Flow<Set<String>>
                get() = flowOf(emptySet())

            override fun addTranslationResolver(resolver: TranslationDataResolver) {
                translationResolver = resolver
            }
        }
        translationResolver = null
        OGL10n.shared().overrideResolver(resolver = resolver)
    }

    val baseTranslationResolver = object : BaseTranslationResolver(123) {
        override fun getTranslation(key: String): String {
            return key
        }
    }

    @Test
    fun `global resolve passes the key to the resolver`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val resultGlobal = getString("key1")
        assertEquals("translation1", resultGlobal)
    }

    @Test
    fun `registering additional translation resolver will pass instance to general resolver`() {
        assertNull(translationResolver)
        OGL10n.shared().registerTranslationResolver(baseTranslationResolver)
        assertEquals(baseTranslationResolver, translationResolver)
    }

    @Test
    fun `resolve returns correct translation for key with project and platform`() {
        OGL10n.shared().configure(config = OGL10nConfig(project = "project"))
        val result = OGL10n.shared().resolve("key1", "param1" to "value1")
        assertEquals("translation1_project_$platform", result)
    }

    @Test
    fun `resolve returns correct translation for key with project only`() {
        OGL10n.shared().configure(config = OGL10nConfig(project = "project"))
        val result = OGL10n.shared().resolve("key2")
        assertEquals("translation2_project", result)
    }

    @Test
    fun `resolve returns correct translation for key with platform only`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val result = OGL10n.shared().resolve("key3")
        assertEquals("translation3_$platform", result)
    }

    @Test
    fun `resolve returns correct translation for key without project and platform`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val result = OGL10n.shared().resolve("key4")
        assertEquals("translation4", result)
    }

    @Test
    fun `resolve returns key when no translation found`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val result = OGL10n.shared().resolve("unknownKey")
        assertEquals("unknownKey", result)
    }

    @Test
    fun `resolve replaces placeholders with provided values`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val result = OGL10n.shared().resolve("key5", "%{param1}" to "value1", "%{param2}" to "value2")
        assertEquals("value1 translation value2", result)
    }

    @Test
    fun `resolve replaces multiple placeholders with provided values`() {
        OGL10n.shared().configure(config = OGL10nConfig())
        val result = OGL10n.shared().resolve(
            "key6",
            "%{param1}" to "value1",
            "%{param2}" to "value2",
            "%s" to "string",
            "%1${'$'}s" to "string1",
            "%2${'$'}s" to "string2",
            "%3${'$'}s" to "string3",
            "%4${'$'}s" to "string4",
            "%{param3}" to "value3",
        )
        assertEquals("string string translation string string1 string1 string1 string2 value1 value1 value2", result)
    }
}
