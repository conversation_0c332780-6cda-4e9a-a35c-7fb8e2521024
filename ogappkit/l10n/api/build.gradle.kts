import com.ottogroup.appkit.l10n.GenerateTranslationAccessLayerTask
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject

plugins {
    id("ogAppKit.libraryModule")
    id("ogAppKit.resources")
    id("ogAppKit.l10n")
}

OGResources {
    resources.register("Translations") {
        destPath = "translations"
        from = fileTree("../data/translations")
    }
}

OGL10n {
    accessLayer {
        register("Base") {
            className = "OGL10nBase"
            inputFile = file("../data/translations/en-US.json")
            packageName = "com.ottogroup.appkit.l10n"
        }
    }
}

val validationTask = tasks.register("validateJsonTranslationFiles") {
    group = "ogAppKit"
    description = "Validates the json translation files"
    doLast {
        val jsonParser = Json

        val jsonFiles = fileTree("../data/translations") {
            include("*.json")
        }
        jsonFiles.forEach { file ->
            try {
                val content = file.readText()
                val json =
                    jsonParser.parseToJsonElement(content).jsonObject // Validate JSON

                val keys = json.keys.toList()
                if (keys != keys.sorted()) {
                    logger.warn("WARN: Keys in file ${file.name} are not properly sorted.")
                }
            } catch (e: Exception) {
                throw GradleException("Invalid JSON in file: ${file.name}. Error: ${e.message}")
            }
        }
    }
}

tasks.withType<GenerateTranslationAccessLayerTask>().configureEach {
    dependsOn(validationTask)
}
