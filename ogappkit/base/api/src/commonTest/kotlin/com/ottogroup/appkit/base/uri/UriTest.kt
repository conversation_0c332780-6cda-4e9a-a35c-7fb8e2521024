package com.ottogroup.appkit.base.uri

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class UriTest {

    @Test
    fun `toUrl returns Url for valid URLs`() {
        val validUrls = listOf(
            "http://example.com",
            "https://example.com/path/to/resource?query=param#fragment",
            "ftp://ftp.example.com/resource",
            "http://localhost:8080",
            "https://sub.domain.example.com/path/",
        )
        validUrls.forEach {
            assertEquals(
                it,
                it.toUrl().toString()
            )
        }
    }

    @Test
    fun `toUrl returns null for invalid URLs`() {
        val invalidUrls = listOf(
            "justAName",
            "://missing.scheme.com",
            "http//missing.colon.com",
            "http:/one.slash.com",
            "http://example.com:portThatIsNotANumber",
            "https://nonHexEscape.com?param=%ZZ",
        )
        invalidUrls.forEach {
            assertNull(it.toUrl())
        }
    }
}
