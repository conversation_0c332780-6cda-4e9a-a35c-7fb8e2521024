package com.ottogroup.appkit.base.uri

import kotlin.test.Test
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class UrlMatcherTest {

    @Test
    fun `matchesUrl should return true for matching regex pattern`() {
        val urlMatcher = UrlMatcher("regex:http://example\\.com.*")
        assertTrue(urlMatcher.matchesUrl("http://example.com/path/to/resource".toUrl()))
    }

    @Test
    fun `matchesUrl should return false for non-matching regex pattern`() {
        val urlMatcher = UrlMatcher("regex:http://example\\.com.*")
        assertFalse(urlMatcher.matchesUrl("http://another.com/path/to/resource".toUrl()))
    }

    @Test
    fun `matchesUrl should return true for matching host pattern`() {
        val urlMatcher = UrlMatcher("host:example.com")
        assertTrue(urlMatcher.matchesUrl("http://example.com/path/to/resource".toUrl()))
    }

    @Test
    fun `matchesUrl should return false for non-matching host pattern`() {
        val urlMatcher = UrlMatcher("host:example.com")
        assertFalse(urlMatcher.matchesUrl("http://another.com/path/to/resource".toUrl()))
    }

    @Test
    fun `matchesUrl should return true for matching path pattern`() {
        val urlMatcher = UrlMatcher("path/to/resource")
        assertTrue(urlMatcher.matchesUrl("http://example.com/path/to/resource".toUrl()))
    }

    @Test
    fun `matchesUrl should return false for non-matching path pattern`() {
        val urlMatcher = UrlMatcher("path/to/resource")
        assertFalse(urlMatcher.matchesUrl("http://example.com/another/path".toUrl()))
    }
}
