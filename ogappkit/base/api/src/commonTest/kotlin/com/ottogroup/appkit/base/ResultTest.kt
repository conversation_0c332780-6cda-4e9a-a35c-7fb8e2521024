package com.ottogroup.appkit.base

import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class ResultTest {

    @Test
    fun `map transforms a successful result`() {
        assertEquals(
            Result.Success("123"),
            Result.Success(123).map { it.toString() }
        )
    }

    @Test
    fun `map returns failed result's original failure`() {
        val error = RuntimeException("Something went wrong")
        assertEquals(
            Result.Failure(error),
            Result.Failure<Int>(error).map { it.toString() }
        )
    }

    @Test
    fun `resultFor returns success when block does not throw`() = runTest {
        assertEquals(
            Result.Success("123"),
            resultFor { "123" }
        )
    }

    @Test
    fun `resultFor returns failure when block throws`() = runTest {
        val error = RuntimeException("Something went wrong")
        assertEquals(
            Result.Failure(error),
            resultFor { throw error }
        )
    }
}
