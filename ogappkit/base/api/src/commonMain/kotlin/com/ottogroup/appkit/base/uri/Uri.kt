package com.ottogroup.appkit.base.uri

import io.ktor.http.Url
import io.ktor.http.parseUrl

public fun String.toUrl(): Url? = try {
    parseUrl(this)
} catch (_: Exception) {
    null
}

/**
 * Sanitizes a URL string by removing query parameters, fragments, and normalizing the path.
 *
 * This function performs the following operations:
 * 1. Removes query parameters (everything after '?')
 * 2. Removes fragments (everything after '#')
 * 3. Removes duplicate consecutive slashes in the path
 * 4. Ensures the path ends with a trailing slash
 */
public fun String.sanitize(): String {
    val baseUrl = this.split('?', '#')[0]
    val protocolIndex = baseUrl.indexOf("://")

    return if (protocolIndex == -1) {
        baseUrl.replace(Regex("/+"), "/").let { if (it.endsWith("/")) it else "$it/" }
    } else {
        val afterProtocol = baseUrl.substring(protocolIndex + 3)
        val slashIndex = afterProtocol.indexOf('/')
        val protocol = baseUrl.take(protocolIndex + 3)

        if (slashIndex == -1) {
            "$protocol$afterProtocol/"
        } else {
            val domain = afterProtocol.take(slashIndex)
            val path = afterProtocol.substring(slashIndex).replace(Regex("/+"), "/")
            "$protocol$domain${if (path.endsWith("/")) path else "$path/"}"
        }
    }
}
