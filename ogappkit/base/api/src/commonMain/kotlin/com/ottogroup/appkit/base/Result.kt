package com.ottogroup.appkit.base

import kotlin.experimental.ExperimentalObjCName
import kotlin.native.ObjCName

@OptIn(ExperimentalObjCName::class)
@ObjCName("OGResult")
/** A sealed typ that represents either a successful or a failed outcome. */
public sealed class Result<out S : Any> {
    /** A successful [Result], holding a [value]. */
    public data class Success<out S : Any>(val value: S) : Result<S>()

    /** A failed [Result] holding a [failure]. */
    public data class Failure<out S : Any>(val failure: Throwable) : Result<S>()
}

/**
 * Transforms this [Result] into a result of another type. If this result
 * was a failure, the resulting result will hold the same failure. If this
 * result was a success, the resulting result applies [f] to its value.
 */
public fun <I : Any, O : Any> Result<I>.map(f: (I) -> O): Result<O> {
    return flatMap { Result.Success(f(it)) }
}

/**
 * Transforms this [Result] into a result of another type. If this result
 * was a failure, the resulting result will hold the same failure. If
 * this result was a success, the resulting result is [f] of its value.
 */
public fun <I : Any, O : Any> Result<I>.flatMap(f: (I) -> Result<O>): Result<O> {
    return when (this) {
        is Result.Failure<I> -> Result.Failure(failure)
        is Result.Success -> f(value)
    }
}

/**
 * Returns the value of this [Result] if it was a success, or [default] if
 * it was a failure.
 */
public fun <I : Any> Result<I>.getOrElse(default: I): I {
    return when (this) {
        is Result.Success -> value
        is Result.Failure -> default
    }
}

/**
 * Returns the value of this [Result] if it was a success, or `null` if it
 * was a failure.
 */
public fun <I : Any> Result<I>.getOrNull(): I? {
    return when (this) {
        is Result.Success -> value
        is Result.Failure -> null
    }
}

@Throws(Throwable::class)
public inline fun <T : Any> Result<T>.getOrThrow(): T {
    if (this is Result.Failure) throw failure
    return (this as Result.Success).value
}

/**
 * Returns a result as the outcome of [block]. The result will be
 * a [Result.Success] holding the return value of [block], or a
 * [Result.Failure] if [block] throws.
 */
public suspend fun <T : Any> resultFor(block: suspend () -> T): Result<T> {
    return try {
        Result.Success(block())
    } catch (t: Throwable) {
        Result.Failure(t)
    }
}
