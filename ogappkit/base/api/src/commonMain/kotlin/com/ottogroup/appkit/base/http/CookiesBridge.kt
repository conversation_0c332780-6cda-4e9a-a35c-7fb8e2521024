package com.ottogroup.appkit.base.http

/**
 * A bridge for interfacing with an app's cookies. An implementation of
 * this interface should typically forward cookies set via [setCookie] into
 * the cookie store used by the app's web views and read cookies requested
 * by [getCookies] from the same store.
 */
public interface CookiesBridge {
    /**
     * Retrieves cookies for the specified URL. Called before the SDK performs
     * API requests that required cookie headers supplied by the web view, e.g.
     * for session identification purposes.
     *
     * @param url The URL for which to get the cookies.
     * @return A map of cookie names to their values.
     */
    public suspend fun getCookies(url: String): Map<String, String>

    /**
     * Sets a cookie for the specified URL in the Set-Cookie header format.
     * Called when the SDK obtains new cookies from backend APIs and needs to
     * inform the client app.
     *
     * @param url The URL for which to set the cookie.
     * @param cookie The cookie to set in Set-Cookie header format.
     */
    public suspend fun setCookie(url: String, cookie: String)
}
