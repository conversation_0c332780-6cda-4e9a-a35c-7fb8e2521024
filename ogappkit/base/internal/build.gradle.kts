plugins {
    id("ogAppKit.libraryModule")
}

val buildConfigTask = tasks.register("generateBuildConfig", BuildConfigTask::class)

kotlin {
    sourceSets {
        androidMain {
            dependencies {
                implementation(libs.androidx.lifecycle)
            }
        }
        commonMain {
            dependencies {
                api(projects.ogappkit.base.api)
                implementation(libs.kotlinx.datetime)
                implementation(libs.square.okio)
                implementation(libs.androidx.datastore.core)
                implementation(libs.androidx.datastore.core.okio)
                implementation(libs.ktor.client.core)
            }
            kotlin.srcDirs(buildConfigTask)
        }
    }
}
