package com.ottogroup.appkit.base

import app.cash.turbine.test
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

class OperationTest {

    @Test
    fun `asOperation emits InProgress as first element`() = runTest {
        flowOf(Result.Success(123)).asOperation().test {
            assertEquals(Operation.InProgress, awaitItem())
            assertEquals(
                Operation.Complete(
                    Result.Success(123)
                ),
                awaitItem()
            )

            awaitComplete()
        }
    }

    @Test
    fun `awaitResult ignores InProgress state`() = runTest {
        flowOf(Result.Success(123)).asOperation().awaitResult().test {
            assertEquals(
                Result.Success(123),
                awaitItem()
            )

            awaitComplete()
        }
    }
}
