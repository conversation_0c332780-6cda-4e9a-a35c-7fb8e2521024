package com.ottogroup.appkit.base

import kotlin.test.Test
import kotlin.test.assertEquals

class RememberedTest {

    @Test
    fun `calculation is only triggered once after a key change`() {
        var counter = 0
        val calculation: (Int) -> Int = { key ->
            ++counter
        }
        var key = 1
        val remembered by remember(keyFun = { key }, calculation = calculation)

        repeat(5) {
            assertEquals(
                1,
                remembered
            )
        }

        key = 2
        repeat(5) {
            assertEquals(
                2,
                remembered
            )
        }
    }
}
