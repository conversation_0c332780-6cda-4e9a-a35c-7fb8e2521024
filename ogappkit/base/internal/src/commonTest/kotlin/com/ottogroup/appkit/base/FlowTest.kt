package com.ottogroup.appkit.base

import app.cash.turbine.test
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.test.runTest

class FlowTest {

    @Test
    fun `withInitialValue returns initial value before other values`() = runTest {
        flowOf(1, 2).withInitialValue(0).test {
            assertEquals(0, awaitItem())
            assertEquals(1, awaitItem())
            assertEquals(2, awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `withInitialValue returns initial value without waiting for other values`() = runTest {
        val sharedFlow = MutableSharedFlow<Int>()

        sharedFlow.withInitialValue(0).test {
            assertEquals(0, awaitItem())
            sharedFlow.emit(1)
            assertEquals(1, awaitItem())
        }
    }

    @Test
    fun `combineWithPreviousValue emits pairs of previous and current values`() = runTest {
        flowOf(1, 2, 3).combineWithPreviousValue().test {
            assertEquals(null to 1, awaitItem())
            assertEquals(1 to 2, awaitItem())
            assertEquals(2 to 3, awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `combine on empty list returns empty flow`() = runTest {
        emptyList<Flow<Int>>().combine().test {
            awaitComplete()
        }
    }

    @Test
    fun `combine on list of flows emits flow of list`() = runTest {
        val f1 = MutableStateFlow(1)
        val f2 = MutableStateFlow(10)
        listOf(f1, f2).combine().test {
            assertEquals(listOf(1, 10), awaitItem())

            f1.value = 2
            assertEquals(listOf(2, 10), awaitItem())

            f2.value = 100
            assertEquals(listOf(2, 100), awaitItem())
        }
    }

    @Test
    fun `combineOrEmpty on list of flows emits flow of list`() = runTest {
        val f1 = MutableStateFlow(1)
        val f2 = MutableStateFlow(10)
        listOf(f1, f2).combineOrEmpty().test {
            assertEquals(listOf(1, 10), awaitItem())

            f1.value = 2
            assertEquals(listOf(2, 10), awaitItem())

            f2.value = 100
            assertEquals(listOf(2, 100), awaitItem())
        }
    }

    @Test
    fun `combineOrEmpty on empty list of flows emits flow of empty list`() = runTest {
        emptyList<Flow<Any>>().combineOrEmpty().test {
            assertEquals(emptyList<Any>(), awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `onEachDistinctBy performs action only once per selected value but does not filter`() = runTest {
        val f = flowOf(
            1 to "first one",
            1 to "second one",
            2 to "first two",
            2 to "second two",
        )

        val actionsPerformed = mutableListOf<String>()

        f.onEachDistinctBy({ it.first }) {
            actionsPerformed.add(it.second)
        }.test {
            assertEquals(
                1 to "first one",
                awaitItem()
            )
            assertEquals(
                1 to "second one",
                awaitItem()
            )
            assertEquals(
                2 to "first two",
                awaitItem()
            )
            assertEquals(
                2 to "second two",
                awaitItem()
            )
            awaitComplete()
        }

        assertEquals(
            listOf("first one", "first two"),
            actionsPerformed
        )
    }

    @Test
    fun `afterEach ensures accessing StateFlow value in action block uses latest value`() = runTest {
        lateinit var stateFlow: StateFlow<Int>
        stateFlow = flowOf(1, 2, 3).afterEach { i ->
            // delay to prevent value conflation of StateFlow only emitting the latest value
            delay(100)
            // this would fail using onEach because the StateFlow value would not have updated yet
            assertEquals(i, stateFlow.value)
        }.stateIn(this, SharingStarted.Eagerly, 0)

        stateFlow.take(3).collect()
    }
}
