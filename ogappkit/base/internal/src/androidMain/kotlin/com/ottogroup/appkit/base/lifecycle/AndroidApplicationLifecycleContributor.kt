package com.ottogroup.appkit.base.lifecycle

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

public class AndroidApplicationLifecycleContributor : ApplicationLifecycleProvider {
    private val _lifecycle: MutableStateFlow<LifecycleEvent> =
        MutableStateFlow(LifecycleEvent.AppInBackground)
    override val lifecycle: Flow<LifecycleEvent> = _lifecycle.asStateFlow()

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(
            object : DefaultLifecycleObserver {
                override fun onResume(owner: LifecycleOwner) {
                    _lifecycle.value = LifecycleEvent.AppInForeground
                }

                override fun onStop(owner: LifecycleOwner) {
                    _lifecycle.value = LifecycleEvent.AppInBackground
                }
            }
        )
    }
}
