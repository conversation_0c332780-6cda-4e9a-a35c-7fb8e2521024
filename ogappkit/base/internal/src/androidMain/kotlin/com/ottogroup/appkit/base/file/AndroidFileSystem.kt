package com.ottogroup.appkit.base.file

import android.content.Context
import okio.Path
import okio.Path.Companion.toOkioPath

public class AndroidFileSystem(private val context: Context) : FileSystem() {
    override fun getCacheDir(): Path = context.cacheDir.toOkioPath()
    override fun getAppDataDir(): Path = context.filesDir.toOkioPath()
    override fun getDatabasePath(dbName: String): Path = context.getDatabasePath(dbName).toOkioPath()
}
