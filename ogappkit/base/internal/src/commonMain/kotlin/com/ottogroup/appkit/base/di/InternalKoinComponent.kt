package com.ottogroup.appkit.base.di

import org.koin.core.Koin
import org.koin.core.KoinApplication
import org.koin.core.component.KoinComponent
import org.koin.core.context.KoinContext
import org.koin.core.module.Module
import org.koin.dsl.KoinAppDeclaration

public interface InternalKoinComponent : KoinComponent {
    override fun getKoin(): Koin = InternalKoinContext.get()
}

public object InternalKoinContext : KoinContext {
    private val koinApplication by lazy { baseKoinApplication() }
    override fun get(): Koin = koinApplication.koin
    override fun getOrNull(): Koin = get()
    override fun loadKoinModules(modules: List<Module>, createEagerInstances: Boolean) {
        get().loadModules(
            modules,
            allowOverride = true,
            createEagerInstances = createEagerInstances
        )
    }

    override fun loadKoinModules(module: Module, createEagerInstances: Boolean) {
        get().loadModules(
            listOf(module),
            allowOverride = true,
            createEagerInstances = createEagerInstances
        )
    }

    override fun startKoin(koinApplication: KoinApplication): KoinApplication =
        error("Providing custom KoinApplication is not supported")

    override fun startKoin(appDeclaration: KoinAppDeclaration): KoinApplication =
        koinApplication.apply(appDeclaration)

    override fun stopKoin() {
        koinApplication.close()
    }

    override fun unloadKoinModules(modules: List<Module>) {
        get().unloadModules(modules)
    }

    override fun unloadKoinModules(module: Module) {
        get().unloadModules(listOf(module))
    }
}
