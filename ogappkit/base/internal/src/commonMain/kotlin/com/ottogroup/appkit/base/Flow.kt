package com.ottogroup.appkit.base

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.runningFold
import kotlinx.coroutines.flow.transform

/**
 * Returns a flow that directly emits the initial value as the first item,
 * before the other items. Can be used if a value is needed for UI to show
 * up.
 *
 * Example:
 * ```kotlin
 * flowOf(1,2)
 *     .withInitialValue(0)
 *     .collect {
 *         println(it)
 *     }
 * ```
 *
 * will produce:
 * ```
 * 0
 * 1
 * 2
 * ```
 */
public fun <T> Flow<T>.withInitialValue(initialValue: T): Flow<T> = flow {
    emit(initialValue)
    collect {
        emit(it)
    }
}

/**
 * Returns a flow that emits the previous and current value of the upstream
 * flow as a [Pair]. On first emission, the previous value is `null`.
 */
@Suppress("UNCHECKED_CAST")
public fun <T> Flow<T>.combineWithPreviousValue(): Flow<Pair<T?, T>> =
    runningFold((null to null) as Pair<T?, T>) { (_, previous), next ->
        previous to next
    }.filter { it.second != null }

/**
 * Combines a list of flows into a single flow that emits a list of the
 * flows' values.
 *
 * Note that the resulting flow will only emit a value if all input flows
 * have emitted a value.
 */
public inline fun <reified T> Iterable<Flow<T>>.combine(): Flow<List<T>> = combine(this) { it.toList() }

/**
 * Combines a list of flows into a single flow that emits a list of the
 * flows' values. If the original list is empty, the resulting flow
 * will emit an empty list (as opposed to [combine] which will not emit
 * anything). If the original list is not empty, the behavior is the same
 * as [combine] and the resulting flow will only emit a value if all input
 * flows have emitted a value.
 */
public inline fun <reified T> Iterable<Flow<T>>.combineOrEmpty(): Flow<List<T>> {
    if (!this.iterator().hasNext()) {
        return flow { emit(emptyList()) }
    }
    return combine(this) { it.toList() }
}

/**
 * Returns a flow that invokes the given [action] **before** each value of
 * the upstream flow is emitted downstream **only once** per distinct value
 * returned by the [selector] function.
 *
 * This is different from chaining `distinctBy` and `onEach` because it
 * does not actually filter out elements from the upstream flow. Every
 * element is passed downstream, but [action] is only performed on the
 * first of possibly multiple elements with the same selector value.
 */
public fun <T> Flow<T>.onEachDistinctBy(selector: (T) -> Any?, action: suspend (T) -> Unit): Flow<T> = flow {
    var previous: Any? = null
    collect { value ->
        val current = selector(value)
        if (current != previous) {
            action(value)
            previous = current
        }
        emit(value)
    }
}

/**
 * Equivalent to [onEach] that invokes the given action **after** each value
 * of the upstream flow is emitted downstream.
 */
public fun <T> Flow<T>.afterEach(action: suspend (T) -> Unit): Flow<T> = transform {
    emit(it)
    action(it)
}
