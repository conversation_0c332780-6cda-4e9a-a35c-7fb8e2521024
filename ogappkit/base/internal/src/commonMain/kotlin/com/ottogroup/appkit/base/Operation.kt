package com.ottogroup.appkit.base

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map

/**
 * An internal wrapper around [Result] that allows for the additional
 * state [InProgress] before the operation is [Complete] and can provide a
 * [Result].
 *
 * Complete does not mean that there will not be further results! It merely
 * indicates that any result is available.
 *
 * This may be used when combining multiple flow of operations, but wishing
 * to emit partial data before all of them have emitted a result.
 */
public sealed interface Operation<out S : Any> {
    public data object InProgress : Operation<Nothing>
    public data class Complete<out S : Any>(val result: Result<S>) : Operation<S>
}

/**
 * Convenience function to start off an operation as [Operation.InProgress]
 * before the first [Result] is emitted.
 */
public fun <T : Any> Flow<Result<T>>.asOperation(): Flow<Operation<T>> =
    map { Operation.Complete(it) }.withInitialValue(Operation.InProgress)

/**
 * Convenience function to ignore the [Operation.InProgress] state and
 * react only on a completed [Result].
 */
public fun <T : Any> Flow<Operation<T>>.awaitResult(): Flow<Result<T>> {
    return filterIsInstance<Operation.Complete<T>>().map { it.result }
}
