package com.ottogroup.appkit.base.di

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.plus
import org.koin.core.Koin
import org.koin.core.scope.Scope

public typealias ApplicationScope = CoroutineScope
public typealias ApplicationJob = Job

public fun Koin.getCoroutineScope(dispatcher: CoroutineDispatcher = Dispatchers.Default): CoroutineScope =
    get<ApplicationScope>() + dispatcher

public fun Scope.getCoroutineScope(dispatcher: CoroutineDispatcher = Dispatchers.Default): CoroutineScope =
    get<ApplicationScope>() + dispatcher

public inline fun <reified R> Scope.getAllDistinct(): List<R> =
    getAll<R>(R::class).filterNotNull().distinctBy { it::class }
