package com.ottogroup.appkit.base.http

import com.ottogroup.appkit.BuildConfig
import io.ktor.client.plugins.api.ClientPlugin
import io.ktor.client.plugins.api.createClientPlugin
import com.ottogroup.appkit.base.platform
import com.ottogroup.appkit.base.platformVersion

public sealed class OGAppKitHeader(public val name: String) {
    public abstract val value: String

    public data class AppVersion(override val value: String) :
        OGAppKitHeader("OGAK-App-Version")

    public data class AppName(override val value: String) : OGAppKitHeader("OGAK-App-Name")
    public data class AppPackage(override val value: String) :
        OGAppKitHeader("OGAK-App-Package")

    public data object LibraryVersion : OGAppKitHeader("OGAK-Library-Version") {
        override val value: String = BuildConfig.VERSION
    }

    public data object Platform : OGAppKitHeader("OGAK-Platform-Name") {
        override val value: String = platform
    }

    public data object PlatformVersion : OGAppKitHeader("OGAK-Platform-Version") {
        override val value: String = platformVersion
    }

    public data class InstallationId(override val value: String) :
        OGAppKitHeader("OGAK-Installation-Id")
}

public class OGAppKitHeaders(
    appInfoProvider: AppInfoProvider,
    installationId: InstallationIdProvider
) : List<OGAppKitHeader> by listOf(
    OGAppKitHeader.AppName(appInfoProvider.appName),
    OGAppKitHeader.AppPackage(appInfoProvider.appPackage),
    OGAppKitHeader.AppVersion(appInfoProvider.appVersionName),
    OGAppKitHeader.LibraryVersion,
    OGAppKitHeader.Platform,
    OGAppKitHeader.PlatformVersion,
    OGAppKitHeader.InstallationId(installationId.installationId)
)

public interface AppInfoProvider {
    public val appName: String
    public val appVersionName: String
    public val appPackage: String
}

public interface InstallationIdProvider {
    public val installationId: String
}

public typealias OGAppKitHeadersPlugin = ClientPlugin<OGAppKitHeaders>

public fun ogAppKitHeadersPlugin(
    headers: OGAppKitHeaders
): OGAppKitHeadersPlugin =
    createClientPlugin("OGAppKitHeaders", {
        headers
    }) {
        onRequest { request, _ ->
            <EMAIL> {
                request.headers.append(it.name, it.value)
            }
        }
    }
