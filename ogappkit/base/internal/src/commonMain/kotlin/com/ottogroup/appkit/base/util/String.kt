package com.ottogroup.appkit.base.util

/**
 * Ensures the string is suffixed by the given suffix, taking into account
 * a possibly already existing partial suffix.
 */
public fun String.suffix(suffix: String): String {
    var longestMatchingSuffixBeginning = suffix
    while (!this.endsWith(longestMatchingSuffixBeginning) && longestMatchingSuffixBeginning.isNotEmpty()) {
        longestMatchingSuffixBeginning = longestMatchingSuffixBeginning.dropLast(1)
    }
    return "${this.removeSuffix(longestMatchingSuffixBeginning)}$suffix"
}
