package com.ottogroup.appkit.base

import kotlin.reflect.KProperty

/**
 * A property delegate similar to <PERSON><PERSON> or <PERSON><PERSON><PERSON>'s remember. The value is
 * only recalculated if the key function provides a new value.
 *
 * This implementation is not thread-safe.
 */
public class Remembered<K, V>(
    private val keyFun: () -> K,
    private val calculation: (K) -> V
) {
    private var currentKey: K = keyFun()
    private var value: V = calculation(currentKey)

    public operator fun getValue(thisRef: Any?, property: KProperty<*>): V {
        val key = keyFun()
        if (currentKey != key) {
            currentKey = key
            value = calculation(currentKey)
        }
        return value
    }
}

public fun <K, V> remember(keyFun: () -> K, calculation: (K) -> V): Remembered<K, V> = Remembered(keyFun, calculation)
