@file:Suppress("BlockingMethodInNonBlockingContext")

package com.ottogroup.appkit.base.datastore

import androidx.datastore.core.CorruptionException
import androidx.datastore.core.DataStore
import androidx.datastore.core.DataStoreFactory
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.core.okio.OkioSerializer
import androidx.datastore.core.okio.OkioStorage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.withContext
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import okio.BufferedSink
import okio.BufferedSource
import okio.FileSystem
import okio.SYSTEM
import com.ottogroup.appkit.base.file.FileSystem as OGFileSystem

/**
 *  Creates a DataStore which uses kotlinx.Serialization to store the values in a JSON format.
 *
 *  @param default Default value that will be used to initialize the DataStore
 *  @param fileName The filename for the json-file
 *  @param corruptionHandler The corruptionHandler will become active if the file contents can not
 *  be serialized into the expected type. The default handler will in this case override the value
 *  in the file with the default value
 *  @param migrations Define how old data should be migrated into the new format
 *  @return The datastore for the specified type
 */
public inline fun <reified T : Any> jsonDataStore(
    default: T,
    corruptionHandler: ReplaceFileCorruptionHandler<T>? = null,
    fileName: String,
    fileSystem: OGFileSystem
): DataStore<T> {
    val delegate = DataStoreFactory.create(
        storage = OkioStorage(
            fileSystem = FileSystem.SYSTEM,
            serializer = createJsonDataStoreSerializer(Json, default),
            producePath = {
                fileSystem.getAppDataDir().resolve(fileName)
            }
        )
    )
    return delegate
}

public inline fun <reified T : Any> createJsonDataStoreSerializer(
    json: Json,
    defaultValue: T,
): OkioSerializer<T> {
    return object : OkioSerializer<T> {

        override val defaultValue: T
            get() = defaultValue

        override suspend fun readFrom(input: BufferedSource): T {
            return withContext(Dispatchers.IO) {
                try {
                    Json.decodeFromString(input.readUtf8())
                } catch (e: SerializationException) {
                    throw CorruptionException("Error with local json", e)
                }
            }
        }

        override suspend fun writeTo(t: T, output: BufferedSink) {
            withContext(Dispatchers.IO) {
                val string = json.encodeToString(t)
                output.apply {
                    writeUtf8(string)
                    flush()
                }
            }
        }
    }
}
