package com.ottogroup.appkit.base.util

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.base.http.InstallationIdProvider
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking

public typealias InstallationId = String

public class InstallationIdProviderImpl(
    private val dataStore: DataStore<InstallationId>
) : InstallationIdProvider {

    @OptIn(ExperimentalUuidApi::class)
    private suspend fun getOrCreateInstallationId(): InstallationId {
        val current = dataStore.data.first()
        return if (current == Uuid.NIL.toString()) {
            dataStore.updateData {
                Uuid.random().toString()
            }
        } else current
    }

    override val installationId: String
        get() = runBlocking { getOrCreateInstallationId() }
}
