package com.ottogroup.appkit.base.lifecycle

import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import platform.Foundation.NSNotificationCenter
import platform.UIKit.UIApplication
import platform.UIKit.UIApplicationDidBecomeActiveNotification
import platform.UIKit.UIApplicationDidEnterBackgroundNotification
import platform.UIKit.UIApplicationState

public class IosApplicationLifecycleProvider : ApplicationLifecycleProvider {
    private val _lifecycle: MutableStateFlow<LifecycleEvent> =
        MutableStateFlow(LifecycleEvent.AppInBackground)

    @OptIn(FlowPreview::class)
    override val lifecycle: Flow<LifecycleEvent> = _lifecycle.asStateFlow().debounce(500)

    init {
        // Observer for when app becomes active (foreground)
        NSNotificationCenter.defaultCenter.addObserverForName(
            name = UIApplicationDidBecomeActiveNotification,
            `object` = null,
            queue = null
        ) { _ ->
            _lifecycle.value = LifecycleEvent.AppInForeground
        }

        // Observer for when app enters background
        NSNotificationCenter.defaultCenter.addObserverForName(
            name = UIApplicationDidEnterBackgroundNotification,
            `object` = null,
            queue = null
        ) { _ ->
            _lifecycle.value = LifecycleEvent.AppInBackground
        }

        // Set initial state based on current app state
        val currentState = UIApplication.sharedApplication.applicationState
        _lifecycle.value = when (currentState) {
            UIApplicationState.UIApplicationStateActive -> LifecycleEvent.AppInForeground
            UIApplicationState.UIApplicationStateBackground,
            UIApplicationState.UIApplicationStateInactive -> LifecycleEvent.AppInBackground
            else -> LifecycleEvent.AppInBackground
        }
    }
}
