package com.ottogroup.appkit.base.file

import kotlinx.cinterop.ExperimentalForeignApi
import okio.Path
import okio.Path.Companion.toPath
import platform.Foundation.NSApplicationSupportDirectory
import platform.Foundation.NSCachesDirectory
import platform.Foundation.NSDocumentDirectory
import platform.Foundation.NSFileManager
import platform.Foundation.NSURL
import platform.Foundation.NSUserDomainMask

public class IosFileSystem : FileSystem() {
    override fun getCacheDir(): Path =
        NSFileManager.defaultManager.CachesDirectory!!.path!!.toPath()

    override fun getAppDataDir(): Path =
        NSFileManager.defaultManager.ApplicationSupportDirectory!!.path!!.toPath()

    override fun getDatabasePath(dbName: String): Path =
        NSFileManager.defaultManager.DocumentDirectory!!.path!!.toPath().resolve(dbName)
}

@OptIn(ExperimentalForeignApi::class)
internal val NSFileManager.ApplicationSupportDirectory: NSURL?
    get() = URLForDirectory(
        directory = NSApplicationSupportDirectory,
        appropriateForURL = null,
        create = true,
        inDomain = NSUserDomainMask,
        error = null
    )

@OptIn(ExperimentalForeignApi::class)
internal val NSFileManager.CachesDirectory: NSURL?
    get() = URLForDirectory(
        directory = NSCachesDirectory,
        appropriateForURL = null,
        create = true,
        inDomain = NSUserDomainMask,
        error = null
    )

@OptIn(ExperimentalForeignApi::class)
internal val NSFileManager.DocumentDirectory: NSURL?
    get() = URLForDirectory(
        directory = NSDocumentDirectory,
        appropriateForURL = null,
        create = true,
        inDomain = NSUserDomainMask,
        error = null
    )
