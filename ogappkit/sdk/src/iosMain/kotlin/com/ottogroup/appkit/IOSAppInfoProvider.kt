package com.ottogroup.appkit

import com.ottogroup.appkit.base.http.AppInfoProvider
import platform.Foundation.NSBundle

internal class IOSAppInfoProvider : AppInfoProvider {
    override val appName: String
        get() = NSBundle.mainBundle.infoDictionary?.get("CFBundleDisplayName") as? String
            ?: NSBundle.mainBundle.infoDictionary?.get("CFBundleName") as? String ?: "unknown"

    override val appVersionName: String
        get() = NSBundle.mainBundle.infoDictionary?.get("CFBundleShortVersionString") as? String
            ?: "unknown"

    override val appPackage: String
        get() = NSBundle.mainBundle.bundleIdentifier ?: "unknown"
}
