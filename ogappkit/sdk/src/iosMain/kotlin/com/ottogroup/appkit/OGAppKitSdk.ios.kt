package com.ottogroup.appkit

import com.ottogroup.appkit.base.file.FileSystem
import com.ottogroup.appkit.base.file.IosFileSystem
import com.ottogroup.appkit.base.http.AppInfoProvider
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import com.ottogroup.appkit.base.lifecycle.IosApplicationLifecycleProvider
import org.koin.core.module.Module
import org.koin.dsl.module

public object OGAppKitSdk : OGAppKit by OGAppKitImpl()

internal actual fun basePlatformModule(): Module = module {
    single<FileSystem> { IosFileSystem() }
    factory<AppInfoProvider> { IOSAppInfoProvider() }
    single<ApplicationLifecycleProvider> { IosApplicationLifecycleProvider() }
}
