package com.ottogroup.appkit

import android.app.Application
import android.content.Context
import com.ottogroup.appkit.base.file.AndroidFileSystem
import com.ottogroup.appkit.base.file.FileSystem
import com.ottogroup.appkit.base.http.AppInfoProvider
import com.ottogroup.appkit.base.lifecycle.AndroidApplicationLifecycleContributor
import com.ottogroup.appkit.base.lifecycle.ApplicationLifecycleProvider
import org.koin.android.ext.koin.androidContext
import org.koin.core.module.Module
import org.koin.dsl.bind
import org.koin.dsl.module

public object OGAppKitSdk {

    private lateinit var sharedInstance: OGAppKit

    public fun init(context: Context): OGAppKit {
        synchronized(this) {
            if (!OGAppKitSdk::sharedInstance.isInitialized) {
                val ogAppKitImpl = OGAppKitImpl()
                val applicationContext = context.applicationContext
                if (applicationContext is Application) {
                    ogAppKitImpl.getKoin()
                        .declare(applicationContext, secondaryTypes = listOf(Context::class))
                } else {
                    ogAppKitImpl.getKoin().declare(applicationContext)
                }
                sharedInstance = ogAppKitImpl
            }
        }
        return sharedInstance
    }

    public fun shared(): OGAppKit {
        require(OGAppKitSdk::sharedInstance.isInitialized) { "must call init in order to initialize" }
        return sharedInstance
    }
}

internal actual fun basePlatformModule(): Module = module {
    factory<AppInfoProvider> { AndroidAppInfoProvider(androidContext()) }
    single<FileSystem> { AndroidFileSystem(get()) }
    single(createdAtStart = true) { AndroidApplicationLifecycleContributor() } bind ApplicationLifecycleProvider::class
}
