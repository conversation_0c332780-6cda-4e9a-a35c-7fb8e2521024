package com.ottogroup.appkit

import android.content.Context
import androidx.startup.Initializer

public class OGAppKitInitializer : Initializer<OGAppKit?> {
    override fun create(context: Context): OGAppKit {
        return OGAppKitSdk.init(context)
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        // No dependencies on other libraries.
        return emptyList()
    }
}
