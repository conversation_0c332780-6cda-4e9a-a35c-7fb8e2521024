package com.ottogroup.appkit

import android.content.Context
import com.ottogroup.appkit.base.http.AppInfoProvider

internal class AndroidAppInfoProvider(context: Context) : AppInfoProvider {
    override val appName: String = context.applicationInfo.loadLabel(context.packageManager).toString()

    override val appVersionName: String = context.packageManager.getPackageInfo(
        context.packageName,
        0
    ).versionName ?: "unknown"

    override val appPackage: String = context.packageName
}
