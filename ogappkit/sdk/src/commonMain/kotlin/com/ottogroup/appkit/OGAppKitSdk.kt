package com.ottogroup.appkit

import com.ottogroup.appkit.base.datastore.jsonDataStore
import com.ottogroup.appkit.base.di.ApplicationJob
import com.ottogroup.appkit.base.di.ApplicationScope
import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.base.http.InstallationIdProvider
import com.ottogroup.appkit.base.http.OGAppKitHeaders
import com.ottogroup.appkit.base.util.InstallationId
import com.ottogroup.appkit.base.util.InstallationIdProviderImpl
import com.ottogroup.appkit.coordinator.OGCoordinator
import com.ottogroup.appkit.coordinator.coordinatorModule
import com.ottogroup.appkit.deals.OGDeals
import com.ottogroup.appkit.deals.dealsModule
import com.ottogroup.appkit.l10n.OGL10n
import com.ottogroup.appkit.l10n.l10nModule
import com.ottogroup.appkit.nativeui.OGNative
import com.ottogroup.appkit.nativeui.nativeModule
import com.ottogroup.appkit.resources.resourcePlatformModule
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.trackingModule
import com.ottogroup.appkit.tracking.trackingPlatformModule
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

internal class OGAppKitImpl : OGAppKit, InternalKoinComponent {
    init {
        getKoin().loadModules(listOf(baseModule, platformModule()), createEagerInstances = true)
    }

    override fun native(): OGNative {
        /* The native module internally performs tracking, so we need to ensure that the module is loaded, even if the
         * implementing client app does not explicitly set up tracking or if the order of module loading is different.
         */
        tracking()
        ensureModule(nativeModule)
        return getKoin().get<OGNative>()
    }

    override fun coordinator(): OGCoordinator {
        /* The coordinator module internally observers the tracking events, so we need to ensure that the module is loaded, even if the
         * implementing client app does not explicitly set up tracking or if the order of module loading is different.
         */
        tracking()
        ensureModule(coordinatorModule)
        return getKoin().get<OGCoordinator>()
    }

    override fun tracking(): OGTracking {
        ensureModule(trackingModule)
        return getKoin().get<OGTracking>()
    }

    override fun deals(): OGDeals {
        ensureModule(dealsModule)
        return getKoin().get<OGDeals>()
    }

    override fun l10n(): OGL10n {
        ensureModule(l10nModule)
        return getKoin().get<OGL10n>()
    }

    private val loadedModules: MutableList<Module> = mutableListOf()

    private fun ensureModule(module: Module) {
        if (loadedModules.contains(module)) return
        loadedModules.add(module)
        getKoin().loadModules(listOf(module), createEagerInstances = true)
    }
}

/**
 * The main entry point for the OGAppKit SDK.
 * This object is responsible for providing access to the various features of the SDK.
 */
public interface OGAppKit {

    /**
     * Provides access to the native module.
     * @see [OGNative]
     */
    public fun native(): OGNative

    /**
     * Provides access to the tracking module.
     * @see [OGTracking]
     */
    public fun tracking(): OGTracking

    /**
     * Provides access to the Deals module.
     * @see [OGDeals]
     */
    public fun deals(): OGDeals

    /**
     * Provides access to the L10n module.
     */
    public fun l10n(): OGL10n

    /**
     * Provides access to the Coordinator module.
     * * @see [OGCoordinator]
     */
    public fun coordinator(): OGCoordinator
}

private val baseModule = module {
    single<ApplicationJob> { SupervisorJob() }
    single<ApplicationScope> { CoroutineScope(get<ApplicationJob>()) }
    @OptIn(ExperimentalUuidApi::class)
    single(named<InstallationId>()) {
        jsonDataStore<InstallationId>(
            default = Uuid.NIL.toString(),
            fileName = "installationId.json",
            fileSystem = get()
        )
    }
    single<InstallationIdProvider> { InstallationIdProviderImpl(get(named<InstallationId>())) }
    factory<OGAppKitHeaders> {
        OGAppKitHeaders(
            get(),
            get()
        )
    }
}

internal fun platformModule(): Module = module {
    includes(
        basePlatformModule(),
        trackingPlatformModule(),
        resourcePlatformModule()
    )
}

internal expect fun basePlatformModule(): Module
