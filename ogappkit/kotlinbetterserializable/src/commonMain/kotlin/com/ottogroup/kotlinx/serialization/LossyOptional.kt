@file:Suppress("DEPRECATION")

package com.ottogroup.kotlinx.serialization

import kotlin.jvm.JvmInline
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlin.properties.ReadOnlyProperty

/**
 * Wrapper for optional values that returns `null` instead of throwing an exception if the value can't be deserialized.
 * The [LossyOptional] itself doesn't make the property optional. You need to add `?` to the property type, making it nullable.
 *
 * To use it, wrap the optional value with a [LossyOptional], ensure the name by [@SerialName][kotlinx.serialization.SerialName] and provide the property via delegation.
 *
 * ```kotlin
 * @Serializable
 * data class MyObject(
 *     val requiredProperty: String,
 *     @SerialName("optionalProperty") private val _optionalProperty: LossyOptional<String>?
 * ) {
 *     val optionalProperty by _optionalProperty.lossyProvider()
 * }
 * ```
 *
 * @see LossyOptionalSerializer
 */
@Deprecated(
    message = "We have a better alternative without wrapping the object. See `LossySerializer` and the _lossy_ primitives.",
    level = DeprecationLevel.WARNING
)
@JvmInline
@Serializable(with = LossyOptionalSerializer::class)
public value class LossyOptional<T : Any>(internal val content: T?) {
    override fun toString(): String = content.toString()
}

/**
 * Serializer for [LossyOptional] that, when the object can't be deserialized, returns `null` instead of throwing an error.
 *
 * @see LossyOptional
 */
@Deprecated(
    message = "We have a better alternative without wrapping the object. See `LossySerializer` and the _lossy_ primitives.",
    level = DeprecationLevel.WARNING
)
@OptIn(ExperimentalSerializationApi::class)
public open class LossyOptionalSerializer<T : Any>(private val dataSerializer: KSerializer<T>) :
    KSerializer<LossyOptional<T>> {
    override val descriptor: SerialDescriptor = dataSerializer.descriptor

    override fun serialize(encoder: Encoder, value: LossyOptional<T>) {
        if (value.content != null) {
            encoder.encodeNotNullMark()
            dataSerializer.serialize(encoder, value.content)
        } else {
            encoder.encodeNull()
        }
    }

    override fun deserialize(decoder: Decoder): LossyOptional<T> {
        val content: T? = if (decoder.decodeNotNullMark())
            try {
                dataSerializer.deserialize(decoder)
            } catch (e: Exception) {
                // TODO: Log the exception
                null
            }
        else
            decoder.decodeNull()
        return content.lossy()
    }
}

public fun <T : Any> T?.lossy(): LossyOptional<T> = LossyOptional(this)
public fun <T : Any> KSerializer<T>.lossySerializer(): LossyOptionalSerializer<T> = LossyOptionalSerializer(this)
public fun <T : Any> LossyOptional<T>?.lossyProvider(): ReadOnlyProperty<Any?, T?> = ReadOnlyProperty<Any?, T?> { _, _ -> this?.content }
