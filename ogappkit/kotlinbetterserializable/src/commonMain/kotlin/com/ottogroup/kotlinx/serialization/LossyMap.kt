package com.ottogroup.kotlinx.serialization

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.MapSerializer
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * A map that, when deserialized, discards invalid keys or values and keeps the valid ones.
 * If the map itself can't be parsed, it becomes `null`, so keep in mind the property is nullable.
 * See the `typealias` code.
 *
 * ```kotlin
 * @Serializable
 * data class MyObject(
 *     val myProperty: LossyMap<String, Int>,
 * )
 * ```
 *
 * @see LossyMapSerializer
 */
public typealias LossyMap<K, V> =
    @Serializable(with = LossyMapSerializer::class)
    Map<K, V>

public class LossyMapSerializer<K : Any, V : Any>(keySerializer: KSerializer<K>, valueSerializer: KSerializer<V>) :
    LossySerializer<Map<K?, V?>>(MapSerializer(LossySerializer(keySerializer), LossySerializer(valueSerializer))) {

    override fun serialize(encoder: Encoder, value: Map<K?, V?>?) {
        super.serialize(encoder, value?.filter { it.key != null && it.value != null })
    }

    override fun deserialize(decoder: Decoder): Map<K?, V?>? =
        super.deserialize(decoder)?.filter { it.key != null && it.value != null }
}
