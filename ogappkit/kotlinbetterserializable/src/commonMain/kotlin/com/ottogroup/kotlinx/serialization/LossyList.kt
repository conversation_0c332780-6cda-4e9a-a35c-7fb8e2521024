package com.ottogroup.kotlinx.serialization

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * A list that, when deserialized, discards invalid objects and keeps the valid ones.
 * If the list itself can't be parsed, it becomes `null`, so keep in mind the property is nullable.
 * See the `typealias` code.
 *
 * ```kotlin
 * @Serializable
 * data class MyObject(
 *     val myProperty: LossyList<String>,
 * )
 * ```
 *
 * @see LossyListSerializer
 */
public typealias LossyList<T> =
    @Serializable(with = LossyListSerializer::class)
    List<T>?

public class LossyListSerializer<T : Any>(dataSerializer: KSerializer<T>) :
    LossySerializer<List<T?>>(ListSerializer(LossySerializer(dataSerializer))) {

    override fun serialize(encoder: Encoder, value: List<T?>?) {
        super.serialize(encoder, value?.filterNotNull())
    }

    override fun deserialize(decoder: Decoder): List<T?>? =
        super.deserialize(decoder)?.filterNotNull()
}

public class LossyListSerializer2<T : Any>(dataSerializer: KSerializer<T>) : KSerializer<List<T>> {
    private val lossySerializer = LossyListSerializer(dataSerializer)
    override val descriptor: SerialDescriptor = lossySerializer.descriptor

    override fun serialize(encoder: Encoder, value: List<T>) {
        lossySerializer.serialize(encoder, value)
    }

    override fun deserialize(decoder: Decoder): List<T> {
        return lossySerializer.deserialize(decoder)?.filterNotNull() ?: emptyList()
    }
}
