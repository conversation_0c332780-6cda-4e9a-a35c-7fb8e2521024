package com.ottogroup.kotlinx.serialization

import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.serializer

/**
 * An optional [String] that becomes `null` if it can't be parsed, instead of throwing an exception.
 *
 * @see LossyStringSerializer
 */
public typealias LossyString =
    @Serializable(with = LossyStringSerializer::class)
    String

/**
 * The [KSerializer][kotlinx.serialization.KSerializer] for properties of type [String].
 *
 * @see LossyString
 */
public object LossyStringSerializer : LossySerializer<String>(String.serializer())

/**
 * An optional [Boolean] that becomes `null` if it can't be parsed, instead of throwing an exception.
 *
 * @see LossyBooleanSerializer
 */
public typealias LossyBoolean =
    @Serializable(with = LossyBooleanSerializer::class)
    Boolean

/**
 * The [KSerializer][kotlinx.serialization.KSerializer] for properties of type [Boolean].
 *
 * @see LossyBoolean
 */
public object LossyBooleanSerializer : LossySerializer<Boolean>(Boolean.serializer())

/**
 * An optional [Int] that becomes `null` if it can't be parsed, instead of throwing an exception.
 *
 * @see LossyIntSerializer
 */
public typealias LossyInt =
    @Serializable(with = LossyIntSerializer::class)
    Int

/**
 * The [KSerializer][kotlinx.serialization.KSerializer] for properties of type [Int].
 *
 * @see LossyInt
 */
public object LossyIntSerializer : LossySerializer<Int>(Int.serializer())

/**
 * An optional [Double] that becomes `null` if it can't be parsed, instead of throwing an exception.
 *
 * @see LossyDoubleSerializer
 */
public typealias LossyDouble =
    @Serializable(with = LossyDoubleSerializer::class)
    Double

/**
 * The [KSerializer][kotlinx.serialization.KSerializer] for properties of type [Double].
 *
 * @see LossyDouble
 */
public object LossyDoubleSerializer : LossySerializer<Double>(Double.serializer())

/**
 * An optional [Float] that becomes `null` if it can't be parsed, instead of throwing an exception.
 *
 * @see LossyFloatSerializer
 */
public typealias LossyFloat =
    @Serializable(with = LossyFloatSerializer::class)
    Float

/**
 * The [KSerializer][kotlinx.serialization.KSerializer] for properties of type [Float].
 *
 * @see LossyFloat
 */
public object LossyFloatSerializer : LossySerializer<Float>(Float.serializer())
