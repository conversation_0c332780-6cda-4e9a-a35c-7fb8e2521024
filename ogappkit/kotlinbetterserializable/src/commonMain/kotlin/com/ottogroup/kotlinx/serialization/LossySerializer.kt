package com.ottogroup.kotlinx.serialization

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * A [KSerializer][kotlinx.serialization.KSerializer] that tries to deserialize the object. If it fails, returns a
 * `null` value instead of throwing an exception. The [SerializationException] is available in the [exceptionHandler],
 * for example to log it.
 */
@OptIn(ExperimentalSerializationApi::class)
public open class LossySerializer<T : Any>(private val dataSerializer: KSerializer<T>) : KSerializer<T?> {

    override val descriptor: SerialDescriptor = dataSerializer.descriptor

    override fun serialize(encoder: Encoder, value: T?): Unit =
        encoder.encodeNullableSerializableValue(dataSerializer, value)

    override fun deserialize(decoder: Decoder): T? = try {
        decoder.decodeNullableSerializableValue(dataSerializer)
    } catch (e: SerializationException) {
        exceptionHandler(e)
        null
    }

    public open fun exceptionHandler(exception: SerializationException) {
        // No operation by default, but also not mandatory to override if it's not needed.
    }
}
