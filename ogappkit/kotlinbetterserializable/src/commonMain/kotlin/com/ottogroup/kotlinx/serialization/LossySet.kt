package com.ottogroup.kotlinx.serialization

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.SetSerializer
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * A set that, when deserialized, discards invalid objects and keeps the valid ones.
 * If the set itself can't be parsed, it becomes `null`, so keep in mind the property is nullable.
 * See the `typealias` code.
 *
 * ```kotlin
 * @Serializable
 * data class MyObject(
 *     val myProperty: LossySet<String>,
 * )
 * ```
 *
 * @see LossySetSerializer
 */
public typealias LossySet<T> =
    @Serializable(with = LossySetSerializer::class)
    Set<T>?

public class LossySetSerializer<T : Any>(dataSerializer: KSerializer<T>) :
    LossySerializer<Set<T?>>(SetSerializer(LossySerializer(dataSerializer))) {

    override fun serialize(encoder: Encoder, value: Set<T?>?) {
        super.serialize(encoder, value?.filterNotNull()?.toSet())
    }

    override fun deserialize(decoder: Decoder): Set<T?>? =
        super.deserialize(decoder)?.filterNotNull()?.toSet()
}
