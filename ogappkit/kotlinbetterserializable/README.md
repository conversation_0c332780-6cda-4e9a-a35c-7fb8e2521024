# kotlin-better-serializable

How to use the serializable module.

## Configure it

```build.gradle.kts
kotlin {
    sourceSets {
        commonMain.dependencies {
            implementation(project(":ogappkit:kotlinbetterserializable"))
        }
    }
}
```

## Using Lossy

This Lossy classes ensures that if some data is invalid, `LossySerializer` will deserialize it as a null, so these
classes are nullable.

### LossyList

From

```
"MyObject": {
    [
        "string",
        "string",
        "string"
    ]
}

```

to:

```
 @Serializable
 data class MyObject(
     val myProperty: LossyList<String>,
 )
```

---

If there is some invalid data, example:
```
"MyObject": {
    [
        "string",
        123,
        "string"
    ]
}
```

And LossyString has `String` as type:

```
 @Serializable
 data class MyObject(
     val myProperty: LossyList<String>,
 )
```

In this case:

```
 myObject = ["string", null, "string"]

 myObject.get(1) == null
```

### LossyMap
```
"MyObject": {
    "key": "value",
    "key": "value",
    "key": "value"
}

```

to:

```
@Serializable
data class MyObject(
    val myProperty: LossySet<String>,
)
```

---

If there is some invalid data, example:
```
"MyObject": {
    "key0" : "string",
    "key1" : 123,
    "key2" : "string",
}
```

And LossyMap has `String, String` as types:

```
 @Serializable
 data class MyObject(
     val myProperty: LossyMap<String, String>,
 )
```

In this case:

```
 myObject = {
    "key0" : "string",
    "key1" : null,
    "key2" : "string",
}

 myObject.get("key1") == null
```
