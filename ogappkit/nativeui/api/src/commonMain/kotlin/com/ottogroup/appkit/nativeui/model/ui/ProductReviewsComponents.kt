package com.ottogroup.appkit.nativeui.model.ui

import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.ProductRating.Config.Rounding
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

public data class ProductReviewsScreen(
    override val components: List<ProductReviewsComponent>
) : Screen<ProductReviewsComponent>()

public sealed interface ProductReviewsComponent : Component {
    public val config: ProductReviewsComponentConfig
}

@Serializable
public sealed interface ProductReviewsComponentConfig : ComponentConfig

public data class ReviewsInformation(
    val brandName: String?,
    val title: String,
    val rating: Rating?,
    val screenId: String,
    override val config: Config
) : ProductReviewsComponent {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val rounding: Rounding = Rounding.HALF,
    ) : ProductReviewsComponentConfig {

        public companion object {
            internal const val NAME: String = "ReviewsInformation"
        }
    }
}

public data class ReviewsSortingOptions(
    val options: List<SortingOption>,
    val screenId: String
) : ProductReviewsComponent {

    override val config: Config = Config

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductReviewsComponentConfig {
        const val NAME: String = "ReviewsSortingOptions"
    }

    public enum class SortingOption {
        RECENT,
        OLDEST,
        HIGHEST,
        LOWEST
    }
}

public data class ReviewsList(
    val reviews: List<Review>,
    val totalReviewsCount: Int,
    val screenId: String,
    override val config: Config
) : ProductReviewsComponent {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val reviewsPerPage: Int = 15,
    ) : ProductReviewsComponentConfig {

        public companion object {
            internal const val NAME: String = "ReviewsList"
        }
    }
}

public data class WriteReviewButton(
    val writeReviewUrl: String,
) : ProductReviewsComponent {
    override val config: Config = Config

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductReviewsComponentConfig {
        internal const val NAME: String = "WriteReviewButton"
    }
}
