package com.ottogroup.appkit.nativeui.model.ui

import com.ottogroup.appkit.base.DEMO
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions.ProductDimension.VariantLink
import com.ottogroup.appkit.nativeui.model.ui.ProductRating.Config.Rounding
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

public data class ProductDetailScreen(
    val screenId: String,
    override val components: List<ProductDetailComponent<*>>
) : Screen<ProductDetailComponent<*>>()

public sealed interface ProductDetailComponent<C : Any> : LoadingComponent<C> {
    public val config: ProductDetailComponentConfig
}

@Serializable
public sealed interface ProductDetailComponentConfig : ComponentConfig

public data class ProductGallery(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductGallery.Content> {
    override val config: Config = Config

    public data class Content(
        val images: List<Image>,
        val flags: List<Flag>,
        val isWishlisted: Boolean,
        val productIdForWishlisting: String,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        const val NAME: String = "ProductGallery"
    }
}

public data class ProductColorDimension(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductColorDimension.Content> {
    override val config: Config = Config

    public data class Content(
        /** This product's color display name. */
        val colorName: String,
        /** All colors this product (theoretically) comes in. */
        val colors: List<ColorLink>,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductColorDimension"
    }

    public data class ColorLink(
        /** Color's display name. */
        val colorName: String,
        /** ID of product to display when selecting this color. */
        val productId: String,
        /**
         * Preview of the color. May be one of several different types. May be null
         * if no preview is available.
         */
        val preview: Preview?,
        /**
         * Overall availability of the color.
         *
         * Its state is equivalent to the
         * "most available" state of the variants. E.g. will only be
         * [Availability.State.TEMPORARILY_OUT_OF_STOCK] if none of the variants
         * are in stock or pre-orderable.
         *
         * Its quantity is the sum of all variant quantities of this color.
         */
        val availability: Availability,
        /**
         * Whether this color link is the current product. Equivalent to comparing
         * [productId] to this product's ID.
         */
        val isSelected: Boolean,
    ) {
        public sealed class Preview {
            /** A thumbnail image. */
            public data class Thumbnail(val url: String) : Preview()

            // reserving the option to add different types of previews, such as solid colors (hex) or even video?
        }
    }
}

public data class ProductDimensions(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<ProductDimensions.Content> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val style: Style,
        val longTitleStyle: LongTitleStyle? = null,
    ) : ProductDetailComponentConfig {
        /** The desired style of dimensions. */
        public enum class Style {
            /**
             * A single list of variants in a single dimension. If the product has
             * multiple dimensions, they are fused into a flat list of all possible
             * variants. The returned data is identical to [FLATCHIPS] but it is to be
             * displayed differently.
             */
            FLAT,

            /**
             * A single list of variants in a single dimension. If the product has
             * multiple dimensions, they are fused into a flat list of all possible
             * variants. The returned data is identical to [FLAT] but it is to be
             * displayed differently.
             */
            FLATCHIPS,

            /**
             * A nested dimension structure of two dimensions. The returned data is
             * identical to [CATEGORIZED] but it is to be displayed differently.
             */
            NESTED,

            /**
             * A nested dimension structure of two dimensions. The returned data is
             * identical to [NESTED] but it is to be displayed differently.
             */
            CATEGORIZED,
        }

        @Serializable
        public data class LongTitleStyle(
            val characterLimit: Int,
            val style: Style
        )

        private companion object {
            const val NAME: String = "ProductDimensions"
        }
    }

    public sealed interface Content {
        public val isWishlisted: Boolean
        public val productIdForWishlisting: String
        public val sizeAdvisorUrl: String?
    }

    /** Wraps a single product dimension that contains a plain list of variants. */
    public data class FlatDimension(
        val dimension: ProductDimension,
        override val isWishlisted: Boolean,
        override val productIdForWishlisting: String,
        override val sizeAdvisorUrl: String?
    ) : Content

    /**
     * A nested dimension structure, in which the outer layer represents a
     * dimension whose entries each contain another dimension. This is used for
     * two-dimensional sizes, e.g. bra sizes, in which each cup size contains a
     * list of widths.
     */
    public data class NestedDimensions(
        val name: String,
        val entries: List<Entry>,
        override val isWishlisted: Boolean,
        override val productIdForWishlisting: String,
        override val sizeAdvisorUrl: String?
    ) : Content {
        public data class Entry(
            val name: String,
            val dimension: ProductDimension,
        )
    }

    public data class ProductDimension(
        /** This dimension's name. */
        val name: String,
        /** All variants that are available in this dimension. */
        val variants: List<VariantLink>,
    ) {
        public data class VariantLink(
            /** Variant's display name. */
            val name: String,
            /** ID of product to display when selecting this variant. */
            val productId: String,
            /** Availability of this variant. */
            val availability: Availability,
            /** The price of this variant. */
            val price: Price,
            /**
             * Whether this variant link is the current product. Equivalent to
             * comparing [productId] to this product's ID.
             */
            val isSelected: Boolean,
        )
    }

    public data class VoucherDimension(
        val dimension: ProductDimension,
        /** If non-null, allows customizing the name on the voucher. */
        val customName: CustomName?,
        override val isWishlisted: Boolean,
        override val productIdForWishlisting: String,
        override val sizeAdvisorUrl: String?
    ) : Content {
        public data class CustomName(
            val maxLength: Int = Int.MAX_VALUE
        )
    }
}

public data class ProductInformation(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductInformation.Content> {
    override val config: Config = Config

    public data class Content(
        val description: Description,
        val details: Details,
        val brand: Brand?,
        val importantInformation: ImportantInformation?,
        val articleStandards: ArticleStandards?,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductInformation"
    }

    public data class Description(
        val articleNumber: String,
        val bulletPoints: List<String>,
        val text: String,
        val documents: List<Link>,
    )

    public data class Details(
        val attributesTable: Information.AttributesTable,
    )

    public data class Brand(
        val name: String,
        val description: String?,
    )

    public data class ImportantInformation(
        val distributingCompanies: List<DistributingCompany>,
    ) {
        public data class DistributingCompany(
            val name: String,
            val data: String,
        )
    }
}

public data class ProductHeader(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductHeader.Content> {
    override val config: Config = Config

    public data class Content(
        val title: String,
        val brandName: String?,
        val sharingData: SharingData?,
        val isWishlisted: Boolean,
        /**
         * The ID with which the product can be added to the wishlist. This might be different from [productId].
         */
        val productIdForWishlisting: String,
        /**
         * The ID of the delivered product.
         */
        val productId: String,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductHeader"
    }

    public data class SharingData(
        val url: String,
    )
}

public data class ProductTitle(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductTitle.Content> {
    override val config: Config = Config

    public data class Content(
        val title: String,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductTitle"
    }
}

public data class ProductPrice(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<ProductPrice.Content> {

    public data class Content(
        val price: Price,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        /**
         * An optional URL that should be linked to in the legal text next to the
         * price, e.g. for shipping cost information.
         */
        val conditionsUrl: String?
    ) : ProductDetailComponentConfig {
        internal companion object {
            const val NAME: String = "ProductPrice"
        }
    }
}

public data class ProductRating(
    override val state: LoadingComponent.State<Content>,
    override val config: Config
) : ProductDetailComponent<ProductRating.Content> {

    public data class Content(
        val rating: Rating,
        val allReviewsUrl: String,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val showCount: Boolean = true,
        val rounding: Rounding = Rounding.HALF,
        val allReviewsUrl: String
    ) : ProductDetailComponentConfig {

        public enum class Rounding {
            WHOLE,
            HALF,
            NONE,
        }

        public companion object {
            private const val NAME: String = "ProductRating"
            public const val URL_PRODUCT_ID_PLACEHOLDER: String = "{productId}"
        }
    }
}

public data class ProductAvailability(
    override val state: LoadingComponent.State<Content>,
) : ProductDetailComponent<ProductAvailability.Content> {
    override val config: Config = Config

    public data class Content(
        val availability: Availability,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductAvailability"
    }
}

public data class ProductColor(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductColor.Content> {
    override val config: Config = Config

    public data class Content(
        val colorName: String
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductColor"
    }
}

public data class ProductVariant(
    override val state: LoadingComponent.State<Content>
) : ProductDetailComponent<ProductVariant.Content> {
    override val config: Config = Config

    public data class Content(
        val dimensionName: String,
        val variant: VariantLink,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "ProductVariant"
    }
}

public data class AddToBasketButton(
    override val state: LoadingComponent.State<Content>,
) : ProductDetailComponent<AddToBasketButton.Content> {
    override val config: Config = Config

    public sealed interface Content {
        public data class AddToBasket(val productId: String) : Content
        public data class NotifyMe(val url: String) : Content
    }

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : ProductDetailComponentConfig {
        internal const val NAME: String = "AddToBasketButton"
    }
}

public data class ShopUsps(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<ShopUsps.Content> {

    /** Most actual content is purely client-side (translations/assets). */
    public data class Content(
        val paybackPoints: Int?
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        /**
         * The index at which to insert the Payback points into the shop USPs,
         * if they exist. An index <= 0 indicates the first position, an
         * index >= #USPs indicates the last position.
         *
         * Defaults to the last position.
         */
        val paybackPointsIndex: Int = Int.MAX_VALUE,
    ) : ProductDetailComponentConfig {
        public companion object {
            private const val NAME: String = "ShopUsps"
        }
    }
}

public data class ProductReviews(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<ProductReviews.Content> {

    public sealed interface Content {
        public data class Empty(
            val writeReviewUrl: String,
        ) : Content

        public data class Reviews(
            val totalCount: Int,
            val rating: Rating,
            val reviews: List<Review>,
            val allReviewsUrl: String,
        ) : Content
    }

    /**
     * @param allReviewsUrl URL to show all reviews for this product. MUST
     *    contain a placeholder for the SDK to inject the required product ID:
     *    `{productId}`
     * @param ratingRounding The rounding strategy to use for the average
     *    rating.
     * @param reviewCount The number of reviews to show directly on the PDP.
     */
    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val allReviewsUrl: String,
        val ratingRounding: Rounding = Rounding.HALF,
        val reviewCount: Int = 6,
    ) : ProductDetailComponentConfig {
        public companion object {
            private const val NAME: String = "ProductReviews"
            public const val URL_PRODUCT_ID_PLACEHOLDER: String = "{productId}"
        }
    }
}

public sealed interface ProductRecommendationsConfig : ProductDetailComponentConfig, AddToBasketSuccessComponentConfig {
    public val titleL10n: String?
    public val subtitleL10n: String?
    public val maxEntries: Int
}

public sealed interface ProductRecommendations<C : ProductRecommendationsConfig> :
    LoadingComponent<ProductRecommendations.Content> {
    public data class Content(
        val products: List<RecommendedProduct>,
        val image: Image? = null,
        val trackingId: String,
        val title: String? = null,
    )

    public data class RecommendedProduct(
        val productId: String,
        val secondaryId: String?,
        val brandName: String?,
        val title: String,
        val price: Price?,
        val image: Image,
        val isWishlisted: Boolean,
        val productIdForWishlisting: String,
    )
}

public data class StaticProductRecommendations(
    override val state: LoadingComponent.State<ProductRecommendations.Content>,
    override val config: Config,
) : ProductDetailComponent<ProductRecommendations.Content>,
    AddToBasketSuccessComponent<ProductRecommendations.Content>,
    ProductRecommendations<StaticProductRecommendations.Config> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val productIds: List<String> = staticIds,
        override val titleL10n: String? = null,
        override val subtitleL10n: String? = null,
        override val maxEntries: Int = Int.MAX_VALUE,
    ) : ProductRecommendationsConfig {

        private companion object {
            const val NAME: String = "StaticProductRecommendations"

            @Deprecated(DEMO)
            val staticIds = listOf(
                "1612365037",
                "1507200093",
                "100728091",
                "1611639625",
                "100737681",
                "1612362022",
                "105905042",
                "1611648614",
                "993325866",
                "1341318429",
                "1219166285",
            )
        }
    }

    public companion object {
        public const val TRACKING_ID: String = "Static"
    }
}

public data class RecentlyViewedRecommendations(
    override val state: LoadingComponent.State<ProductRecommendations.Content>,
    override val config: Config,
) : ProductDetailComponent<ProductRecommendations.Content>,
    AddToBasketSuccessComponent<ProductRecommendations.Content>,
    ProductRecommendations<RecentlyViewedRecommendations.Config> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        override val titleL10n: String? = null,
        override val subtitleL10n: String? = null,
        override val maxEntries: Int = Int.MAX_VALUE,
    ) : ProductRecommendationsConfig {

        private companion object {
            const val NAME: String = "RecentlyViewedRecommendations"
        }
    }

    public companion object {
        public const val TRACKING_ID: String = "RecentlyViewed"
    }
}

public data class DynamicYieldRecommendations(
    override val state: LoadingComponent.State<ProductRecommendations.Content>,
    override val config: Config,
) : ProductDetailComponent<ProductRecommendations.Content>,
    AddToBasketSuccessComponent<ProductRecommendations.Content>,
    ProductRecommendations<DynamicYieldRecommendations.Config> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val selectorNames: List<String> = emptyList(),
        val selectorGroups: List<String> = emptyList(),
        override val titleL10n: String? = null,
        override val subtitleL10n: String? = null,
        override val maxEntries: Int = Int.MAX_VALUE,
    ) : ProductRecommendationsConfig {

        private companion object {
            const val NAME: String = "DynamicYieldRecommendations"
        }
    }
}

public data class ShopTheLookRecommendations(
    override val state: LoadingComponent.State<ProductRecommendations.Content>,
    override val config: Config,
) : ProductDetailComponent<ProductRecommendations.Content>,
    AddToBasketSuccessComponent<ProductRecommendations.Content>,
    ProductRecommendations<DynamicYieldRecommendations.Config> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        override val titleL10n: String? = null,
        override val subtitleL10n: String? = null,
        override val maxEntries: Int = Int.MAX_VALUE,
    ) : ProductRecommendationsConfig {

        private companion object {
            const val NAME: String = "ShopTheLookRecommendations"
        }
    }

    public companion object {
        public const val TRACKING_ID: String = "ShopTheLook"
    }
}

public data class MoreFromTheSeriesRecommendations(
    override val state: LoadingComponent.State<ProductRecommendations.Content>,
    override val config: Config,
) : ProductDetailComponent<ProductRecommendations.Content>,
    AddToBasketSuccessComponent<ProductRecommendations.Content>,
    ProductRecommendations<DynamicYieldRecommendations.Config> {

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        override val titleL10n: String? = null,
        override val subtitleL10n: String? = null,
        override val maxEntries: Int = Int.MAX_VALUE,
    ) : ProductRecommendationsConfig {

        private companion object {
            const val NAME: String = "MoreFromTheSeriesRecommendations"
        }
    }

    public companion object {
        public const val TRACKING_ID: String = "MoreFromTheSeries"
    }
}

public data class DynamicYieldBanner(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<DynamicYieldBanner.Content> {

    public data class Content(
        val text: String,
        val infoText: String?,
        val promoCode: String?,
        val trackingEvents: TrackingEvents,
    )

    public data class TrackingEvents(
        val view: View.ProductDetailPromotion,
        val click: Interaction.ProductDetailSelectPromotion,
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val selectorNames: List<String> = emptyList(),
        val selectorGroups: List<String> = emptyList(),
        /**
         * The ID of the "slot"/position in which this banner will be rendered. Relevant for tracking.
         */
        val slotId: String? = null,
    ) : ProductDetailComponentConfig {
        private companion object {
            const val NAME: String = "DynamicYieldBanner"
        }
    }
}
