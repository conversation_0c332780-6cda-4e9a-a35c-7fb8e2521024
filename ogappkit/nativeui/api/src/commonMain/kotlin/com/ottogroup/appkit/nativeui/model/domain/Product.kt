package com.ottogroup.appkit.nativeui.model.domain

import kotlinx.datetime.Instant

public data class Product(
    val id: String,
    val title: String,
    val shortTitle: String,
    val webShopUrl: String?,
    val parentId: String?,
    val images: List<Image>,
    val flags: List<Flag>,
    val brand: Brand?,
    val price: Price,
    /**
     * The list of product dimensions, with some server-side dimensions
     * possibly having been fused into a single dimension, depending
     * on the company-specific setup. This might be the same list as
     * [individualDimensions]. If fusing took place, this list contains exactly
     * two entries: the color dimension and the dimension fusing all other
     * dimensions into one.
     */
    val fusedDimensions: List<Dimension>,
    /**
     * The list of product dimensions, as we receive it from the server,
     * without anything being fused into a single dimension. This might be the
     * same list as [fusedDimensions].
     */
    val individualDimensions: List<Dimension>,
    /**
     * In products that have exactly two size dimensions, such as bras or
     * bikinis, this contains a matrix of all the available variants across
     * those two dimensions. This is required for UIs in which dimensions are
     * not switched one after the other, but where all options are offered at
     * once.
     *
     * This is null if the product does not have exactly two size dimensions.
     */
    val sizeMatrix: SizeDimensionMatrix?,
    val availability: Availability,
    val information: Information,
    val reviews: Reviews,
    val shopTheLook: ShopTheLook?,
    val moreFromTheSeries: MoreFromTheSeries?,
    val sizeAdvisorUrl: String? = null,
    val paybackPoints: Int? = null,

    /* Required for tracking */
    val sku: String = id,

    /**
     * If this product is a voucher, this contains details about the voucher
     * implementation.
     */
    val voucherSpec: VoucherSpec? = null,
    val breadcrumbs: List<String>,

    /**
     * Marks this product as a fake product used for optimistic state updates
     * when switching between products. The data of this product may be stale
     * or technically from a different product but can be used to display
     * something to the user that is probably right, while the actual product
     * is being fetched in the background.
     *
     * This flag can be used to selectively exclude certain UI components from
     * displaying data we know is not final.
     */
    val isOptimisticFake: Boolean = false,
)

public data class Image(
    val url: String,
    val thumbnailUrl: String?,
)

public data class Flag(
    val type: FlagType,
) {
    public enum class FlagType { SALE, NEW, MIX_MATCH, SET, MULTI_PACK }
}

public data class Brand(
    val name: String,
    val description: String?,
)

public data class Rating(
    val averageRating: Float,
    val count: Int,
    /**
     * Shows how often each rating option was given. Key is the rating option
     * (e.g. values from 1 to 5), value is a pair containing the number of
     * times that this option was picked and the relative percentage of that
     * rating.
     */
    val ratingDistribution: Map<Int, Pair<Int, Double>>,
)

public data class Price(
    val currency: String,
    val value: Int,
    val oldValue: Int? = null,
    val isStartPrice: Boolean = false,
) {
    public companion object {
        /**
         * Special marker price instance for products that do not truly have a
         * price. Since this is a rare exception and not actually intended for the
         * domain model, this is preferable to making the price nullable for all
         * products.
         */
        public val None: Price = Price("None", 0)
    }
}

public data class Dimension(
    val name: String,
    val type: DimensionType,
    val values: List<Value>,
) {
    public enum class DimensionType { COLOR, DEFAULT, UNKNOWN }

    public data class Value(
        val text: String,
        val productId: String,
        val thumbnailUrl: String?,
        val availability: Availability,
        val price: Price,
    )
}

public data class SizeDimensionMatrix(
    val parentDimensionName: String,
    val entries: List<Entry>,
) {
    public data class Entry(
        val name: String,
        val child: Dimension,
    )
}

public data class Availability(
    val state: State,
    val quantity: Int,
    val message: String? = null,
    val deliveryTime: DeliveryTime? = null,
    val notifyMeUrl: String? = null,
) {
    public companion object {
        public val Unknown: Availability = Availability(
            state = State.UNKNOWN,
            quantity = 0,
        )
    }

    public enum class State {
        IN_STOCK,
        LOW_STOCK,
        PRE_ORDERABLE,
        TEMPORARILY_OUT_OF_STOCK,
        PERMANENTLY_OUT_OF_STOCK,
        UNKNOWN,
    }

    public data class DeliveryTime(
        val range: IntRange,
        val timeUnit: TimeUnit,
    ) {
        public enum class TimeUnit {
            DAY,
            WEEK,
            MONTH,
        }
    }
}

public data class Information(
    val articleNumber: String,
    val description: String,
    val bulletPoints: List<String>,
    val attributesTable: AttributesTable,
    val distributingCompanies: List<DistributingCompany>,
    val documents: List<Link>,
    val articleStandards: ArticleStandards?,
) {
    public data class AttributesTable(
        val sections: List<Section>,
    ) {
        public data class Section(
            val title: String,
            val entries: List<Entry>,
        ) {
            public data class Entry(
                val key: String,
                val values: List<String>,
            )
        }
    }

    public data class DistributingCompany(
        val name: String,
        val address: String,
        val email: String?,
        val phone: String?,
        val url: String?,
    )
}

public data class Reviews(
    val rating: Rating?,
    val reviews: List<Review>,
    val writeReviewUrl: String,
)

public data class Review(
    val rating: Int,
    val text: String,
    val title: String? = null,
    val dateTime: Instant? = null,
    val reviewerName: String? = null,
)

public data class ShopTheLook(
    val image: Image,
    val articles: List<Product>,
)

public data class MoreFromTheSeries(
    val articles: List<Product>,
)

public data class Link(
    val text: String,
    val url: String,
)

public data class VoucherSpec(
    val allowsCustomName: Boolean,
    val maxCustomNameLength: Int = Int.MAX_VALUE,
)

public fun List<Dimension>.singleColorDimension(): Dimension? =
    singleOrNull { it.type == Dimension.DimensionType.COLOR }

public fun List<Dimension>.singleDefaultDimension(): Dimension? =
    singleOrNull { it.type == Dimension.DimensionType.DEFAULT }

public fun Product.singleColorDimension(): Dimension? = individualDimensions.singleColorDimension()

public fun Product.singleDefaultDimension(): Dimension? = fusedDimensions.singleDefaultDimension()
