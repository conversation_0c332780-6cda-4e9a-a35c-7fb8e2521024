package com.ottogroup.appkit.nativeui

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessScreen
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailScreen
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsScreen
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions
import kotlinx.coroutines.flow.Flow

/**
 * The main entry point to native functionality. Obtain an instance from
 * the `OGAppKitSdk` object.
 */
public interface OGNative {
    /**
     * Configures the native SDK. MUST be called before performing any other
     * operations.
     */
    public fun configure(config: OGNativeConfig)

    /**
     * Gets a filled product detail screen for the provided product [id]
     * according to the supplied configuration data structure.
     */
    public fun getProductDetailScreen(
        id: String,
        secondaryId: String?,
        componentConfigs: ComponentConfigs<ProductDetailComponentConfig>
    ): Flow<Result<ProductDetailScreen>>

    /**
     * Gets a filled product detail screen for the provided product [id]
     * according to the supplied configuration JSON string.
     */
    public fun getProductDetailScreen(id: String, secondaryId: String?, componentConfigsJson: String): Flow<Result<ProductDetailScreen>>

    /**
     * Updates the given screen with latest product data of the given ID.
     *
     * @param screenId The ID of the screen to update, received from
     *    [getProductDetailScreen].
     * @param productId The ID of the new product to display in the screen.
     * @param context Optional context to provide additional information about
     *    the request that is used during screen component creation.
     */
    public fun updateProductDetailScreen(
        screenId: String,
        productId: String,
        context: ProductDetailScreenRequestContext? = null
    )

    /**
     * Gets a filled product detail screen for the provided [url] according to
     * the supplied configuration JSON string.
     *
     * If the given [url] does not match the expected, configured format of
     * a product detail page, i.e. we cannot parse a product ID from it, a
     * failure will be returned.
     */
    public fun getProductDetailScreenByUrl(url: String, componentConfigsJson: String): Flow<Result<ProductDetailScreen>>

    /** Returns the current basket. */
    public suspend fun getBasket(): Result<Basket>

    /** Adds a product to the basket. */
    public suspend fun addProductToBasket(id: String): Result<Basket>

    /** Adds a gift voucher to the basket. */
    public suspend fun addVoucherToBasket(id: String, customName: String?): Result<Basket>

    /** Returns the current wishlist. */
    public suspend fun getWishlist(): Result<Wishlist>

    /** Adds a product to the wishlist. */
    public suspend fun addProductToWishlist(id: String): Result<Wishlist>

    /** Removes a product from the wishlist. */
    public suspend fun removeProductFromWishlist(id: String): Result<Wishlist>

    /**
     * Gets a filled product reviews screen for the provided product [id]
     * according to the supplied configuration data structure.
     */
    public fun getProductReviewsScreen(
        id: String,
        componentConfigs: ComponentConfigs<ProductReviewsComponentConfig>
    ): Flow<Result<ProductReviewsScreen>>

    /**
     * Gets a filled product reviews screen for the provided product [id]
     * according to the supplied configuration JSON string.
     */
    public fun getProductReviewsScreen(id: String, componentConfigsJson: String): Flow<Result<ProductReviewsScreen>>

    /**
     * Sorts the product reviews by the provided
     * [ReviewsSortingOptions.SortingOption].
     */
    public fun sortProductReviews(screenId: String, sortingOption: ReviewsSortingOptions.SortingOption)

    /** Filters the product reviews using the provided filter rating. */
    public fun filterProductReviews(screenId: String, filterRating: Int?)

    /** Filters the product reviews using the provided filter rating. */
    public fun showMoreReviews(screenId: String)

    /**
     * Gets a filled add-to-basket-success screen for the provided product [id]
     * according to the supplied configuration data structure.
     */
    public fun getAddToBasketSuccessScreen(
        id: String,
        componentConfigs: ComponentConfigs<AddToBasketSuccessComponentConfig>,
        productDetailScreenId: String? = null,
    ): Flow<Result<AddToBasketSuccessScreen>>

    /**
     * Gets a filled add-to-basket-success screen for the provided product [id]
     * according to the supplied configuration JSON string.
     */
    public fun getAddToBasketSuccessScreen(
        id: String,
        componentConfigsJson: String,
        productDetailScreenId: String? = null,
    ): Flow<Result<AddToBasketSuccessScreen>>
}

public interface ProductDetailScreenRequestContext {
    public data object FromColorSelection : ProductDetailScreenRequestContext
    public data object FromVariantSelection : ProductDetailScreenRequestContext
}
