package com.ottogroup.appkit.nativeui.model.ui

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

/**
 * Describes a screen as a list of UI components. A screen can only contain
 * components of a certain type, so that, for example, a product detail
 * screen can not, contain components of a basket screen.
 */
public abstract class Screen<C : Component> {
    public abstract val components: List<C>
}

/**
 * Component is a sealed type. There are a finite, knowable amount of
 * different components available that the SDK supports.
 */
public sealed interface Component

/**
 * A component that has a state meant to communicate the status of
 * asynchronous operations. This can be used to indicate a placeholder
 * component within an otherwise complete screen while the data for that
 * particular component is not yet available.
 */
public interface LoadingComponent<C : Any> : Component {
    public val state: State<C>

    public sealed interface State<out C : Any> {
        public data class Loading<C : Any>(val placeholderContent: C) : State<C>
        public data class Done<C : Any>(val content: C) : State<C>
    }
}

/** The common supertype of all component configuration types. */
@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator("name")
public interface ComponentConfig

/**
 * A holder for the configuration objects of all desired components of a
 * certain type.
 *
 * This type also provides a static method to parse component configuration
 * from a JSON string.
 */
@Serializable
public data class ComponentConfigs<C : ComponentConfig>(
    val components: List<C>
)
