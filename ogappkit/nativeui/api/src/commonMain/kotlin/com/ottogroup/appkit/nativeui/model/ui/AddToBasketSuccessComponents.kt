package com.ottogroup.appkit.nativeui.model.ui

import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

public data class AddToBasketSuccessScreen(
    override val components: List<AddToBasketSuccessComponent<*>>
) : Screen<AddToBasketSuccessComponent<*>>()

public sealed interface AddToBasketSuccessComponent<C : Any> : LoadingComponent<C> {
    public val config: AddToBasketSuccessComponentConfig
}

@Serializable
public sealed interface AddToBasketSuccessComponentConfig : ComponentConfig

public data class AddedProduct(
    override val state: LoadingComponent.State<Content>
) : AddToBasketSuccessComponent<AddedProduct.Content> {
    override val config: Config = Config

    public data class Content(
        val productId: String,
        val brandName: String?,
        val title: String,
        val selectedDimensionValues: List<SelectedDimensionValue>,
        val price: Price,
        val image: Image,
    ) {
        public data class SelectedDimensionValue(
            val dimensionName: String,
            val value: String,
        )
    }

    @Serializable
    @SerialName(Config.NAME)
    public data object Config : AddToBasketSuccessComponentConfig {
        const val NAME: String = "AddedProduct"
    }
}

public data class ShowBasketButton(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : AddToBasketSuccessComponent<ShowBasketButton.Content> {
    public data class Content(
        val basketUrl: String
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val basketUrl: String,
    ) : AddToBasketSuccessComponentConfig {
        private companion object {
            const val NAME: String = "ShowBasketButton"
        }
    }
}

public data class ContinueShoppingButton(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : AddToBasketSuccessComponent<ContinueShoppingButton.Content> {
    /**
     * @param continueShoppingUrl Optional URL to load when clicking the
     *    button. If `null`, the expectation is to simply close the
     *    AddToBasketSuccess screen.
     */
    public data class Content(
        val continueShoppingUrl: String?
    )

    @Serializable
    @SerialName(Config.NAME)
    public data class Config(
        val continueShoppingUrl: String? = null,
    ) : AddToBasketSuccessComponentConfig {
        private companion object {
            const val NAME: String = "ContinueShoppingButton"
        }
    }
}
