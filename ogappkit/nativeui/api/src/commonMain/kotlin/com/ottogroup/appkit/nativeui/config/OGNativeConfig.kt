package com.ottogroup.appkit.nativeui.config

import com.ottogroup.appkit.base.http.CookiesBridge
import io.ktor.util.encodeBase64

/**
 * The configuration required to be passed to the
 * [OGNative.configure][com.ottogroup.appkit.nativeui.OGNative.configure]
 * method before using the native SDK
 *
 * @param graphQLBackend The backend configuration for GraphQL queries. Its
 *    URL typically includes the `/graphql` path.
 * @param tenant The [NativeApiTenant] that specifies which data format the
 *    GraphQL API uses. This is NOT identical to the app's tenant. E.g. all
 *    Witt shop tenants likely use the same GraphQL spec.
 * @param productIdRegex Regex used to parse product ID(s) from a web shop
 *    URL. This regex MUST contain a capture group named `productId` and
 *    MAY contain a capture group named `variantId`.
 * @param dynamicYield Configuration for the Dynamic Yield tracking
 *    service. Must provide an API key and may override the backend URLs.
 * @param cookiesBridge A bridge for interfacing with the app's cookies.
 *    The SDK will read values as required for API requests and update
 *    cookies that come as part of API responses.
 */
public data class OGNativeConfig(
    val graphQLBackend: Backend = Backend(""),
    val restBackend: Backend? = null,
    val tenant: NativeApiTenant = NativeApiTenant.NONE,
    val productIdRegex: String = "",
    val dynamicYield: DynamicYield? = null,
    val cookiesBridge: CookiesBridge? = null,
    val debug: Boolean = false,
) {
    /**
     * Practically specifies which GraphQL schema and response data
     * transformation the native SDK should use.
     */
    public enum class NativeApiTenant {
        NONE,
        LASCANA
    }

    public data class Backend(
        val url: String,
        val headers: Map<String, String> = mapOf<String, String>(),
    ) {
        public constructor(url: String, basicAuthUser: String, basicAuthPassword: String) : this(
            url,
            mapOf(
                "Authorization" to "Basic " + "$basicAuthUser:$basicAuthPassword".encodeBase64()
            )
        )
    }

    /**
     * Configuration options for the Dynamic Yield feature components.
     *
     * @param trackPageViewUrl The API endpoint used for tracking page views
     *    and selecting campaigns.
     * @param apiKey The client-side API key for Dynamic Yield APIs.
     * @param cookiesUrl The URL to use for cookies set by Dynamic Yield.
     *    Typically, this would be the web shop's base URL, including the https
     *    scheme.
     */
    public data class DynamicYield(
        val trackPageViewUrl: String = "https://direct.dy-api.eu/v2/serve/user/choose",
        val apiKey: String,
        val cookiesUrl: String,
    )
}
