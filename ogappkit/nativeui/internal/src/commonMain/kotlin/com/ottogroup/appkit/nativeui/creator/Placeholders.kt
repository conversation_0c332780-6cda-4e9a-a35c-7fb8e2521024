package com.ottogroup.appkit.nativeui.creator

import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.AddedProduct
import com.ottogroup.appkit.nativeui.model.ui.AddedProduct.Content.SelectedDimensionValue
import com.ottogroup.appkit.nativeui.model.ui.ProductAvailability
import com.ottogroup.appkit.nativeui.model.ui.ProductColor
import com.ottogroup.appkit.nativeui.model.ui.ProductColorDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions.ProductDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductGallery
import com.ottogroup.appkit.nativeui.model.ui.ProductHeader
import com.ottogroup.appkit.nativeui.model.ui.ProductInformation
import com.ottogroup.appkit.nativeui.model.ui.ProductPrice
import com.ottogroup.appkit.nativeui.model.ui.ProductRating
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductReviews

internal object Placeholders {
    val productId = "123456"
    val secondaryId = "789012"

    val productTitle = "Product title"
    val brandName = "LASCANA"
    internal val dummyUrl = ""
    private val price = Price(
        currency = "EUR",
        value = 1000,
    )

    val rating = Rating(
        count = 100,
        averageRating = 4.5f,
        ratingDistribution = emptyMap(),
    )
    val colorNames = listOf(
        "lavender",
        "blue",
        "green",
        "red",
        "yellow",
        "black",
        "white",
    )

    val availability = Availability(
        message = stringWithLength(20),
        state = Availability.State.IN_STOCK,
        quantity = 100,
        deliveryTime = null,
    )

    val productHeader = ProductHeader.Content(
        title = productTitle,
        brandName = brandName,
        sharingData = null,
        isWishlisted = false,
        productIdForWishlisting = productId,
        productId = productId,
    )

    val productGallery = ProductGallery.Content(
        images = List(5) {
            Image(
                url = dummyUrl,
                thumbnailUrl = dummyUrl,
            )
        },
        flags = emptyList(),
        isWishlisted = false,
        productIdForWishlisting = productId,
    )

    val productColorDimension = ProductColorDimension.Content(
        colorName = colorNames.first(),
        colors = colorNames.map {
            ProductColorDimension.ColorLink(
                colorName = it,
                productId = productId,
                preview = ProductColorDimension.ColorLink.Preview.Thumbnail(dummyUrl),
                availability = availability,
                isSelected = it == colorNames.first(),
            )
        }
    )

    val productColor = ProductColor.Content(
        colorName = colorNames.first()
    )
    val productRating = ProductRating.Content(
        rating = rating,
        allReviewsUrl = dummyUrl,
    )

    val productPrice = ProductPrice.Content(price)

    val productAvailability = ProductAvailability.Content(
        availability = availability,
    )

    val productDimensions: ProductDimensions.Content = ProductDimensions.FlatDimension(
        dimension = ProductDimension(
            name = "Größe",
            variants = listOf(
                ProductDimension.VariantLink(
                    name = "S",
                    productId = productId,
                    availability = availability,
                    price = price,
                    isSelected = true,
                )
            )
        ),
        isWishlisted = false,
        productIdForWishlisting = productId,
        sizeAdvisorUrl = dummyUrl,
    )

    val productInformation = ProductInformation.Content(
        description = ProductInformation.Description(
            articleNumber = productId,
            bulletPoints = emptyList(),
            text = "",
            documents = emptyList(),
        ),
        details = ProductInformation.Details(
            attributesTable = Information.AttributesTable(
                sections = emptyList()
            )
        ),
        brand = ProductInformation.Brand(
            brandName,
            null,
        ),
        importantInformation = ProductInformation.ImportantInformation(
            distributingCompanies = listOf(
                ProductInformation.ImportantInformation.DistributingCompany(
                    name = stringWithLength(10),
                    data = List(3) { stringWithLength(20) }.joinToString("\n")
                )
            )
        ),
        articleStandards = null,
    )

    val productReviews = ProductReviews.Content.Reviews(
        totalCount = 100,
        rating = rating,
        reviews = List(2) {
            Review(
                rating = 5,
                text = stringWithLength(200),
            )
        },
        allReviewsUrl = dummyUrl,
    )

    private val recommendedProduct = ProductRecommendations.RecommendedProduct(
        productId = productId,
        secondaryId = secondaryId,
        brandName = brandName,
        title = productTitle,
        price = price,
        image = Image(
            url = dummyUrl,
            thumbnailUrl = dummyUrl,
        ),
        isWishlisted = false,
        productIdForWishlisting = productId,
    )
    val productRecommendations = ProductRecommendations.Content(
        products = List(6) { recommendedProduct },
        trackingId = "",
    )
    val shopTheLookRecommendations = productRecommendations.copy(
        image = Image(url = Placeholders.dummyUrl, thumbnailUrl = null)
    )

    val addedProduct = AddedProduct.Content(
        productId = productId,
        brandName = brandName,
        title = productTitle,
        selectedDimensionValues = listOf(
            SelectedDimensionValue("Farbe", colorNames.first()),
            SelectedDimensionValue("Größe", "S"),
        ),
        price = price,
        image = Image(
            url = dummyUrl,
            thumbnailUrl = dummyUrl,
        )
    )
}

private fun stringWithLength(length: Int) = "A".repeat(length)
