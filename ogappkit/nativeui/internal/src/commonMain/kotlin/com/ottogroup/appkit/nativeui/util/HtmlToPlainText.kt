package com.ottogroup.appkit.nativeui.util

import com.fleeksoft.ksoup.Ksoup
import com.fleeksoft.ksoup.nodes.Element
import com.fleeksoft.ksoup.nodes.Node
import com.fleeksoft.ksoup.nodes.TextNode
import com.fleeksoft.ksoup.select.NodeTraversor
import com.fleeksoft.ksoup.select.NodeVisitor

/**
 * Convert HTML input to lightly-formatted plain-text.
 *
 * Adapted from
 * https://github.com/jhy/jsoup/blob/master/src/main/java/org/jsoup/examples/HtmlToPlainText.java
 */
internal object HtmlToPlainText {

    fun stripHtml(text: String): String {
        return getPlainText(Ksoup.parse(text))
    }

    fun getPlainText(element: Element): String {
        val formatter: FormattingVisitor = FormattingVisitor()
        NodeTraversor.traverse(formatter, element)

        return formatter.toString()
    }

    private class FormattingVisitor : NodeVisitor {
        private val stringBuilder = StringBuilder()

        override fun head(node: Node, depth: Int) {
            val name = node.nodeName()
            when {
                node is TextNode -> append(node.text().replace("\\n", "\n"))
                name == "li" -> append("\n - ")
                name == "dt" -> append("  ")
                name in listOf("p", "h1", "h2", "h3", "h4", "h5", "tr") -> append("\n")
            }
        }

        override fun tail(node: Node, depth: Int) {
            val name = node.nodeName()
            if (name in listOf("br", "dd", "dt", "p", "h1", "h2", "h3", "h4", "h5")) {
                append("\n")
            }
        }

        private fun append(text: String) {
            // don't accumulate long runs of empty spaces and don't add leading spaces after line breaks
            val textToAppend = if (stringBuilder.isEmpty() || stringBuilder.last().isWhitespace()) {
                text.trimStart { it.category == CharCategory.SPACE_SEPARATOR }
            } else {
                text
            }
            stringBuilder.append(textToAppend)
        }

        override fun toString(): String {
            return stringBuilder.toString()
                .lines()
                .dropLastWhile { it.isBlank() }
                .joinToString("\n")
        }
    }
}

internal fun String.stripHtml() = HtmlToPlainText.stripHtml(this)
