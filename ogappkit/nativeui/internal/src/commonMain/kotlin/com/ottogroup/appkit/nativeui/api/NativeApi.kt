package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.lascana.LascanaNativeApi
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

internal interface NativeApiProvider {
    val nativeApi: NativeApi
}

/**
 * Provides the correct instance of [NativeApi] for the currently
 * configured [OGNativeConfig.NativeApiTenant].
 */
internal class NativeApiProviderImpl(
    private val configProvider: OGNativeConfigProvider,
    private val lascanaNativeApi: LascanaNativeApi,
) : NativeApiProvider {
    override val nativeApi: NativeApi
        get() = when (configProvider.configState.value.tenant) {
            OGNativeConfig.NativeApiTenant.NONE -> NopNativeApi
            OGNativeConfig.NativeApiTenant.LASCANA -> lascanaNativeApi
        }
}

/**
 * This interface must be implemented for each supported group company
 * backend. It abstracts the concrete data retrieval and conversion into
 * the common domain models.
 */
internal interface NativeApi {
    // The data these deliver can change over time, hence the use of Flow.
    fun getProduct(
        id: String,
        resolveVariant: Boolean = true,
        includeShopTheLook: Boolean = false,
        cachePolicy: CachePolicy = CachePolicy.CacheAndNetwork,
        overrideProductId: String? = null,
    ): Flow<Result<Product>>

    fun getMinimalProduct(id: String): Flow<Result<Product>>
    fun getReviews(id: String): Flow<Result<ProductReviews>>
    fun getWishlist(): Flow<Result<Wishlist>>
    fun getBasket(): Flow<Result<Basket>>

    // These are single-shot operations that do not need to be observed.
    suspend fun addProductToWishlist(id: String): Result<Wishlist>
    suspend fun removeProductFromWishlist(id: String): Result<Wishlist>
    suspend fun addProductToBasket(id: String): Result<Basket>
    suspend fun addVoucherToBasket(id: String, customName: String?): Result<Basket>

    enum class CachePolicy {
        /**
         * If a cached result exists, return it. Never do a network request.
         */
        CacheOnly,

        /**
         * If a cached result exists, return it. If not, do a network request.
         */
        CacheFirst,

        /**
         * If a cached result exists, return it first. Then do a network request
         * and return that result next.
         */
        CacheAndNetwork,
    }
}

private object NopNativeApi : NativeApi {
    override fun getProduct(
        id: String,
        resolveVariant: Boolean,
        includeShopTheLook: Boolean,
        cachePolicy: NativeApi.CachePolicy,
        overrideProductId: String?,
    ): Flow<Result<Product>> = nopFlow()

    override fun getMinimalProduct(id: String): Flow<Result<Product>> = nopFlow()
    override fun getReviews(id: String): Flow<Result<ProductReviews>> = nopFlow()
    override fun getWishlist(): Flow<Result<Wishlist>> = nopFlow()
    override fun getBasket(): Flow<Result<Basket>> = nopFlow()

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> = nopResult()
    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> = nopResult()
    override suspend fun addProductToBasket(id: String): Result<Basket> = nopResult()
    override suspend fun addVoucherToBasket(id: String, customName: String?): Result<Basket> = nopResult()

    private fun <T : Any> nopFlow(): Flow<Result<T>> = flowOf(Result.Failure(nopError))
    private fun <T : Any> nopResult(): Result<T> = Result.Failure(nopError)
    private val nopError = RuntimeException("Please configure OGNative with a tenant.")
}
