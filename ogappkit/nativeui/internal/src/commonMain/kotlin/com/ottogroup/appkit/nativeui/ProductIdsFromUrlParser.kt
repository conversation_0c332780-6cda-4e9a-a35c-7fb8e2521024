package com.ottogroup.appkit.nativeui

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.remember
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider

private const val PRODUCT_ID_GROUP_NAME = "productId"
private const val VARIANT_ID_GROUP_NAME = "variantId"

internal class ProductIdsFromUrlParser(
    configProvider: OGNativeConfigProvider,
) {
    private val regex by remember({ configProvider.configState.value.productIdRegex }) { Regex(it) }

    fun parse(url: String): Result<ProductIds> {
        return internalParse(url)?.let { Result.Success(it) }
            ?: run {
                Result.Failure(
                    IllegalArgumentException(
                        "Could not parse product IDs from URL ($url) using regex ($regex). " +
                            "Check the URL or the regex passed in the configuration."
                    )
                )
            }
    }

    private fun internalParse(url: String): ProductIds? {
        val matchResult = regex.matchEntire(url) ?: return null
        return try {
            val productId = matchResult.groups[PRODUCT_ID_GROUP_NAME]?.value ?: return null
            val variantId = try {
                matchResult.groups[VARIANT_ID_GROUP_NAME]?.value
            } catch (_: IllegalArgumentException) {
                null
            }
            ProductIds(productId, variantId)
        } catch (_: IllegalArgumentException) {
            null
        }
    }

    internal data class ProductIds(
        val productId: String,
        val variantId: String?,
    )
}
