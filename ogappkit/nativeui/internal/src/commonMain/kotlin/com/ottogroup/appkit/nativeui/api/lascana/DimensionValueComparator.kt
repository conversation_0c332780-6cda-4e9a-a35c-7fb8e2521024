package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.nativeui.model.domain.Dimension

internal object DimensionValueComparator : Comparator<Dimension.Value> {
    override fun compare(a: Dimension.Value, b: Dimension.Value): Int {
        return DimensionValueNameComparator.compare(a.text, b.text)
    }
}

internal object DimensionValueNameComparator : Comparator<String> {
    override fun compare(a: String, b: String): Int {
        val texts = a to b
        val sizes = Sizes(a, b)

        return with(sizes) {
            tryCompareAsUSSizes()
                ?: tryCompareAsNumericSizes()
                ?: tryCompareAsCombinedSizes()
                ?: tryCompareAsCupSizes()
                ?: tryCompareAsBraSizes()
                ?: tryCompareAsPrices()
                ?: tryCompareAsNumericPrefix()
                ?: compareAsStrings()
        }
    }

    private fun Sizes.tryCompareAsUSSizes(): Int? {
        val usPrefixes = map { inputSize -> usSizes.find { usSize -> inputSize.startsWith(usSize) } }
        if (usPrefixes.either { prefix -> prefix == null }) return null
        return usSizes.indexOf(usPrefixes.first) - usSizes.indexOf(usPrefixes.second)
    }

    private fun Sizes.tryCompareAsNumericSizes(): Int? {
        if (either { it.toIntOrNull() == null }) return null
        return first.toInt() - second.toInt()
    }

    private fun Sizes.tryCompareAsCombinedSizes(): Int? {
        val aMatch = combinedSizeRegex.matchEntire(first)
        val bMatch = combinedSizeRegex.matchEntire(second)
        if (aMatch == null || bMatch == null) return null
        val firstResult = compare(aMatch.groupValues[1], bMatch.groupValues[1])
        return if (firstResult != 0) {
            firstResult
        } else {
            compare(aMatch.groupValues[2], bMatch.groupValues[2])
        }
    }

    /**
     * Supported formats:
     * - Just cups: AA, A, B, ..., Z
     * - Cup with prefix: Cup AA, Cup A, Cup B, ..., Cup Z
     */
    private fun Sizes.tryCompareAsCupSizes(): Int? {
        val cups = map { it.removePrefix(CUP_PREFIX).trim() }
        if (cups.both { it in cupSizes }) {
            return cupSizes.indexOf(cups.first) - cupSizes.indexOf(cups.second)
        }
        return null
    }

    /**
     * Supported formats:
     * - EU-style full size: 70AA, 70A, 80B, 120F
     * - Full size with cup prefix, cup first: Cup AA 70, Cup A 70, Cup B 80,
     *   Cup F 120
     * - Full size with cup prefix, width first: 70 Cup AA, 80 Cup B, 120 Cup F
     */
    private fun Sizes.tryCompareAsBraSizes(): Int? {
        return tryCompareAsBraSizesByRegex(euBraSizeRegex)
            ?: tryCompareAsBraSizesByRegex(prefixedCupFirstSizeRegex)
            ?: tryCompareAsBraSizesByRegex(prefixedWidthFirstSizeRegex)
    }

    private fun Sizes.tryCompareAsBraSizesByRegex(regex: Regex): Int? {
        val euSizeMatches = map { regex.matchEntire(it) }
        if (euSizeMatches.either { it == null }) return null

        // we can !! here because otherwise the regex would not have matched
        val firstCup = euSizeMatches.first!!.groups[CUP_GROUP]!!.value
        val secondCup = euSizeMatches.second!!.groups[CUP_GROUP]!!.value
        val firstWidth = euSizeMatches.first!!.groups[WIDTH_GROUP]!!.value
        val secondWidth = euSizeMatches.second!!.groups[WIDTH_GROUP]!!.value
        val cupComparison = Sizes(firstCup, secondCup).tryCompareAsCupSizes() ?: return null
        return if (cupComparison != 0) {
            cupComparison
        } else {
            Sizes(firstWidth, secondWidth).tryCompareAsNumericSizes()
        }
    }

    private fun Sizes.tryCompareAsPrices(): Int? {
        val aMatch = priceRegex.matchEntire(first)
        val bMatch = priceRegex.matchEntire(second)
        if (aMatch == null || bMatch == null) return null
        return compare(aMatch.groupValues[1], bMatch.groupValues[1])
    }

    private fun Sizes.tryCompareAsNumericPrefix(): Int? {
        val aPrefix = first.takeWhile { it.isDigit() }.toIntOrNull()
        val bPrefix = second.takeWhile { it.isDigit() }.toIntOrNull()
        if (aPrefix == null || bPrefix == null) return null
        return aPrefix - bPrefix
    }

    private fun Sizes.compareAsStrings(): Int = first.compareTo(second)

    private val usSizes = listOf("XS", "S", "M", "L", "XL", "XXL")
    private val combinedSizeRegex = Regex("(\\d+)[-/](\\d+)")
    private const val CUP_PREFIX = "Cup"
    private val cupSizes = listOf("AA") + CharRange('A', 'Z').map { it.toString() } + listOf("ZZ", "ZZZ")
    private val priceRegex = Regex("(\\d+(?:\\.\\d+)?)\\s+\\S+")

    // IntelliJ doesn't like using a string constant as a group name. Ignore the error.
    private val euBraSizeRegex = Regex("(?<$WIDTH_GROUP>\\d{2,3})(?<$CUP_GROUP>[A-Z]{1,3})")
    private val prefixedCupFirstSizeRegex =
        Regex("$CUP_PREFIX\\s+(?<$CUP_GROUP>[A-Z]{1,3})\\s+(?<$WIDTH_GROUP>\\d{2,3})")
    private val prefixedWidthFirstSizeRegex =
        Regex("(?<$WIDTH_GROUP>\\d{2,3})\\s+$CUP_PREFIX\\s+(?<$CUP_GROUP>[A-Z]{1,3})")
    private const val WIDTH_GROUP = "width"
    private const val CUP_GROUP = "cup"
}

private typealias Sizes = Pair<String, String>

private fun <T> Pair<T, T>.both(predicate: (T) -> Boolean): Boolean = predicate(first) && predicate(second)
private fun <T> Pair<T, T>.either(predicate: (T) -> Boolean): Boolean = predicate(first) || predicate(second)
private fun <T, R> Pair<T, T>.map(transform: (T) -> R): Pair<R, R> = transform(first) to transform(second)
