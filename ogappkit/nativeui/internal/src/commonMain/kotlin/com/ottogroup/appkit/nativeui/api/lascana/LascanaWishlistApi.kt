package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import io.ktor.client.call.body
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import kotlinx.serialization.Serializable

internal interface LascanaWishlistApi {
    suspend fun getWishlist(): Result<Wishlist>
    suspend fun addToWishlist(id: String): Result<Wishlist>
    suspend fun removeFromWishlist(id: String): Result<Wishlist>
}

/**
 * API for interacting with the Lascana wishlist.
 *
 * This class expects the value of the `restBackend` field in the
 * `OGNativeConfig` to be set to the host + `/api` but excluding further
 * paths segments, such as `graphql` or the name of the individual
 * operation endpoint.
 */
internal class LascanaWishlistApiImpl(
    private val ogNativeConfigProvider: OGNativeConfigProvider,
    private val httpClientProvider: LascanaHttpClientProvider,
) : LascanaWishlistApi {
    private val client get() = httpClientProvider.httpClient

    override suspend fun getWishlist(): Result<Wishlist> {
        return resultFor {
            val response = client.get {
                setupRequest(ogNativeConfigProvider, "getWishlist")
            }
            response.body<WishlistResponse>().toWishlist()
        }
    }

    override suspend fun addToWishlist(id: String): Result<Wishlist> {
        return resultFor {
            val response = client.post {
                setupRequest(ogNativeConfigProvider, "addToWishlist")
                setBody("{\"articleId\":\"$id\"}")
            }
            response.body<WishlistResponse>().toWishlist()
        }
    }

    override suspend fun removeFromWishlist(id: String): Result<Wishlist> {
        return resultFor {
            val response = client.delete {
                setupRequest(ogNativeConfigProvider, "addToWishlist")
                setBody("{\"articleId\":\"$id\"}")
            }
            response.body<WishlistResponse>().toWishlist()
        }
    }
}

@Serializable
private data class WishlistResponse(
    val wishlistItems: List<WishlistItem>
) {

    @Serializable
    /**
     * [parentId] may be empty, in which case [articleId] is already the ID of
     * a parent product.
     */
    data class WishlistItem(
        val articleId: String,
        val parentId: String,
    )
}

private fun WishlistResponse.toWishlist(): Wishlist {
    return Wishlist(
        wishlistItems.map {
            Wishlist.Item(it.articleId)
        }
    )
}
