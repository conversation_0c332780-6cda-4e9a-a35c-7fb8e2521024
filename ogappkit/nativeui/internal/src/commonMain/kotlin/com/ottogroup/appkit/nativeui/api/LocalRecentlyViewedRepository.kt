package com.ottogroup.appkit.nativeui.api

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

internal class LocalRecentlyViewedRepository {

    private val _recentlyViewed = MutableStateFlow<List<String>>(emptyList())
    val recentlyViewed: StateFlow<List<String>> = _recentlyViewed.asStateFlow()

    fun addToRecentlyViewed(id: String) {
        _recentlyViewed.update { oldList ->
            listOf(id) + oldList.filter { productId -> productId != id }
        }
    }
}
