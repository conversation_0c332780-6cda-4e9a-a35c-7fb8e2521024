package com.ottogroup.appkit.nativeui.creator

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.Result.Failure
import com.ottogroup.appkit.base.Result.Success
import com.ottogroup.appkit.base.awaitResult
import com.ottogroup.appkit.base.withInitialValue
import com.ottogroup.appkit.nativeui.api.RecommendationsRepository
import com.ottogroup.appkit.nativeui.creator.productdetail.createProductRecommendations
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Dimension.DimensionType
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponent
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessScreen
import com.ottogroup.appkit.nativeui.model.ui.AddedProduct
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ContinueShoppingButton
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendationsConfig
import com.ottogroup.appkit.nativeui.model.ui.ShowBasketButton
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.shareIn

@OptIn(ExperimentalCoroutinesApi::class)
internal class AddToBasketSuccessScreenCreator(
    private val recommendationsRepository: RecommendationsRepository,
) {

    fun createScreen(
        productFlow: Flow<Operation<Product>>,
        componentConfigs: ComponentConfigs<AddToBasketSuccessComponentConfig>,
    ): Flow<Result<AddToBasketSuccessScreen>> {
        return channelFlow {
            // wait until we have a product result
            val productResultFlow = productFlow.awaitResult()

            productResultFlow
                .onEach {
                    if (it is Failure) {
                        send(Failure<AddToBasketSuccessScreen>(it.failure))
                    }
                }
                .filterIsInstance<Success<Product>>()
                .map { it.value }
                // ensure everything above is executed only once, even though below we subscribe to the flow multiple times
                .shareIn(scope = this, SharingStarted.Lazily)
                .createComponents(componentConfigs)
                .collect { it ->
                    send(Success(it))
                }
        }
    }

    private fun Flow<Product>.createComponents(
        componentConfigs: ComponentConfigs<AddToBasketSuccessComponentConfig>,
    ): Flow<AddToBasketSuccessScreen> {
        val filledInComponents = componentConfigs.components.map { config ->
            @Suppress("UNCHECKED_CAST")
            when (config) {
                AddedProduct.Config -> createAddedProduct()
                is ProductRecommendationsConfig ->
                    createProductRecommendations(
                        config,
                        recommendationsRepository
                    ) as Flow<AddToBasketSuccessComponent<*>>

                is ContinueShoppingButton.Config -> createContinueShoppingButton(config)
                is ShowBasketButton.Config -> createShowBasketButton(config)
            }
        }
        return combine(filledInComponents) { values ->
            AddToBasketSuccessScreen(values.filterNotNull())
        }.distinctUntilChanged()
    }

    private fun Flow<Product>.createAddedProduct(): Flow<AddedProduct> {
        data class ProductLens(
            val id: String,
            val shortTitle: String,
            val brandName: String?,
            val fusedDimensions: List<Dimension>,
            val individualDimensions: List<Dimension>,
            val price: Price,
            val images: List<Image>,
            val voucherSpec: VoucherSpec?,
        )
        return mapWithLens(
            mapping = {
                ProductLens(
                    id = it.id,
                    shortTitle = it.shortTitle,
                    brandName = it.brand?.name,
                    fusedDimensions = it.fusedDimensions,
                    individualDimensions = it.individualDimensions,
                    price = it.price,
                    images = it.images,
                    voucherSpec = it.voucherSpec,
                )
            }
        ) { lens ->
            val dimensions = if (lens.voucherSpec != null) {
                lens.individualDimensions.filter { it.type != DimensionType.COLOR }
            } else {
                lens.fusedDimensions
            }
            LoadingComponent.State.Done(
                AddedProduct.Content(
                    productId = lens.id,
                    brandName = lens.brandName,
                    title = lens.shortTitle,
                    selectedDimensionValues = dimensions.mapNotNull { dimension ->
                        dimension.values.find { it.productId == lens.id }?.let { dimensionValue ->
                            AddedProduct.Content.SelectedDimensionValue(
                                dimensionName = dimension.name,
                                value = dimensionValue.text
                            )
                        }
                    },
                    price = lens.price,
                    image = lens.images.firstOrNull() ?: Image("no image provided", "no image provided"),
                )
            )
        }.withInitialValue(LoadingComponent.State.Loading(Placeholders.addedProduct))
            .map { AddedProduct(it) }
    }

    private fun Flow<Product>.createContinueShoppingButton(
        config: ContinueShoppingButton.Config
    ): Flow<ContinueShoppingButton> {
        return mapWithLens(
            mapping = {
                // Not depending on any product attributes, as of now.
            }
        ) { lens ->
            LoadingComponent.State.Done(ContinueShoppingButton.Content(config.continueShoppingUrl))
        }.withInitialValue(LoadingComponent.State.Loading(ContinueShoppingButton.Content(null)))
            .map { ContinueShoppingButton(it, config) }
    }

    private fun Flow<Product>.createShowBasketButton(
        config: ShowBasketButton.Config
    ): Flow<ShowBasketButton> {
        return mapWithLens(
            mapping = {
                // Not depending on any product attributes, as of now.
            }
        ) { lens ->
            LoadingComponent.State.Done(ShowBasketButton.Content(config.basketUrl))
        }.withInitialValue(LoadingComponent.State.Loading(ShowBasketButton.Content("")))
            .map { ShowBasketButton(it, config) }
    }
}
