package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.base.combine
import com.ottogroup.appkit.base.combineOrEmpty
import com.ottogroup.appkit.base.getOrElse
import com.ottogroup.appkit.base.getOrNull
import com.ottogroup.appkit.nativeui.api.dynamicyield.DynamicYieldRepository
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.MoreFromTheSeriesRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendationsConfig
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

internal interface RecommendationsRepository {
    fun getRecentlyViewedRecommendations(
        productParentId: String,
        config: ProductRecommendationsConfig,
    ): Flow<Operation<ProductRecommendations.Content>>

    fun getStaticRecommendations(
        config: StaticProductRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>

    fun getDynamicYieldRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>

    fun getShopTheLookRecommendations(
        shopTheLook: ShopTheLook?,
        config: ShopTheLookRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>

    fun getMoreFromTheSeriesRecommendations(
        moreFromTheSeries: MoreFromTheSeries?,
        config: MoreFromTheSeriesRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>
}

internal class RecommendationsRepositoryImpl(
    private val nativeApiProvider: NativeApiProvider,
    private val localRecentlyViewedRepository: LocalRecentlyViewedRepository,
    private val dynamicYieldRepository: DynamicYieldRepository,
    private val wishlistRepository: WishlistRepository,
) : RecommendationsRepository {

    override fun getRecentlyViewedRecommendations(
        productParentId: String,
        config: ProductRecommendationsConfig,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return loadProducts(
            localRecentlyViewedRepository.recentlyViewed.value.filter {
                it != productParentId
            },
            config,
            trackingId = RecentlyViewedRecommendations.TRACKING_ID,
        )
    }

    override fun getStaticRecommendations(
        config: StaticProductRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return loadProducts(config.productIds, config, StaticProductRecommendations.TRACKING_ID)
    }

    override fun getDynamicYieldRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return dynamicYieldRepository.getRecommendations(productSku, config)
    }

    override fun getShopTheLookRecommendations(
        shopTheLook: ShopTheLook?,
        config: ShopTheLookRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        if (shopTheLook == null) {
            return flowOf(
                Result.Failure<ProductRecommendations.Content>(
                    IllegalArgumentException("Product does not have shop the look data.")
                )
            ).asOperation()
        }

        return shopTheLook.articles.take(config.maxEntries).map { product ->
            wishlistRepository
                .isProductOnWishlist(product.id)
                .map { wishlistResult ->
                    product.toRecommendedProduct(wishlistResult.getOrElse(false))
                }
        }.combine().map {
            Result.Success<ProductRecommendations.Content>(
                ProductRecommendations.Content(
                    products = it,
                    image = shopTheLook.image,
                    trackingId = ShopTheLookRecommendations.TRACKING_ID,
                )
            )
        }.asOperation()
    }

    override fun getMoreFromTheSeriesRecommendations(
        moreFromTheSeries: MoreFromTheSeries?,
        config: MoreFromTheSeriesRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        if (moreFromTheSeries == null) {
            return flowOf(
                Result.Failure<ProductRecommendations.Content>(
                    IllegalArgumentException("Product does not have more from the series data.")
                )
            ).asOperation()
        }

        return moreFromTheSeries.articles.take(config.maxEntries).map { product ->
            wishlistRepository
                .isProductOnWishlist(product.id)
                .map { wishlistResult ->
                    product.toRecommendedProduct(wishlistResult.getOrElse(false))
                }
        }.combine().map {
            Result.Success<ProductRecommendations.Content>(
                ProductRecommendations.Content(
                    products = it,
                    trackingId = ShopTheLookRecommendations.TRACKING_ID,
                )
            )
        }.asOperation()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun loadProducts(
        productIds: List<String>,
        config: ProductRecommendationsConfig,
        trackingId: String,
    ): Flow<Operation<ProductRecommendations.Content>> {
        val productFlows = productIds.take(config.maxEntries).map { id ->
            nativeApiProvider.nativeApi
                .getMinimalProduct(id)
        }

        return productFlows.combineOrEmpty().flatMapLatest { results ->
            val loadedProducts = results.mapNotNull { it.getOrNull() }
            if (loadedProducts.isEmpty()) {
                flowOf(emptyList())
            } else {
                loadedProducts.map { product ->
                    wishlistRepository
                        .isProductOnWishlist(product.id)
                        .map { wishlistResult ->
                            product.toRecommendedProduct(wishlistResult.getOrElse(false))
                        }
                }.combine()
            }
        }.map {
            // This can only be a success. At worst case, it's an empty list.
            Result.Success(ProductRecommendations.Content(products = it, trackingId = trackingId))
        }.asOperation()
    }
}

internal fun Product.toRecommendedProduct(isWishlisted: Boolean): ProductRecommendations.RecommendedProduct {
    return ProductRecommendations.RecommendedProduct(
        productId = id,
        secondaryId = null,
        brandName = brand?.name,
        title = title,
        price = price.takeIf { it != Price.None },
        image = images.firstOrNull() ?: Image("no image provided", "no image provided"),
        isWishlisted = isWishlisted,
        productIdForWishlisting = id,
    )
}
