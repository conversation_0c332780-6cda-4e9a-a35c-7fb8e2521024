package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.nativeui.util.safeParentId
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

internal interface ProductLoader {
    fun load(
        originalProductId: String,
        id: String,
        secondaryId: String?,
        includeShopTheLook: Boolean,
        onLoadSuccess: (Product) -> Unit = {},
    ): Flow<Operation<Product>>
}

internal class ProductLoaderImpl(
    private val nativeApiProvider: NativeApiProvider,
    private val localRecentlyViewedRepository: LocalRecentlyViewedRepository,
) : ProductLoader {
    @OptIn(ExperimentalCoroutinesApi::class)
    override fun load(
        originalProductId: String,
        id: String,
        secondaryId: String?,
        includeShopTheLook: Boolean,
        onLoadSuccess: (Product) -> Unit,
    ): Flow<Operation<Product>> {
        val originalProductFlow = if (id == originalProductId) {
            null
        } else {
            NativeLogger.v("ProductScreensRepository Re-requesting origin product $originalProductId")
            nativeApiProvider.nativeApi.getProduct(
                id = originalProductId,
                includeShopTheLook = includeShopTheLook,
                cachePolicy = NativeApi.CachePolicy.CacheOnly,
                overrideProductId = id,
            ).map { result ->
                NativeLogger.v("ProductScreensRepository Origin product result $originalProductId. Success: ${result is com.ottogroup.appkit.base.Result.Success}")
                result.map { product -> product.copy(isOptimisticFake = true) }
            }
        }

        val newProductFlow =
            nativeApiProvider.nativeApi.getProduct(id, includeShopTheLook = includeShopTheLook).flatMapLatest { result ->
                when (result) {
                    is Result.Success -> {
                        NativeLogger.v("ProductScreensRepository New product result ${result.value.id}. Success: true")
                        onLoadSuccess(result.value)
                        localRecentlyViewedRepository.addToRecentlyViewed(result.value.safeParentId)
                        flowOf(result)
                    }
                    is Result.Failure -> {
                        if (secondaryId != null) {
                            NativeLogger.w("Original product $id not found, trying secondary id $secondaryId")
                            nativeApiProvider.nativeApi.getProduct(secondaryId, includeShopTheLook = includeShopTheLook).onEach { secondaryResult ->
                                when (secondaryResult) {
                                    is com.ottogroup.appkit.base.Result.Success -> {
                                        NativeLogger.v("ProductScreensRepository New product result ${secondaryResult.value.id}. Success: true")
                                        onLoadSuccess(secondaryResult.value)
                                        localRecentlyViewedRepository.addToRecentlyViewed(secondaryResult.value.safeParentId)
                                    }
                                    is Result.Failure -> {
                                        NativeLogger.e("Failed to get product with both primary id ($id) and secondary id ($secondaryId): ${secondaryResult.failure.message}")
                                    }
                                }
                            }
                        } else {
                            flowOf(result)
                        }
                    }
                }
            }

        return channelFlow {
            val originalJob = originalProductFlow?.let { f ->
                launch {
                    f.collect {
                        /* Speculative fix for a possible race condition:
                         * Cache emits a value, and we reach inside collect.
                         * Then network emits a value, we cancel the job, emit the network value,
                         * then resume this coroutine and emit the cached value over the network value.
                         * This seems very unlikely, perhaps even impossible, since send should never suspend due to
                         * using a buffered channel, and there should not be a switch to the other coroutine happening
                         * here.
                         */
                        coroutineContext.ensureActive()
                        send(it)
                    }
                }
            }
            newProductFlow.collect {
                originalJob?.cancel()
                send(it)
            }
        }.asOperation()
    }
}
