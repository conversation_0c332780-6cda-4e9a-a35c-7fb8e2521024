package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

internal interface WishlistRepository {
    val wishlist: Flow<Result<Wishlist>>
    suspend fun addProductToWishlist(id: String): Result<Wishlist>
    suspend fun removeProductFromWishlist(id: String): Result<Wishlist>
}

/**
 * A repository providing access to the wishlist and allowing to observe
 * changes to it.
 */
internal class WishlistRepositoryImpl(
    private val nativeApiProvider: NativeApiProvider,
    coroutineScope: CoroutineScope,
) : WishlistRepository {

    private val nativeApi get() = nativeApiProvider.nativeApi

    override val wishlist by lazy {
        // lazy, because initially there is no native API until SDK initialization happens
        nativeApi.getWishlist()
            .filterIsInstance<Result.Success<Wishlist>>()
            .stateIn(
                coroutineScope,
                SharingStarted.Eagerly,
                Result.Success(Wishlist(emptyList()))
            )
    }

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        return nativeApi.addProductToWishlist(id)
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        return nativeApi.removeProductFromWishlist(id)
    }
}

internal fun WishlistRepository.isProductOnWishlist(parentId: String): Flow<Result<Boolean>> {
    return wishlist.map {
        it.map { wishlist ->
            wishlist.items.any { it.id == parentId }
        }
    }
}
