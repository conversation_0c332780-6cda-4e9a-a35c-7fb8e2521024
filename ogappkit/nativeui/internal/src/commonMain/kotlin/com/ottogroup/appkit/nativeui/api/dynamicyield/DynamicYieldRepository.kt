package com.ottogroup.appkit.nativeui.api.dynamicyield

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.base.combineOrEmpty
import com.ottogroup.appkit.base.getOrElse
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.nativeui.DY_ALTERNATIVE_ID_PARAMETER
import com.ottogroup.appkit.nativeui.ProductIdsFromUrlParser
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.api.isProductOnWishlist
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProvider
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.tracking.event.CustomParameter
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent
import com.ottogroup.appkit.tracking.services.dynamicyield.api.DynamicYieldNetworkDataSource
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.ProductData
import com.ottogroup.appkit.tracking.services.dynamicyield.from
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.decodeFromJsonElement

internal interface DynamicYieldRepository {
    fun getRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>

    fun getBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldBanner.Config,
    ): Flow<Operation<DynamicYieldBanner.Content>>
}

internal class DynamicYieldRepositoryImpl(
    private val dynamicYieldNetworkDataSource: DynamicYieldNetworkDataSource,
    private val wishlistRepository: WishlistRepository,
    private val nativeTrackingProvider: NativeTrackingProvider,
    private val productIdsFromUrlParser: ProductIdsFromUrlParser,
) : DynamicYieldRepository {
    /**
     * This function assumes that the provided [config] only returns at most
     * one choice of type recommendation. If there are multiple recommendation
     * choices contained in the DY response, the first one is selected.
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return choicesFlow(
            productSku,
            config.selectorNames,
            config.selectorGroups,
        ).flatMapLatest { result ->
            when (result) {
                is Result.Failure -> flowOf(Result.Failure(result.failure))
                is Result.Success -> {
                    result.value
                        .filterIsInstance<Choice.RecommendationsChoice>()
                        .firstOrNull()
                        ?.variations?.firstOrNull()
                        ?.payload?.data?.slots.orEmpty()
                        .map { slot ->
                            val productData = slot.productData
                            val ids = productIdsFromUrlParser.parse(productData.url)

                            val id = when (ids) {
                                is Result.Success -> ids.value.variantId ?: ids.value.productId
                                is Result.Failure -> return@flatMapLatest flowOf(Result.Failure(ids.failure))
                            }

                            val secondaryId = if (id == ids.value.variantId) ids.value.productId else null
                            wishlistRepository
                                .isProductOnWishlist(id)
                                .map { wishlistResult ->
                                    productData.toRecommendedProduct(
                                        isWishlisted = wishlistResult.getOrElse(false),
                                        productId = id,
                                        secondaryId = secondaryId
                                    )
                                }
                        }
                        .combineOrEmpty()
                        .map {
                            Result.Success(
                                ProductRecommendations.Content(
                                    products = it,
                                    trackingId = (config.selectorGroups + config.selectorNames).joinToString("_")
                                )
                            )
                        }
                }
            }
        }.asOperation()
    }

    override fun getBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldBanner.Config
    ): Flow<Operation<DynamicYieldBanner.Content>> {
        return choicesFlow(
            productSku,
            config.selectorNames,
            config.selectorGroups
        ).map { result ->
            when (result) {
                is Result.Failure -> Result.Failure(result.failure)
                is Result.Success -> {
                    val variation = result.value
                        .filterIsInstance<Choice.CustomChoice>()
                        .firstOrNull()
                        ?.variations?.firstOrNull()
                    val data = variation
                        ?.payload?.data
                        ?: return@map Result.Failure(
                            IllegalStateException(
                                "Failed to get custom JSON choice from from Dynamic Yield for SKU $productSku " +
                                    "and config $config."
                            )
                        )

                    val parsedData = lenientJson.decodeFromJsonElement<BannerData>(data)
                    val text = buildString {
                        parsedData.headline?.let {
                            append("$it: ")
                        }
                        parsedData.text?.let(::append)
                    }

                    val eCommerceItemWithCoupon = eCommerceItem.copy(coupon = parsedData.discountValue)

                    val viewEvent = nativeTrackingProvider.nativeTracking.getViewPromotionEvent(
                        item = eCommerceItemWithCoupon,
                        creativeSlot = config.slotId,
                        promotionId = variation.id,
                        promotionName = parsedData.headline
                    )
                    val clickEvent = nativeTrackingProvider.nativeTracking.getSelectPromotionEvent(
                        item = eCommerceItemWithCoupon,
                        creativeSlot = config.slotId,
                        promotionId = variation.id,
                        promotionName = parsedData.headline
                    )

                    Result.Success(
                        DynamicYieldBanner.Content(
                            text = text,
                            infoText = parsedData.discountText,
                            promoCode = parsedData.discountValue,
                            trackingEvents = DynamicYieldBanner.TrackingEvents(
                                view = viewEvent,
                                click = clickEvent,
                            )
                        )
                    )
                }
            }
        }.asOperation()
    }

    private fun choicesFlow(
        productSku: String,
        selectorNames: List<String>,
        selectorGroups: List<String>,
    ): Flow<Result<List<Choice>>> = flow {
        val variationsResult = dynamicYieldNetworkDataSource.chooseVariations(
            /* The whole dance with the alternativ ID parameter is technically not needed here,
             * but done for consistency with normal item tracking. */
            event = DynamicYieldEvent.from(
                event = View.ProductDetailViewItem(
                    ECommerceItem(
                        name = "",
                        id = "",
                        additionalParameters = mapOf(DY_ALTERNATIVE_ID_PARAMETER to CustomParameter(productSku))
                    )
                ),
                itemIdAlternativeParameter = DY_ALTERNATIVE_ID_PARAMETER,
            ),
            selectorNames = selectorNames,
            selectorGroups = selectorGroups,
        )

        if (variationsResult is Result.Failure) {
            NativeLogger.w(variationsResult.failure) {
                "Failed to get result from Dynamic Yield for SKU $productSku, " +
                    "selectorNames $selectorNames, selectorGroups $selectorGroups."
            }
        }

        emit(variationsResult)
    }
}

private fun ProductData.toRecommendedProduct(isWishlisted: Boolean, productId: String, secondaryId: String?) =
    ProductRecommendations.RecommendedProduct(
        productId = productId,
        secondaryId = secondaryId,
        brandName = brand,
        title = name,
        image = Image(
            url = imageUrl,
            thumbnailUrl = imageUrl,
        ),
        price = Price(
            // Not returned from DY. Probably safe to assume EUR for now.
            currency = "EUR",
            value = (price * 100).toInt(),
        ),
        isWishlisted = isWishlisted,
        productIdForWishlisting = productId,
    )

@Serializable
private data class BannerData(
    val headline: String?,
    val text: String?,
    val discountText: String?,
    val discountValue: String?,
)
