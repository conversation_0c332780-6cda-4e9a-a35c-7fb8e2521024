package com.ottogroup.appkit.nativeui.creator

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map

/**
 * [mapping] should create a "lens" object of the product that contains all
 * the relevant data for the component. As long as the lens object does not
 * change, the component will not be recreated, even if other properties of
 * the product do change.
 */
internal fun <Input, Lens, Component> Flow<Input>.mapWithLens(mapping: (Input) -> Lens, map: (Lens) -> Component): Flow<Component> =
    map { mapping(it) }.distinctUntilChanged().map(map)

/**
 * Version of [mapWithLens] that creates components based on a flow
 * returned from [flatMapLatest].
 */
@OptIn(ExperimentalCoroutinesApi::class)
internal fun <Input, Lens, Component> Flow<Input>.flatMapWithLens(mapping: (Input) -> Lens, flatMapLatest: (Lens) -> Flow<Component>): Flow<Component> =
    map { mapping(it) }.distinctUntilChanged().flatMapLatest(flatMapLatest)

internal fun <T, U> Flow<T?>.mapOrNull(transform: (T) -> U?): Flow<U?> = map { it?.let(transform) }
