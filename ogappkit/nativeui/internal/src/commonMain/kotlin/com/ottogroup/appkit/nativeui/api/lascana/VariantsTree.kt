package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.head
import com.ottogroup.appkit.base.tail
import com.ottogroup.appkit.nativeui.util.NativeLogger

/**
 * A perhaps too complicated decision tree implementation holding a product
 * hierarchy's available variants and how to reach them by selecting their
 * variant values. The tree returns neighboring products, if the exact
 * match does not exist.
 *
 * Consider a bra product with the variant labels "Color", "Cup Size",
 * "Band Size". It might have these variants:
 * - a variant with ID "123" has the variant labels: "Blue", "Cup A", "75"
 * - a variant with ID "456" has the variant labels: "Blue", "Cup B", "75"
 * - a variant with ID "789" has the variant labels: "Blue", "Cup C", "80"
 *
 * The first variant can be retrieved by querying "Blue", "Cup A", "75".
 * Switching to the second variant is possible by replacing "Cup A" with
 * "Cup B". The interesting part is that even substituting "Cup C" yields a
 * result, even though the requested size "75" does not exist for "Cup C".
 * In this case, the tree falls back to the closest possible value.
 */
internal class VariantsTree {

    private val root = Node.Root()

    fun insert(variantValues: List<String>, variantId: String) {
        root.insert(variantValues + variantId)
    }

    /**
     * Finds the ID of the variant found by traversing the tree according to
     * [variantValues]. [variantValues] must be a complete list of values to
     * select to reach a Variant ID.
     */
    fun findVariantIdFor(variantValues: List<String>): String? {
        fun traverse(node: Node, variantValues: List<String>): Node? {
            when (node) {
                is Node.VariantId -> return node
                is Node.WithChildren -> {
                    if (variantValues.isEmpty()) {
                        // we are at the end of the variant labels. There must be a single VariantId child
                        val child = node.children.entries.firstOrNull()?.value ?: return null
                        if (child !is Node.VariantId) {
                            NativeLogger.e("Did not reach VariantId leaf after traversing all of $variantValues.")
                            return null
                        }
                        return child
                    }

                    val head = variantValues.head
                    val tail = variantValues.tail

                    val maybeChild = node.children[head]
                    if (maybeChild != null) {
                        return traverse(maybeChild, tail)
                    }

                    val closestMatch = findClosestNeighbor(node.children.keys, head)
                    return traverse(node.children[closestMatch]!!, tail)
                }
            }
        }

        return when (val found = traverse(root, variantValues)) {
            is Node.VariantId -> found.id
            else -> null
        }
    }

    /**
     * Returns the next level's existing dimension values after traversing the
     * tree according to [variantValues].
     */
    fun findDimensionValuesFor(variantValues: List<String>): List<String> {
        fun traverse(node: Node, variantValues: List<String>): Node? {
            when (node) {
                is Node.VariantId -> return null
                is Node.WithChildren -> {
                    if (variantValues.isEmpty()) {
                        return node
                    }

                    val head = variantValues.head
                    val tail = variantValues.tail

                    val maybeChild = node.children[head] ?: return null
                    return traverse(maybeChild, tail)
                }
            }
        }

        return when (val found = traverse(root, variantValues)) {
            is Node.WithChildren -> found.children.keys.toList()
            else -> emptyList()
        }
    }

    override fun toString(): String {
        fun toString(node: Node, indent: Int = 0): String {
            return buildString {
                appendLine(" ".repeat(indent) + node)
                if (node is Node.WithChildren) {
                    node.children.forEach {
                        append(toString(it.value, indent + 2))
                    }
                }
            }
        }
        return toString(root)
    }

    sealed interface Node {
        sealed class WithChildren : Node {
            val children: MutableMap<String, Node> = mutableMapOf()

            fun insert(values: List<String>) {
                if (values.isEmpty()) return

                val head = values.head
                val tail = values.tail

                if (tail.isEmpty()) {
                    children[head] = VariantId(head)
                    return
                }

                val foo = children.getOrPut(head) {
                    DimensionValue(value = head)
                } as? DimensionValue
                foo?.insert(tail)
            }
        }

        class Root : WithChildren() {
            override fun toString(): String {
                return "Root"
            }
        }

        data class DimensionValue(val value: String) : WithChildren()
        data class VariantId(val id: String) : Node
    }
}

/**
 * Attempts to find [element] in the list of [options]. If it is not
 * contained, returns one of the closest neighboring values according to
 * the elements' natural sorting order. Returns `null` if [options] is
 * empty.
 */
private fun <T : Comparable<T>> findClosestNeighbor(options: Collection<T>, element: T): T? {
    if (options.isEmpty()) return null
    if (element in options) return element
    val all = (options + element).sorted()
    val elementIndex = all.indexOf(element).coerceIn(options.indices)
    return options.sorted()[elementIndex]
}
