package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions.SortingOption
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

internal class ProductReviewsRepository(private val reviewsPerPage: Int) {

    private val _productReviews = MutableStateFlow<List<Review>>(emptyList())
    private val _displayedProductReviews = MutableStateFlow<List<Review>>(emptyList())
    val displayedProductReviews: StateFlow<List<Review>> = _displayedProductReviews.asStateFlow()

    private var currentFilter: Int? = null
    private var currentSorting = SortingOption.RECENT
    private var currentPage = 1

    private var _totalReviewsCount = 0
    internal val totalReviewsCount: Int
        get() = _totalReviewsCount

    fun setProductReviews(reviews: List<Review>) {
        _productReviews.value = reviews
        applyCurrentSortingAndFiltering()
    }

    fun sortProductReviews(sortingOption: SortingOption) {
        currentSorting = sortingOption
        currentPage = 1
        applyCurrentSortingAndFiltering()
    }

    fun filterProductReviews(filterRating: Int?) {
        currentFilter = filterRating
        currentPage = 1
        applyCurrentSortingAndFiltering()
    }

    fun showMoreReviews() {
        currentPage++
        applyCurrentSortingAndFiltering()
    }

    private fun applyCurrentSortingAndFiltering() {
        var reviews = _productReviews.value

        currentFilter?.let { filterRating ->
            reviews = reviews.filter { it.rating == filterRating }
        }

        reviews = when (currentSorting) {
            SortingOption.RECENT -> reviews.sortedByDescending { it.dateTime }
            SortingOption.OLDEST -> reviews.sortedBy { it.dateTime }
            SortingOption.HIGHEST -> reviews.sortedByDescending { it.rating }
            SortingOption.LOWEST -> reviews.sortedBy { it.rating }
        }
        _totalReviewsCount = reviews.size

        val totalReviewsToDisplay = currentPage * reviewsPerPage
        _displayedProductReviews.value = reviews.take(totalReviewsToDisplay)
    }
}
