package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.util.NativeLogger
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.api.ClientPlugin
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultTransformers
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.http.appendPathSegments
import io.ktor.http.encodedPath
import io.ktor.http.takeFrom
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.ExperimentalSerializationApi
import io.ktor.client.plugins.logging.Logger as KtorLogger

internal interface LascanaHttpClientProvider {
    val httpClient: HttpClient
}

internal class LascanaHttpClientProviderImpl(
    ogNativeConfigProvider: OGNativeConfigProvider,
    customEngine: HttpClientEngine? = null
) : LascanaHttpClientProvider {
    override val httpClient: HttpClient = lascanaHttpClient(
        plugins = listOf(cookiesPlugin(ogNativeConfigProvider)),
        customEngine = customEngine,
    )
}

/**
 * Provides a ktor HttpClient and allows overriding the engine for testing
 * purposes.
 */
@OptIn(ExperimentalSerializationApi::class)
internal fun lascanaHttpClient(plugins: List<ClientPlugin<*>>, customEngine: HttpClientEngine? = null): HttpClient {
    val config: HttpClientConfig<*>.() -> Unit = {
        install(Logging) {
            logger = object : KtorLogger {
                override fun log(message: String) {
                    NativeLogger.d { message }
                }
            }
            level = LogLevel.INFO
        }
        install(ContentNegotiation) {
            json(lenientJson)
        }
        // see https://youtrack.jetbrains.com/issue/KTOR-5616/Ktor-always-adds-by-default-an-Accept-Charset-header#focus=Comments-27-6924404.0-0
        useDefaultTransformers = false

        plugins.forEach {
            install(it)
        }
    }

    return if (customEngine != null) {
        HttpClient(customEngine, config)
    } else {
        // let ktor pick the right engine for the platform
        HttpClient(config)
    }.apply {
        // see https://youtrack.jetbrains.com/issue/KTOR-5616/Ktor-always-adds-by-default-an-Accept-Charset-header#focus=Comments-27-6924404.0-0
        defaultTransformers()
    }
}

internal fun HttpRequestBuilder.setupRequest(ogNativeConfigProvider: OGNativeConfigProvider, path: String) {
    val restBackend = requireNotNull(ogNativeConfigProvider.configState.value.restBackend) {
        "Missing restBackend in OGNativeConfig"
    }
    url {
        takeFrom(restBackend.url)
        appendPathSegments("graphql", path)
        if (!encodedPath.endsWith("/")) {
            encodedPath += "/"
        }
    }
    restBackend.headers.forEach { (key, value) ->
        header(key, value)
    }
}
