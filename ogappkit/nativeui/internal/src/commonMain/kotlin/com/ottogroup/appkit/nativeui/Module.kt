package com.ottogroup.appkit.nativeui

import com.apollographql.apollo.api.Query
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.nativeui.api.ApolloClientFactory
import com.ottogroup.appkit.nativeui.api.ApolloProvider
import com.ottogroup.appkit.nativeui.api.ApolloProviderImpl
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.ApolloRepositoryImpl
import com.ottogroup.appkit.nativeui.api.LocalRecentlyViewedRepository
import com.ottogroup.appkit.nativeui.api.NativeApiProvider
import com.ottogroup.appkit.nativeui.api.NativeApiProviderImpl
import com.ottogroup.appkit.nativeui.api.ProductLoader
import com.ottogroup.appkit.nativeui.api.ProductLoaderImpl
import com.ottogroup.appkit.nativeui.api.ProductScreensRepository
import com.ottogroup.appkit.nativeui.api.RateLimiter
import com.ottogroup.appkit.nativeui.api.RecommendationsRepository
import com.ottogroup.appkit.nativeui.api.RecommendationsRepositoryImpl
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.api.WishlistRepositoryImpl
import com.ottogroup.appkit.nativeui.api.dynamicyield.DynamicYieldRepository
import com.ottogroup.appkit.nativeui.api.dynamicyield.DynamicYieldRepositoryImpl
import com.ottogroup.appkit.nativeui.api.lascana.LascanaBasketApi
import com.ottogroup.appkit.nativeui.api.lascana.LascanaBasketApiImpl
import com.ottogroup.appkit.nativeui.api.lascana.LascanaHttpClientProvider
import com.ottogroup.appkit.nativeui.api.lascana.LascanaHttpClientProviderImpl
import com.ottogroup.appkit.nativeui.api.lascana.LascanaNativeApi
import com.ottogroup.appkit.nativeui.api.lascana.LascanaNativeApiImpl
import com.ottogroup.appkit.nativeui.api.lascana.LascanaWishlistApi
import com.ottogroup.appkit.nativeui.api.lascana.LascanaWishlistApiImpl
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.creator.AddToBasketSuccessScreenCreator
import com.ottogroup.appkit.nativeui.creator.ProductReviewsScreenCreator
import com.ottogroup.appkit.nativeui.creator.productdetail.ProductDetailScreenCreator
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProvider
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProviderImpl
import com.ottogroup.appkit.nativeui.tracking.lascana.LascanaNativeTracking
import org.koin.core.module.Module
import org.koin.dsl.module

public val nativeModule: Module = module {
    single { OGNativeConfigProvider() }
    factory<ApolloProvider> { ApolloProviderImpl(get()) }
    factory { ApolloClientFactory(get()) }
    scope<OGNativeConfig> {
        scoped { get<ApolloClientFactory>().createApolloClient() }
    }
    single { RateLimiter<Query<*>>() }
    factory<ApolloRepository> { ApolloRepositoryImpl(get(), get()) }
    single<LascanaHttpClientProvider> { LascanaHttpClientProviderImpl(get()) }
    single<LascanaWishlistApi> { LascanaWishlistApiImpl(get(), get()) }
    single<LascanaBasketApi> { LascanaBasketApiImpl(get(), get()) }
    single<LascanaNativeApi> { LascanaNativeApiImpl(get(), get(), get(), getCoroutineScope()) }
    single<NativeApiProvider> { NativeApiProviderImpl(get(), get()) }
    single { LascanaNativeTracking(get(), get(), getCoroutineScope()) }
    single<NativeTrackingProvider> { NativeTrackingProviderImpl(get(), get()) }
    single { ProductIdsFromUrlParser(get()) }

    single<ProductLoader> { ProductLoaderImpl(get(), get()) }
    single { ProductScreensRepository(get()) }
    factory { ProductDetailScreenCreator(get(), get(), get(), get()) }
    factory { ProductReviewsScreenCreator() }
    factory { AddToBasketSuccessScreenCreator(get()) }

    single<WishlistRepository> { WishlistRepositoryImpl(get(), getCoroutineScope()) }
    single { LocalRecentlyViewedRepository() }
    single<DynamicYieldRepository> { DynamicYieldRepositoryImpl(get(), get(), get(), get()) }
    single<RecommendationsRepository> { RecommendationsRepositoryImpl(get(), get(), get(), get()) }

    single<OGNative> {
        OGNativeImpl(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
}
