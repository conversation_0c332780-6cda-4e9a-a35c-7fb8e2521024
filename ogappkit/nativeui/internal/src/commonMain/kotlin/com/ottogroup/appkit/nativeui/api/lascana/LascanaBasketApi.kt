package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.BasketError
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

internal interface LascanaBasketApi {
    suspend fun getBasket(): Result<Basket>
    suspend fun addToBasket(id: String, giftCardName: String? = null): Result<Basket>
}

/**
 * API for interacting with the Lascana Basket.
 *
 * This class expects the value of the `restBackend` field in the
 * `OGNativeConfig` to be set to the host + `/api` but excluding further
 * paths segments, such as `graphql` or the name of the individual
 * operation endpoint.
 */
internal class LascanaBasketApiImpl(
    private val ogNativeConfigProvider: OGNativeConfigProvider,
    private val httpClientProvider: LascanaHttpClientProvider,
) : LascanaBasketApi {
    private val client get() = httpClientProvider.httpClient

    override suspend fun getBasket(): Result<Basket> {
        return resultFor {
            val response = client.get {
                setupRequest(ogNativeConfigProvider, "getBasket")
            }
            response.body<BasketResponse>().toBasket()
        }
    }

    override suspend fun addToBasket(id: String, giftCardName: String?): Result<Basket> {
        return resultFor {
            val response = client.post {
                setupRequest(ogNativeConfigProvider, "addToBasket")
                contentType(ContentType.Application.Json)
                setBody(AddToBasketPayload(id, giftCardName))
            }
            if (response.status != HttpStatusCode.OK) {
                val errorResponse = response.body<BasketErrorResponse>()
                throw when {
                    errorResponse.errorMessage == "LASCANA_GIFTCARD_NAME_TO_LONG_EXCEPTION" -> BasketError.GiftCardNameTooLong()
                    errorResponse.errorMessage == "LASCANA_GIFTCARD_SAME_GIFTCARD_ALREADY_IN_BASKET" -> BasketError.GiftCardSameValueAlreadyInBasket()
                    errorResponse.errorMessage == "LASCANA_GIFTCARD_GIFTCARD_ALREADY_IN_BASKET" -> BasketError.GiftCardAlreadyInBasket()
                    errorResponse.errorMessage == "LASCANA_GIFTCARD_NORMAL_PRODUCT_ALREADY_IN_BASKET" -> BasketError.GiftCardProductAlreadyInBasket()
                    errorResponse.errorMessage == "ERROR_MESSAGE_OUTOFSTOCK_OUTOFSTOCK" -> BasketError.ProductUnavailable()
                    errorResponse.errorMessage.contains("Dieser Artikel liegt bereits in deinem Warenkorb") -> BasketError.ItemCountExceeded(
                        errorResponse.errorMessage
                    )

                    else -> BasketError.Generic(errorResponse.errorMessage)
                }
            }

            response.body<BasketResponse>().toBasket()
        }
    }
}

@Serializable
internal data class AddToBasketPayload(
    val articleId: String,
    @SerialName("giftcardname")
    val giftCardName: String?
)

@Serializable
private data class BasketResponse(
    val basketItems: List<BasketItem>
) {
    @Serializable
    data class BasketItem(
        val articleId: String,
        val amount: Int
    )
}

private fun BasketResponse.toBasket(): Basket {
    return Basket(basketItems.map { Basket.Item(it.articleId, it.amount) })
}

@Serializable
private data class BasketErrorResponse(
    val errorMessage: String
) {
    @Serializable
    data class BasketItem(
        val articleId: String,
        val amount: Int
    )
}
