package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.nativeui.ProductDetailScreenRequestContext
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion

internal class ProductScreensRepository(
    private val productLoader: ProductLoader,
) {
    private val states = mutableMapOf<String, ScreenState>()

    fun getScreenState(screenId: String): ScreenState? {
        return states[screenId]
    }

    @OptIn(ExperimentalUuidApi::class)
    fun loadNewProduct(
        id: String,
        secondaryId: String?,
        includeShopTheLook: Boolean,
    ): ScreenState {
        val screenId = Uuid.random().toString()
        val state = states.getOrPut(screenId) {
            ScreenState(
                includeShopTheLook = includeShopTheLook,
                originalProductId = id,
                screenId = screenId,
            )
        }
        state.load(id, secondaryId, FromNothing)
        return state
    }

    fun updateScreen(
        screenId: String,
        productId: String,
        context: ProductDetailScreenRequestContext?
    ) {
        states[screenId]?.load(productId, null, context ?: FromNothing)
    }

    private fun ScreenState.load(
        id: String,
        secondaryId: String?,
        context: ProductDetailScreenRequestContext,
    ) {
        val requestFlow = productLoader.load(
            originalProductId = originalProductId,
            id = id,
            secondaryId = secondaryId,
            includeShopTheLook = includeShopTheLook,
            onLoadSuccess = { product ->
                originalProductId = product.id
            }
        ).map { Contextual(context, it) }

        upstreamFlowProvider.tryEmit(requestFlow)
    }

    internal inner class ScreenState(
        var originalProductId: String,
        val includeShopTheLook: Boolean,
        val screenId: String,
    ) {
        val upstreamFlowProvider: MutableSharedFlow<Flow<Contextual<Operation<Product>>>> =
            MutableSharedFlow(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

        @OptIn(ExperimentalCoroutinesApi::class)
        val flow: Flow<Contextual<Operation<Product>>> = upstreamFlowProvider
            .flatMapLatest { it }
            .onCompletion {
                NativeLogger.d("Cleaning up product state for screenId: $screenId")
                states.remove(screenId)
            }
    }
}

internal data class Contextual<T>(
    val context: ProductDetailScreenRequestContext,
    val value: T
)

internal data object FromNothing : ProductDetailScreenRequestContext
