package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.http.NoOpCookiesBridge
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import io.ktor.client.plugins.api.createClientPlugin
import io.ktor.client.statement.request
import io.ktor.http.HttpHeaders

internal fun cookiesPlugin(configProvider: OGNativeConfigProvider) = createClientPlugin("Cookies") {
    fun cookiesBridge() = configProvider.configState.value.cookiesBridge ?: NoOpCookiesBridge
    onRequest { request, _ ->
        request.headers[HttpHeaders.Cookie] = cookiesBridge().getCookies(request.url.toString())
            .entries
            .joinToString("; ") { "${it.key}=${it.value}" }
    }
    onResponse { response ->
        response.headers.getAll(HttpHeaders.SetCookie)?.forEach { cookieString ->
            val split = splitCookieString(cookieString)
            split.forEach {
                cookiesBridge().setCookie(response.request.url.toString(), it)
            }
        }
    }
}

/**
 * Splits a string with one or more cookies separated with a comma, which
 * deprecated RFC 2109 allows and NSHTTPURLResponse does.
 *
 * Since some attribute values can also have commas, like the expires date
 * field, this does a look ahead to detect where it's a separator or not.
 *
 * Slightly adapted from
 * https://github.com/google/j2objc/blob/16820fdbc8f76ca0c33472810ce0cb03d20efe25/jre_emul/Classes/com/google/j2objc/net/IosHttpURLConnection.java#L860
 */
internal fun splitCookieString(s: String): List<String> {
    var buf: CharArray = s.toCharArray()
    var pos: Int = 0

    // Skip whitespace, returning true if there are more chars to read.
    fun skipSpace(): Boolean {
        while (pos < buf.size && buf[pos].isWhitespace()) {
            pos++
        }
        return pos < buf.size
    }

    fun hasNext(): Boolean {
        return pos < buf.size
    }

    fun next(): String {
        val start = pos
        while (skipSpace()) {
            if (buf[pos] == ',') {
                val lastComma = pos++
                skipSpace()
                val nextStart = pos
                while (pos < buf.size && buf[pos] != '=' && buf[pos] != ';' && buf[pos] != ',') {
                    pos++
                }
                if (pos < buf.size && buf[pos] == '=') {
                    // pos is inside the next cookie, so back up and return it.
                    pos = nextStart
                    return s.substring(start, lastComma)
                }
                pos = lastComma
            }
            pos++
        }
        return s.substring(start)
    }

    return buildList {
        while (hasNext()) {
            add(next())
        }
    }
}
