package com.ottogroup.appkit.nativeui.tracking

import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.tracking.lascana.LascanaNativeTracking
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View

internal interface NativeTrackingProvider {
    val nativeTracking: NativeTracking
}

internal class NativeTrackingProviderImpl(
    private val configProvider: OGNativeConfigProvider,
    private val lascanaNativeTracking: LascanaNativeTracking,
) : NativeTrackingProvider {
    override val nativeTracking: NativeTracking
        get() = when (configProvider.configState.value.tenant) {
            OGNativeConfig.NativeApiTenant.NONE -> NopNativeTracking()
            OGNativeConfig.NativeApiTenant.LASCANA -> lascanaNativeTracking
        }
}

internal interface NativeTracking {
    fun toECommerceItem(product: Product): ECommerceItem
    fun viewItem(product: Product)
    fun addItemToCart(productId: String)
    fun addItemToWishlist(productId: String)
    fun getViewPromotionEvent(
        item: ECommerceItem,
        creativeName: String? = null,
        creativeSlot: String? = null,
        promotionId: String? = null,
        promotionName: String? = null,
    ): View.ProductDetailPromotion

    fun getSelectPromotionEvent(
        item: ECommerceItem,
        creativeName: String? = null,
        creativeSlot: String? = null,
        promotionId: String? = null,
        promotionName: String? = null,
    ): Interaction.ProductDetailSelectPromotion
}

internal class NopNativeTracking : NativeTracking {
    override fun toECommerceItem(product: Product): ECommerceItem {
        nop()
        return ECommerceItem("", "")
    }

    override fun viewItem(product: Product) = nop()
    override fun addItemToCart(productId: String) = nop()
    override fun addItemToWishlist(productId: String) = nop()

    override fun getViewPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): View.ProductDetailPromotion {
        nop()
        return View.ProductDetailPromotion(
            ECommerceItem("", "")
        )
    }

    override fun getSelectPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): Interaction.ProductDetailSelectPromotion {
        nop()
        return Interaction.ProductDetailSelectPromotion(
            ECommerceItem("", "")
        )
    }

    private fun nop() {
        NativeLogger.e(
            "NopNativeTracking is used, please configure OGNative with a tenant.",
            throwable = RuntimeException("Please configure OGNative with a tenant.")
        )
    }
}
