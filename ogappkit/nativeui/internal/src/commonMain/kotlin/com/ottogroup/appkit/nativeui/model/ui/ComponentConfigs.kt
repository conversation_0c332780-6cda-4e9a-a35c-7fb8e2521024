package com.ottogroup.appkit.nativeui.model.ui

import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement

/**
 * Parses [json] into an instance of [ComponentConfigs]. Unsupported or
 * illegal component configuration elements are safely dropped so long as
 * the JSON itself is valid.
 */
internal inline fun <reified C : ComponentConfig> parseComponentConfigFromJson(json: String): ComponentConfigs<C> {
    val v = jsonInstance.decodeFromString<IntermediateComponentConfigs>(json)
    return ComponentConfigs(
        v.components.mapNotNull {
            tryDecodeFromJsonElement<C>(it)
        }
    )
}

internal inline fun <reified T> tryDecodeFromJsonElement(json: JsonElement): T? = try {
    jsonInstance.decodeFromJsonElement<T>(json)
} catch (t: Throwable) {
    NativeLogger.e("Dropping component due to error parsing config: $t")
    null
}

@Serializable
internal class IntermediateComponentConfigs(val components: List<JsonObject>)

@OptIn(ExperimentalSerializationApi::class)
private val jsonInstance = Json {
    ignoreUnknownKeys = true
    decodeEnumsCaseInsensitive = true
}
