package com.ottogroup.appkit.nativeui.api

import co.touchlab.stately.collections.ConcurrentMutableMap
import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

internal class RateLimiter<T : Any>(
    private val defaultRateLimitDuration: Duration = DEFAULT_LIMIT_DURATION_MS.milliseconds,
    private val clock: Clock = Clock.System,
) {
    private val timeStamps = ConcurrentMutableMap<T, Instant>()

    /**
     * Rate-limit an action. The first invocation is always executed. Subsequent [action]s using the
     * same [key] are only invoked when they are triggered after [limitDuration] has passed.
     * @param key unique identifier of the action
     * @param limitDuration the [Duration] required to pass before the next action using the same
     * [key] is invoked
     * @param action the action to invoke
     */
    suspend fun limit(
        key: T,
        limitDuration: Duration = defaultRateLimitDuration,
        action: suspend () -> Unit,
    ) {
        val now = clock.now()
        val lastTime = timeStamps[key] ?: Instant.DISTANT_PAST
        val delta = now - lastTime
        if (delta > limitDuration) {
            timeStamps[key] = now
            action()
        } else {
            NativeLogger.d("Rate-limiting $key: only $delta ms have passed.")
        }
    }

    /**
     * Marks an invocation for [key] at the current time without performing an action.
     * This allows an action to be manually performed always but subsequent actions with the same
     * [key] to be rate-limited.
     */
    fun mark(key: T) {
        timeStamps[key] = clock.now()
    }

    companion object {
        private const val DEFAULT_LIMIT_DURATION_MS = 100
    }
}
