package com.ottogroup.appkit.nativeui.util

import co.touchlab.kermit.LogWriter
import co.touchlab.kermit.Logger
import co.touchlab.kermit.LoggerConfig
import co.touchlab.kermit.Severity
import co.touchlab.kermit.loggerConfigInit
import co.touchlab.kermit.platformLogWriter
import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import org.koin.core.error.NoDefinitionFoundException

internal object NativeLogger : Logger(
    config = loggerConfigInit(platformLogWriter()),
    tag = "OGNative"
) {
    override val config: LoggerConfig = NativeLoggerConfig(super.config.logWriterList)

    class NativeLoggerConfig(
        override val logWriterList: List<LogWriter>
    ) : LoggerConfig, InternalKoinComponent {
        private val configProvider: OGNativeConfigProvider by lazy {
            try {
                getKoin().get()
            } catch (_: NoDefinitionFoundException) {
                OGNativeConfigProvider()
            }
        }
        private val isEnabled: Boolean get() = configProvider.configState.value.debug

        override val minSeverity: Severity
            get() = if (isEnabled) Severity.Verbose else Severity.Error
    }
}
