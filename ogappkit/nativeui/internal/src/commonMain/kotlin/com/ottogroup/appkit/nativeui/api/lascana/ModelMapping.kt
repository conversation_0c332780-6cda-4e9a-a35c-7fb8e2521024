package com.ottogroup.appkit.nativeui.api.lascana

import com.fleeksoft.ksoup.Ksoup
import com.fleeksoft.ksoup.nodes.Document
import com.fleeksoft.ksoup.nodes.Element
import com.fleeksoft.ksoup.nodes.Node
import com.fleeksoft.ksoup.select.Evaluator
import com.fleeksoft.ksoup.select.NodeFilter
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.lascana.fragment.AvailabilityFields
import com.ottogroup.appkit.nativeui.lascana.fragment.CompletePrice
import com.ottogroup.appkit.nativeui.lascana.fragment.MinimalProduct
import com.ottogroup.appkit.nativeui.lascana.fragment.RatingData
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Dimension.DimensionType
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Flag.FlagType
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.domain.SizeDimensionMatrix
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.util.HtmlToPlainText
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.nativeui.util.stripHtml
import kotlin.math.max
import kotlin.math.roundToInt

internal class ModelMapping(
    private val fuseNonColorDimensionsInto: String?,
) {

    internal fun toProduct(queryProduct: MinimalProduct): Product {
        return with(queryProduct) {
            Product(
                id = id,
                title = title,
                shortTitle = title,
                webShopUrl = null,
                parentId = parentId.takeIf { it.isNotBlank() },
                images = imageGallery.images.map {
                    Image(
                        url = it.image,
                        thumbnailUrl = it.image,
                    )
                },
                flags = emptyList(),
                brand = manufacturer?.let { Brand(name = it.title, description = null) },
                price = completePrice.toPrice(),
                fusedDimensions = emptyList(),
                individualDimensions = emptyList(),
                sizeMatrix = null,
                availability = Availability.Unknown,
                information = Information(
                    articleNumber = "",
                    description = "",
                    bulletPoints = emptyList(),
                    attributesTable = Information.AttributesTable(emptyList()),
                    distributingCompanies = emptyList(),
                    documents = emptyList(),
                    articleStandards = null,
                ),
                reviews = Reviews(rating = null, reviews = emptyList(), writeReviewUrl = ""),
                sku = sku ?: "",
                shopTheLook = null,
                moreFromTheSeries = null,
                breadcrumbs = emptyList(),
            )
        }
    }

    internal fun toProduct(queryProduct: ProductDetailQuery.Product): Product {
        return with(queryProduct) {
            NativeLogger.d("Received product $id has ${variants.size} variants.")
            Product(
                id = id,
                title = getTitle(),
                shortTitle = title,
                webShopUrl = seo.url?.onPublicHost(),
                parentId = parentId.takeIf { it.isNotBlank() },
                images = imageGallery.toImages(),
                flags = getFlags(),
                brand = manufacturer?.toBrand(),
                price = getPrice(),
                fusedDimensions = getDimensions(fuseNonColorDimensionsInto),
                individualDimensions = getDimensions(null),
                sizeMatrix = getSizeMatrix(),
                availability = getAvailability(),
                information = getInformation(),
                reviews = ratingData.getReviews(),
                sku = getSku() ?: "",
                shopTheLook = getShopTheLook(),
                moreFromTheSeries = moreFromTheSeries(),
                sizeAdvisorUrl = flyouts.sizeGuide,
                paybackPoints = paybackPoints.toInt(),
                voucherSpec = getVoucherSpec(),
                breadcrumbs = breadcrumb,
            )
        }
    }

    internal fun toProductReviews(queryProductReviews: ProductReviewsQuery.Product): ProductReviews {
        return with(queryProductReviews) {
            ProductReviews(
                id = id,
                title = title,
                brandName = manufacturer?.title,
                reviews = ratingData.getReviews(),
            )
        }
    }

    private fun ProductDetailQuery.Product.getTitle(): String {
        val name = nonDetailPageAttributes.find {
            it.attribute.title == "Produkt-Name" && it.attribute.groupName == "Artikelbezeichnung"
        }?.value

        return if (name == null) {
            title
        } else {
            "$title \"$name\""
        }
    }

    private fun ProductDetailQuery.ImageGallery.toImages(): List<Image> {
        return images.map {
            Image(
                url = it.image.onPublicHost(),
                thumbnailUrl = it.icon.onPublicHost()
            )
        }
    }

    private fun ProductDetailQuery.Product.getFlags(): List<Flag> {
        return flags.sortedBy { it.priority.toIntOrNull() ?: 0 }.mapNotNull { it.toFlag() }
    }

    private fun ProductDetailQuery.Flag.toFlag(): Flag? {
        return try {
            Flag(FlagType.valueOf(type.uppercase()))
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    private fun ProductDetailQuery.Manufacturer.toBrand(): Brand {
        return Brand(
            name = title.stripHtml(),
            description = shortdesc.stripHtml(),
        )
    }

    private fun ProductDetailQuery.Product.getPrice(): Price {
        return variants.find { it.id == id }?.variantInfo?.completePrice?.toPrice() ?: completePrice.toPrice()
    }

    private fun CompletePrice.toPrice(): Price {
        // LAS prices are always for a concrete variant with a concrete (non-start) price
        val isStartPrice = false

        return Price(
            value = price.priceFields.price.toWholeCents(),
            currency = price.priceFields.currency.name,
            oldValue = listPrice?.priceFields?.price?.toWholeCents(),
            isStartPrice = isStartPrice,
        )
    }

    private fun Double.toWholeCents(): Int = (this * 100).roundToInt()

    /**
     * A wrapper to keep track of variant labels and their respective value
     * simultaneously.
     */
    private class VariantLabelValue(val label: String, value: String) {
        // if the value starts with the label name, remove it. Fixes "Cup: Cup A" -> "Cup: A".
        val value: String = value.removePrefix(label).trim()
    }

    private fun ProductDetailQuery.Product.getDimensions(fuseNonColorsInto: String?): List<Dimension> {
        val actualVariantValues = getVariantValues()

        /* There have been cases (in staging, at least) where product defines more variant labels than it actually fills
         * with values. In this case, limit to the actually provided values.
         * The exception being master products, which supply only labels but never values. */
        val variantLabels = if (actualVariantValues.isNotEmpty()) {
            variantLabels.take(actualVariantValues.size)
        } else {
            variantLabels
        }

        val labelValues = variantLabels.mapIndexed { i, v ->
            VariantLabelValue(v, actualVariantValues.getOrNull(i) ?: "")
        }.fuseNonColors(fuseNonColorsInto)

        val colorDimensionIndex = max(labelValues.indexOfFirst { it.label == COLOR_VARIANT_LABEL }, 0)

        val variantsTree = VariantsTree()
        for (v in variants) {
            val values = variantLabels.zip(v.variantInfo.variantValues)
                .map { VariantLabelValue(it.first, it.second) }
                .fuseNonColors(fuseNonColorsInto)
                .map { it.value }
            variantsTree.insert(values, v.id)
        }

        return labelValues.mapIndexed { indexOfVariant, labelValue ->
            val variantLabel = labelValue.label

            val availableDimensionValuesAtThisSubtree =
                variantsTree.findDimensionValuesFor(labelValues.take(indexOfVariant).map { it.value })

            val isColorDimension = variantLabel == COLOR_VARIANT_LABEL
            Dimension(
                name = variantLabel,
                type = if (isColorDimension) {
                    DimensionType.COLOR
                } else {
                    DimensionType.DEFAULT
                },
                values = availableDimensionValuesAtThisSubtree.mapNotNull { variantValue ->
                    if (labelValues.all { it.value.isEmpty() }) return@mapNotNull null

                    val variantValuesToLookFor = labelValues.map { it.value }.toMutableList().apply {
                        set(indexOfVariant, variantValue)
                    }.toList()
                    variantsTree.findVariantIdFor(variantValuesToLookFor)?.let { foundId ->
                        // TODO optimize: avoid additional traversal by keeping actual variants in tree
                        variants.find { it.id == foundId }?.let { foundVariant ->
                            Dimension.Value(
                                text = variantValue.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() },
                                productId = foundId,
                                thumbnailUrl = if (isColorDimension) {
                                    val url = foundVariant.variantInfo.positionedPicture
                                        ?: foundVariant.variantInfo.imageGallery.thumb
                                    url.onPublicHost()
                                } else {
                                    null
                                },
                                availability = if (isColorDimension) {
                                    // overall availability state of a color is the best state of all its variants
                                    val sameColorVariants = variants.filter {
                                        it.variantInfo.variantValues[colorDimensionIndex] == variantValue
                                    }
                                    val availabilitiesOfThisColorsVariants =
                                        sameColorVariants.map {
                                            it.variantInfo.availabilityFields.toAvailabilityState()
                                        }.toSet()
                                    val state =
                                        Availability.State.entries.first { it in availabilitiesOfThisColorsVariants }
                                    Availability(
                                        state = state,
                                        quantity = sameColorVariants.sumOf {
                                            it.variantInfo.availabilityFields.stock.stock.toInt()
                                        },
                                    )
                                } else {
                                    foundVariant.variantInfo.availabilityFields.toAvailability()
                                },
                                price = foundVariant.variantInfo.completePrice.toPrice(),
                            )
                        }
                    }
                }.sortedWith(DimensionValueComparator)
            )
        }
    }

    /**
     * Don't directly take the variantValues of [this], but look for them in
     * its variants. When loading a sibling to a product we already know,
     * we may manually override its ID with the target product ID, while
     * keeping the rest of the data. Since all sibling products share the same
     * variants, this allows us to already access some relevant product data
     * while the target product is still loading, by looking that data up in
     * the variants.
     */
    private fun ProductDetailQuery.Product.getVariantValues(): List<String> {
        val thisProductInItsVariants = variants.find { it.id == this.id } ?: return variantLabels
        return thisProductInItsVariants.variantInfo.variantValues
    }

    /**
     * Fuses variant labels except for the color together so we always
     * have 2 dimensions: color + all else combined under a new name.
     * ["Color", "Cup", "Width"] -> ["Color", "Size"]
     */
    private fun List<VariantLabelValue>.fuseNonColors(fuseNonColorsInto: String?): List<VariantLabelValue> {
        if (fuseNonColorsInto == null) return this
        if (size <= 1) return this

        return partition { it.label == COLOR_VARIANT_LABEL }.let { (colors, nonColors) ->
            // if there is a Cup dimension, put it at the end
            val nonColors = nonColors.partition { it.label == CUP_VARIANT_LABEL }.let { (cups, nonCups) ->
                nonCups + cups
            }
            return colors + VariantLabelValue(fuseNonColorsInto, nonColors.joinToString("") { it.value })
        }
    }

    private fun ProductDetailQuery.Product.getSizeMatrix(): SizeDimensionMatrix? {
        val actualVariantValues = getVariantValues()

        // a size dimension matrix only makes sense when we look at a variant, not a master
        if (actualVariantValues.isEmpty()) return null

        /* There have been cases (in staging, at least) where product defines more variant labels than it actually fills
         * with values. In this case, limit to the actually provided values.
         * The exception being master products, which supply only labels but never values. */
        val variantLabels = if (actualVariantValues.isNotEmpty()) {
            variantLabels.take(actualVariantValues.size)
        } else {
            variantLabels
        }

        // a size dimension matrix only makes sense if we have two size dimensions (+ the ever present color dimension)
        if (variantLabels.size != 3) return null

        val sameColorVariants = variants.filter { it.variantInfo.variantValues[0] == actualVariantValues[0] }
            .map {
                // clean up each variant's values by applying the prefix-stripping from VariantLabelValue
                it.copy(
                    variantInfo = it.variantInfo.copy(
                        variantValues = variantLabels.zip(it.variantInfo.variantValues)
                            .map { (label, value) -> VariantLabelValue(label, value).value }
                    )
                )
            }
        val groupedByFirstSizeDimension = sameColorVariants.groupBy { it.variantInfo.variantValues[1] }

        return SizeDimensionMatrix(
            parentDimensionName = variantLabels[1],
            entries = groupedByFirstSizeDimension.map { (key, value) ->
                SizeDimensionMatrix.Entry(
                    name = key,
                    child = Dimension(
                        name = variantLabels[2],
                        type = DimensionType.DEFAULT,
                        values = value.map {
                            it.toDimensionValue(text = it.variantInfo.variantValues[2])
                        }.sortedWith(DimensionValueComparator)
                    )
                )
            }.sortedWith(
                compareBy(
                    comparator = DimensionValueNameComparator,
                    selector = { it.name }
                )
            )
        )
    }

    private fun ProductDetailQuery.Variant.toDimensionValue(text: String): Dimension.Value {
        return Dimension.Value(
            text = text,
            productId = id,
            thumbnailUrl = null,
            availability = variantInfo.availabilityFields.toAvailability(),
            price = variantInfo.completePrice.toPrice(),
        )
    }

    private fun ProductDetailQuery.Product.getAvailability(): Availability {
        return variants.find { it.id == id }
            ?.variantInfo?.availabilityFields?.toAvailability()
            ?: this.availabilityFields.toAvailability()
    }

    private fun AvailabilityFields.toAvailability(): Availability {
        return Availability(
            state = toAvailabilityState(),
            quantity = stock.stock.toInt(),
            message = deliveryInformation,
            notifyMeUrl = flyouts.reminder,
        )
    }

    private fun AvailabilityFields.toAvailabilityState(): Availability.State {
        return when (stock.stockStatus) {
            STOCK_STATUS_DELIVERABLE -> Availability.State.IN_STOCK
            STOCK_STATUS_DELIVERABLE_FEW_LEFT -> Availability.State.LOW_STOCK
            STOCK_STATUS_NOT_IN_STOCK -> Availability.State.TEMPORARILY_OUT_OF_STOCK

            else -> Availability.State.UNKNOWN
        }
    }

    private fun ProductDetailQuery.Product.getInformation(): Information {
        val articleNumber = nonDetailPageAttributes.find {
            it.attribute.title.equals("Technical Artnum", ignoreCase = true)
        }?.value?.substringBefore("_").orEmpty()

        val shortDescriptionDoc = Ksoup.parse(shortDescription)

        return Information(
            articleNumber = articleNumber,
            description = shortDescriptionDoc.getDescription(),
            bulletPoints = getSellingPoints(),
            attributesTable = getDescriptionTable(),
            distributingCompanies = getDistributingCompanies(),
            documents = shortDescriptionDoc.getDocuments(),
            articleStandards = getArticleStandards(),
        )
    }

    private fun Document.getDescription(): String {
        // filter modifies the existing Document, so we need to clone it to be able to use the original elsewhere
        val docWithoutPdfLinks = clone().filter(RemovePdfLinks)
        return HtmlToPlainText.getPlainText(docWithoutPdfLinks)
    }

    private fun ProductDetailQuery.Product.getSellingPoints(): List<String> {
        return nonDetailPageAttributes
            .filter { it.attribute.title.startsWith("Selling Point", ignoreCase = true) }
            .sortedBy { it.attribute.title }
            .map { it.value }
    }

    private fun ProductDetailQuery.Product.getDescriptionTable(): Information.AttributesTable {
        return Information.AttributesTable(
            sections = detailPageAttributes.groupBy { it.attribute.groupName }
                .map { (groupName, attributes) ->
                    val groupedByTitle = attributes.groupBy { it.attribute.title }
                    Information.AttributesTable.Section(
                        title = groupName,
                        entries = groupedByTitle.map { (title, attributes) ->
                            Information.AttributesTable.Section.Entry(
                                key = title.stripHtml(),
                                values = attributes.map { it.value.stripHtml() }
                            )
                        }
                    )
                }
        )
    }

    private fun ProductDetailQuery.Product.getDistributingCompanies(): List<Information.DistributingCompany> {
        if (distributingCompany == null) return emptyList()
        return distributingCompany.companies.map {
            Information.DistributingCompany(
                name = it.companyName.stripHtml(),
                address = buildString {
                    appendLine(it.street.stripHtml())
                    append("${it.postalCode} ${it.city.stripHtml()}, ${it.countryCode}")
                },
                email = it.email.takeIf { it.isNotBlank() },
                phone = it.phone.takeIf { it.isNotBlank() },
                url = it.url.takeIf { it.isNotBlank() },
            )
        }
    }

    private fun Document.getDocuments(): List<Link> {
        val docLinks = select(Evaluator.AttributeWithValueContaining("href", ".pdf"))
        return docLinks.mapNotNull { element ->
            val text = element.text().takeIf { it.isNotBlank() } ?: return@mapNotNull null
            val url = element.attr("href").takeIf { it.isNotBlank() } ?: return@mapNotNull null
            Link(text, url)
        }
    }

    private fun ProductDetailQuery.Product.getArticleStandards(): ArticleStandards? {
        val sealAttributes = nonDetailPageAttributes.filter { it.attribute.title == "Nachhaltigkeitssiegel" }
        val seals = sealAttributes.mapNotNull { attribute ->
            ArticleStandards.Seal.byNameDE[attribute.value].also {
                if (it == null) NativeLogger.w { "Unknown article standards seal ${attribute.value}." }
            }
        }
        if (seals.isEmpty()) return null

        return ArticleStandards(seals)
    }

    private fun RatingData.getReviews(): Reviews {
        val rating = getRating()
        val reviews = reviews.map { review ->
            Review(
                rating = review.rating,
                text = review.text.stripHtml(),
                title = review.title.stripHtml(),
                dateTime = review.createAt,
                reviewerName = review.reviewer?.firstName?.stripHtml(),
            )
        }
        return Reviews(rating, reviews, flyouts.reviews ?: "")
    }

    private fun RatingData.getRating(): Rating? {
        if (rating.count == 0) {
            if (reviews.isEmpty()) return null

            // Calculate rating from reviews. Ideally, Rating and Reviews should be consistent, but you never know.
            return Rating(
                averageRating = reviews.map { it.rating }.average().toFloat(),
                count = reviews.size,
                ratingDistribution = getRatingDistribution()
            )
        }

        return Rating(
            averageRating = rating.rating.toFloat(),
            count = rating.count,
            ratingDistribution = getRatingDistribution()
        )
    }

    private fun RatingData.getRatingDistribution(): Map<Int, Pair<Int, Double>> {
        val totalReviews = reviews.size.toDouble()
        return reviews.groupingBy { it.rating }.eachCount()
            .mapValues { (_, count) ->
                val percentage = if (totalReviews > 0) (count / totalReviews) * 100 else 0.0
                Pair(count, percentage)
            }
    }

    private fun ProductDetailQuery.Product.getShopTheLook(): ShopTheLook? {
        if (shopTheLook == null) return null
        return ShopTheLook(
            image = Image(url = shopTheLook.mainImagePath.onPublicHost(), thumbnailUrl = null),
            articles = shopTheLook.articles.map { toProduct(it.minimalProduct) }
        )
    }

    private fun ProductDetailQuery.Product.moreFromTheSeries(): MoreFromTheSeries? {
        if (moreFromSeries == null) return null
        return MoreFromTheSeries(
            articles = moreFromSeries.articles.map {
                toProduct(it.article.minimalProduct)
            }
        )
    }

    private fun ProductDetailQuery.Product.getSku(): String? {
        return variants.find { it.id == id }?.variantInfo?.sku ?: sku
    }

    private fun ProductDetailQuery.Product.getVoucherSpec(): VoucherSpec? {
        if (parentId != VOUCHER_PARENT_ID) return null
        return VoucherSpec(
            allowsCustomName = true,
            maxCustomNameLength = VOUCHER_MAX_NAME_LENGTH,
        )
    }

    companion object {
        const val COLOR_VARIANT_LABEL = "Farbe"
        const val CUP_VARIANT_LABEL = "Cup"
        const val FUSED_SIZE_DIMENSION_NAME = "Größe"

        const val VOUCHER_PARENT_ID = "100634143"
        const val VOUCHER_MAX_NAME_LENGTH = 29

        const val STOCK_STATUS_DELIVERABLE = 0
        const val STOCK_STATUS_DELIVERABLE_FEW_LEFT = 1
        const val STOCK_STATUS_NOT_IN_STOCK = -1
    }
}

/**
 * Temporary util function to make URLs referencing only-behind-VPN hosts
 * use the public lascana.de instead. This will obviously not be required
 * in production code.
 */
private fun String.onPublicHost(): String {
    val hostReplacement = mapOf(
        "stage-bilder-lascana-de-ottolasc.unbelievable-machine.net" to "bilder.lascana.de",
        "stage-www-lascana-de-ottolasc.unbelievable-machine.net" to "lascana.de",
    )
    return hostReplacement.entries.fold(this) { acc, (key, value) -> acc.replace(key, value) }
}

private object RemovePdfLinks : NodeFilter {
    override fun head(
        node: Node,
        depth: Int
    ): NodeFilter.FilterResult {
        val element = node as? Element ?: return NodeFilter.FilterResult.CONTINUE

        if (element.tagName() == "a" && element.attr("href").contains(".pdf", ignoreCase = true)) {
            return NodeFilter.FilterResult.REMOVE
        }

        return NodeFilter.FilterResult.CONTINUE
    }
}
