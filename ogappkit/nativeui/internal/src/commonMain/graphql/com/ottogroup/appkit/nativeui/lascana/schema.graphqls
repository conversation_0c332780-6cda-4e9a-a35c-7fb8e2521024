type Query {
  paypalApprovalProcess(basketId: ID!, returnUrl: String!, cancelUrl: String!, displayBasketInPayPal: Boolean!): PayPalCommunicationInformation!

  paypalExpressApprovalProcess(basketId: ID!, returnUrl: String!, cancelUrl: String!, displayBasketInPayPal: Boolean!): PayPalCommunicationInformation!

  paypalTokenStatus(paypalToken: String!): PayPalTokenStatus!

  """
  retrieve a JWT for authentication of further requests
  """
  token(username: String = null, password: String = null): String!

  """
  Query a customer's active JWT.
  User with right 'VIEW_ANY_TOKEN' can query any customer's tokens.
  """
  tokens(filter: TokenFilterList = null, pagination: PaginationInput = null, sort: TokenSorting = null): [Token!]!

  product(productId: ID!): Product!

  products(filter: ProductFilterList = null, pagination: PaginationInput = null, sort: ProductSorting = null): [Product!]!

  vendor(vendorId: ID!): Vendor!

  vendors(filter: VendorFilterList = null, sort: VendorSorting = null): [Vendor!]!

  category(categoryId: ID!): Category!

  categories(filter: CategoryFilterList = null, sort: CategorySorting = null): [Category!]!

  manufacturer(manufacturerId: ID!): Manufacturer!

  manufacturers(filter: ManufacturerFilterList = null, sort: ManufacturerSorting = null): [Manufacturer!]!

  link(linkId: ID!): Link!

  links(filter: LinkFilterList = null): [Link!]!

  action(actionId: ID!): Action!

  actions(filter: ActionFilterList = null): [Action!]!

  banner(bannerId: ID!): Banner!

  banners: [Banner!]!

  promotion(promotionId: ID!): Promotion!

  promotions: [Promotion!]!

  content(contentId: ID!): Content!

  contents(filter: ContentFilterList = null): [Content!]!

  """
  If `name` is ommited, gives you the currently active currency
  """
  currency(name: String = null): Currency!

  currencies: [Currency!]!

  attribute(attributeId: ID!): Attribute!

  attributes(filter: AttributeFilterList = null): [Attribute!]!

  review(reviewId: ID!): Review!

  wishedPrice(wishedPriceId: ID!): WishedPrice!

  wishedPrices: [WishedPrice!]!

  country(countryId: ID!): Country!

  countries(filter: CountryFilterList = null, sort: CountrySorting = null): [Country!]!

  """
  Returns information for any basket the customer owns.
  """
  basket(basketId: ID!): Basket!

  """
  Returns information for any basket marked as public.
  """
  publicBasket(basketId: ID!): PublicBasket!

  """
  Argument `owner` will be matched exactly against lastname and / or email
  Query for public baskets by owner.
  """
  baskets(owner: String!): [PublicBasket!]!

  translation(key: String!): Translation!

  translations: [Translation!]!
}

type PayPalCommunicationInformation {
  token: String!

  communicationUrl: String!
}

type PayPalTokenStatus {
  token: String!

  tokenApproved: Boolean!
}

input TokenFilterList {
  customerId: IDFilterInput = null

  shopId: IDFilterInput = null

  expiresAt: DateFilterInput = null
}

input IDFilterInput {
  equals: ID!
}

input DateFilterInput {
  equals: String = null

  between: [String!] = null
}

input PaginationInput {
  offset: Int = 0

  limit: Int = null
}

input TokenSorting {
  expiresAt: String = "ASC"
}

type Token {
  id: ID!

  token: String!

  createdAt: DateTime

  expiresAt: DateTime

  userAgent: String!

  customerId: ID!

  shopId: ID!
}

"""
The `DateTime` scalar type represents time data, represented as an ISO-8601 encoded UTC date string.
"""
scalar DateTime

type Product {
  id: ID!

  active: Boolean!

  sku: String

  ean: String!

  manufacturerEan: String!

  mpn: String!

  title: String!

  shortDescription: String!

  longDescription: String!

  vat: Float!

  insert: DateTime

  freeShipping: Boolean!

  timestamp: DateTime

  variantLabels: [String!]!

  variantValues: [String!]!

  wishedPriceEnabled: Boolean!

  varMinPrice: Float!

  flyouts: Flyout!

  breadcrumb: [String!]!

  paybackPoints: Float!

  shopTheLook: ShopTheLook

  moreFromSeries: MoreFromSeries

  positionedPicture: String

  parentId: String!

  flags: [Flag!]!

  distributingCompany: DistributingCompanies

  attributes(isForDetailPage: Boolean = false): [ProductAttribute!]!

  variants: [Product!]!

  deliveryInformation: String!

  sustainabilityFlags: SustainabilityFlags

  dimensions: ProductDimensions!

  price: Price!

  listPrice: Price

  stock: ProductStock!

  imageGallery: ProductImageGallery!

  rating: ProductRating!

  deliveryTime: ProductDeliveryTime!

  scalePrices: [ProductScalePrice!]!

  bundleProduct: Product

  manufacturer: Manufacturer

  vendor: Vendor

  categories(onlyMainCategory: Boolean = true): [Category!]!

  unit: ProductUnit

  seo: Seo!

  crossSelling: [Product!]!

  accessories: [Product!]!

  selectionLists: [SelectionList!]!

  reviews: [Review!]!
}

type Flyout {
  sizeGuide: String

  reviews: String

  reminder: String

  shippingCosts: String!
}

type ShopTheLook {
  articles: [Product!]!

  mainImagePath: String!
}

type MoreFromSeries {
  articles: [SeriesProduct!]!
}

type SeriesProduct {
  id: Int!

  title: String!

  image: String!

  link: String!

  article: Product!
}

type Flag {
  type: String!

  priority: String!

  text: String

  color: String

  begin: String

  end: String
}

type DistributingCompanies {
  companies: [DistributingCompany!]!
}

type DistributingCompany {
  companyName: String!

  city: String!

  email: String!

  phone: String!

  url: String!

  countryCode: String!

  postalCode: String!

  street: String!
}

type ProductAttribute {
  attribute: Attribute!

  value: String!
}

type Attribute {
  title: String!

  groupName: String!
}

type SustainabilityFlags {
  flags: [SustainabilityFlag!]
}

type SustainabilityFlag {
  title: String!

  description: String!

  logo: String!

  groupCategory: String!

  groupTitle: String!

  groupDescription: String!

  groupLogoUrl: String!
}

type ProductDimensions {
  length: Float!

  width: Float!

  height: Float!

  weight: Float!
}

type Price {
  price: Float!

  vat: Float!

  vatValue: Float!

  nettoPriceMode: Boolean!

  currency: Currency!
}

type Currency {
  id: Int!

  name: String!

  rate: Float!

  sign: String!
}

type ProductStock {
  stock: Float!

  """
  Value can be one of:
   0 -> (green) deliverable
   1 -> (orange) deliverable, but only a few left
  -1 -> (red) not stock
  """
  stockStatus: Int!

  restockDate: DateTime
}

type ProductImageGallery {
  images: [ProductImage!]!

  icon: String!

  thumb: String!
}

type ProductImage {
  image: String!

  icon: String!

  zoom: String!
}

type ProductRating {
  rating: Float!

  count: Int!
}

type ProductDeliveryTime {
  minDeliveryTime: Int!

  maxDeliveryTime: Int!

  """
  Value can be one of:
  - DAY
  - WEEK
  - MONTH
  """
  deliveryTimeUnit: String!
}

type ProductScalePrice {
  """
  Whether the scale price is
  - a new absolute price (you can query that in the `absolutePrice` field)
  - or a percentage discount (you can query that in the `discount` field)
  """
  absoluteScalePrice: Boolean!

  absolutePrice: Float

  discount: Float

  amountFrom: Int!

  amountTo: Int!
}

type Manufacturer {
  id: ID!

  active: Boolean!

  icon: String

  title: String!

  shortdesc: String!

  timestamp: DateTime

  seo: Seo!

  products(pagination: PaginationInput = null, sort: ProductSorting = null): [Product!]!
}

type Seo {
  description: String!

  keywords: String!

  url: String
}

input ProductSorting {
  position: String = "ASC"

  minPriceVariant: String = null

  price: String = null

  productNumber: String = null

  rating: String = null

  stock: String = null

  title: String = null
}

type Vendor {
  id: ID!

  active: Boolean!

  icon: String

  title: String!

  shortdesc: String!

  timestamp: DateTime

  seo: Seo!

  products(pagination: PaginationInput = null, sort: ProductSorting = null): [Product!]!
}

type Category {
  id: ID!

  """
  Defines the order in which categories are displayed:
  The category with the lowest number is displayed at the top,
  and the category with the highest number at the bottom
  """
  position: Int!

  active(now: DateTime = null): Boolean!

  """
  Hidden categories are not visible in lists and menu,
  but can be accessed by direct link
  """
  hidden: Boolean!

  title: String!

  shortDescription: String!

  longDescription: String!

  thumbnail: String

  """
  If the external link is specified it will be opened instead of category content
  """
  externalLink: String!

  template: String!

  """
  If specified, all products, with price higher than specified,
  will be shown in this category
  """
  priceFrom: Float!

  """
  If specified, all products, with price lower than specified,
  will be shown in this category
  """
  priceTo: Float!

  icon: String

  promotionIcon: String

  vat: Float

  """
  Skip all negative discounts for products in this category
  (Discounts, Vouchers, Delivery ...)
  """
  skipDiscount: Boolean!

  showSuffix: Boolean!

  timestamp: DateTime

  parent: Category

  root: Category

  children: [Category!]!

  seo: Seo!

  products(pagination: PaginationInput = null, sort: ProductSorting = null): [Product!]!
}

type ProductUnit {
  price: Price!

  name: String!
}

type SelectionList {
  title: String!

  fields: [Selection!]!
}

type Selection {
  value: String!
}

type Review {
  id: ID!

  text: String!

  rating: Int!

  createAt: DateTime

  reviewer: Reviewer

  title: String!

  product: Product

  language: Language!

  active: Boolean!
}

type Reviewer {
  firstName: String!
}

type Language {
  id: ID!

  code: String!

  language: String!
}

input ProductFilterList {
  title: StringFilterInput = null

  category: CategoryIDFilterInput = null

  manufacturer: IDFilterInput = null

  vendor: IDFilterInput = null
}

input StringFilterInput {
  equals: String = null

  contains: String = null

  beginsWith: String = null
}

input CategoryIDFilterInput {
  equals: ID!
}

input VendorFilterList {
  title: StringFilterInput = null
}

input VendorSorting {
  title: String = null
}

input CategoryFilterList {
  title: StringFilterInput = null

  parentId: StringFilterInput = null
}

input CategorySorting {
  position: String = "ASC"

  title: String = null
}

input ManufacturerFilterList {
  title: StringFilterInput = null
}

input ManufacturerSorting {
  title: String = null
}

type Link {
  id: ID!

  active: Boolean!

  timestamp: DateTime

  description: String!

  url: String!

  creationDate: DateTime
}

input LinkFilterList {
  description: StringFilterInput = null
}

type Action {
  id: ID!

  active: Boolean!

  title: String!

  products: [Product!]!
}

input ActionFilterList {
  actionId: StringFilterInput = null
}

type Banner {
  id: ID!

  active(now: DateTime = null): Boolean!

  title: String!

  picture: String!

  link: String!

  sorting: Int!

  product: Product
}

type Promotion {
  id: ID!

  active: Boolean!

  title: String!

  text: String!
}

type Content {
  id: ID!

  active: Boolean!

  title: String!

  """
  Returns rendered HTML string that might contain script and style tags
  """
  content: String!

  """
  Return not rendered, raw content
  """
  rawContent: String!

  folder: String!

  version: String!

  seo: Seo!

  category: Category
}

input ContentFilterList {
  folder: StringFilterInput = null
}

input AttributeFilterList {
  title: StringFilterInput = null
}

type WishedPrice {
  id: ID!

  email: String!

  """
  This field gives us information about the last sent notification email.
  When it is null it states that no notification email was sent.
  """
  notificationDate: DateTime

  creationDate: DateTime

  inquirer: Inquirer

  product: Product!

  price: Price!

  currency: Currency!
}

type Inquirer {
  firstName: String!
}

type Country {
  id: ID!

  position: Int!

  active: Boolean!

  title: String!

  isoAlpha2: String!

  isoAlpha3: String!

  isoNumeric: String!

  shortDescription: String!

  description: String!

  creationDate: DateTime

  states(sort: StateSorting = null): [State!]!
}

input StateSorting {
  title: String = null
}

type State {
  id: ID!

  title: String!

  isoAlpha2: String!

  creationDate: DateTime
}

input CountryFilterList {
  title: StringFilterInput = null
}

input CountrySorting {
  position: String = "ASC"

  title: String = null
}

type Basket {
  public: Boolean!

  id: ID!

  title: String!

  creationDate: DateTime

  lastUpdateDate: DateTime

  owner: BasketOwner

  items(pagination: PaginationInput = null): [BasketItem!]!

  cost: BasketCost!

  vouchers: [Voucher!]!

  deliveryAddress: DeliveryAddress

  """
  Returns selected payment for current basket.
  """
  payment: PaymentInterface

  """
  Returns selected delivery method for current basket.
  """
  deliveryMethod: DeliveryMethodInterface

  paypalToken: String!

  paypalServiceType: Int!
}

type BasketOwner {
  firstName: String!

  lastName: String!
}

type BasketItem {
  id: ID!

  amount: Int!

  lastUpdateDate: DateTime

  product: Product
}

type BasketCost {
  voucher: Float!

  discount: Float!

  total: Float!

  productNet: Price!

  productGross: BasketProductBruttoSum!

  payment: Price!

  currency: Currency!

  delivery: Price!
}

type BasketProductBruttoSum {
  sum: Float!

  vats: [BasketProductVats!]!
}

type BasketProductVats {
  vatRate: Float!

  vatPrice: Float!
}

type Voucher {
  id: ID!

  voucher: String!

  number: String!

  reserved: DateTime

  discount: Float

  redeemedAt: DateTime

  series: VoucherSeries!
}

type VoucherSeries {
  id: ID!

  title: String!

  description: String!

  validFrom: DateTime

  validTo: DateTime

  discount: Float!

  discountType: String!
}

type DeliveryAddress {
  id: ID!

  salutation: String!

  firstName: String!

  lastName: String!

  company: String!

  additionalInfo: String!

  street: String!

  streetNumber: String!

  zipCode: String!

  city: String!

  phone: String!

  fax: String!

  updated: DateTime

  country: Country

  state: State
}

interface PaymentInterface {
  id: ID!

  active: Boolean!

  title: String!

  description: String!

  updated: DateTime
}

interface DeliveryMethodInterface {
  id: ID!

  title: String!

  paymentTypes: [BasketPayment!]!

  position: Int!
}

type BasketPayment implements PaymentInterface {
  id: ID!

  active: Boolean!

  title: String!

  description: String!

  updated: DateTime

  cost: Price!
}

type PublicBasket {
  id: ID!

  title: String!

  creationDate: DateTime

  lastUpdateDate: DateTime

  owner: BasketOwner!

  items(pagination: PaginationInput = null): [BasketItem!]!
}

type Translation {
  key: String!

  value: String!
}

type Mutation {
  """
  Invalidate all tokens per customer.
  - Customer with right INVALIDATE_ANY_TOKEN can invalidate tokens for any customer Id.
   - Customer without special rights can invalidate only own tokens.
  If no customerId is supplied, own Id is taken.
  """
  customerTokensDelete(customerId: ID = null): Int!

  """
  Invalidate specific token.
  - Customer with right INVALIDATE_ANY_TOKEN can invalidate any token.
  - Customer without special rights can invalidate only own token.
  """
  tokenDelete(tokenId: ID!): Boolean!

  """
  Invalidate all tokens for current shop.
  INVALIDATE_ANY_TOKEN right is required.
  """
  shopTokensDelete: Int!

  """
  Regenerates the JWT signature key.
  This will invalidate all issued tokens for the current shop.
  Only use if no other option is left.
  REGENERATE_SIGNATURE_KEY right is required.
  """
  regenerateSignatureKey: Boolean!

  customerRegister(customer: CustomerInput!): Customer!

  newsletterOptIn(newsletterStatus: NewsletterStatusInput!): NewsletterStatus!

  """
  NewsletterStatusUnsubscribeInput email field is optional.
  In case of missing input email but available token, newsletter will be unsubscribed for token email.
  Input email is preferred over token email.
  """
  newsletterUnsubscribe(newsletterStatus: NewsletterStatusUnsubscribeInput = null): Boolean!

  """
  NewsletterStatusSubscribeInput input fields are optional in case of token.
  - If token exists without NewsletterStatusSubscribeInput, token email will be subscribed.
    If token user is already subscribed, status will not be changed and no optin mail is sent.
  - If token and NewsletterStatusSubscribeInput exists, input email will be subscribed.
    If input email user is already subscribed, status will be changed to 2 and
    optin mail is sent depending on shop config parameter blOrderOptInEmail.
  - If only NewsletterStatusSubscribeInput exists, input email will be subscribed.
    If input email user is already subscribed, status will be changed to 2 and
    optin mail is sent depending on shop config parameter blOrderOptInEmail.

  If user account for email and shop exists, input fields are overruled by existing user data.
  If user account for email and shop does not exist, new user will be created (no password, mininal data)
  """
  newsletterSubscribe(newsletterStatus: NewsletterStatusSubscribeInput): NewsletterStatus!

  basketAddItem(basketId: ID!, productId: ID!, amount: Float!): Basket!

  basketRemoveItem(basketId: ID!, basketItemId: ID!, amount: Float!): Basket!

  basketCreate(basket: BasketInput!): Basket!

  basketAddVoucher(basketId: ID!, voucherNumber: String!): Basket!

  basketRemoveVoucher(basketId: ID!, voucherId: ID!): Basket!

  placeOrder(basketId: ID!, confirmTermsAndConditions: Boolean = null, remark: String = null): Order!

  contactRequest(request: ContactRequestInput): Boolean!
}

input CustomerInput {
  email: String!

  password: String!

  birthdate: DateTime = null
}

type Customer {
  id: ID!

  firstName: String!

  lastName: String!

  email: String!

  customerNumber: String!

  birthdate: DateTime

  points: Int!

  registered: DateTime

  created: DateTime

  updated: DateTime

  reviews: [Review!]!

  newsletterStatus: NewsletterStatus

  deliveryAddresses: [DeliveryAddress!]!

  invoiceAddress: InvoiceAddress!

  basket(title: String!): Basket!

  baskets: [Basket!]!

  orders(pagination: PaginationInput = null): [Order!]!

  files: [OrderFile!]!
}

type NewsletterStatus {
  salutation: String!

  firstName: String!

  lastName: String!

  email: String!

  status: String!

  failedEmailCount: Int!

  subscribed: DateTime

  unsubscribed: DateTime

  updated: DateTime
}

type InvoiceAddress {
  salutation: String!

  firstName: String!

  lastName: String!

  company: String!

  additionalInfo: String!

  street: String!

  streetNumber: String!

  zipCode: String!

  city: String!

  vatID: String!

  phone: String!

  mobile: String!

  fax: String!

  created: DateTime

  updated: DateTime

  country: Country

  state: State
}

type Order {
  id: ID!

  orderNumber: Int!

  invoiceNumber: Int!

  paid: DateTime

  remark: String!

  cancelled: Boolean!

  invoiced: DateTime

  ordered: DateTime

  updated: DateTime

  invoiceAddress: OrderInvoiceAddress!

  deliveryAddress: OrderDeliveryAddress

  cost: OrderCost!

  delivery: OrderDelivery!

  vouchers: [Voucher!]!

  items: [OrderItem!]!

  payment: OrderPayment

  files: [OrderFile!]!
}

type OrderInvoiceAddress {
  salutation: String!

  email: String!

  firstName: String!

  lastName: String!

  company: String!

  additionalInfo: String!

  street: String!

  streetNumber: String!

  zipCode: String!

  city: String!

  vatID: String!

  phone: String!

  fax: String!

  country: Country

  state: State
}

type OrderDeliveryAddress {
  salutation: String!

  firstName: String!

  lastName: String!

  company: String!

  additionalInfo: String!

  street: String!

  streetNumber: String!

  zipCode: String!

  city: String!

  phone: String!

  fax: String!

  country: Country

  state: State
}

type OrderCost {
  total: Float!

  voucher: Float!

  discount: Float!

  delivery: Price!

  payment: Price!

  productNet: Price!

  productGross: OrderProductBruttoSum!

  currency: Currency!
}

type OrderProductBruttoSum {
  sum: Float!

  vats: [OrderProductVats!]!
}

type OrderProductVats {
  vatRate: Float!

  vatPrice: Float!
}

type OrderDelivery {
  trackingNumber: String!

  trackingURL: String!

  dispatched: DateTime

  provider: DeliveryProvider!
}

type DeliveryProvider {
  id: ID!

  active: Boolean!

  title: String!
}

type OrderItem {
  id: ID!

  amount: Float!

  sku: String!

  title: String!

  shortDescription: String!

  price: Price!

  itemPrice: Price!

  dimensions: ProductDimensions!

  insert: DateTime!

  timestamp: DateTime!

  cancelled: Boolean!

  bundle: Boolean!

  product: Product
}

type OrderPayment {
  id: ID!

  updated: DateTime

  payment: PaymentInterface

  values: [OrderPaymentValue!]!
}

type OrderPaymentValue {
  key: String!

  value: String!
}

type OrderFile {
  id: ID!

  filename: String!

  firstDownload: DateTime

  latestDownload: DateTime

  downloadCount: Int!

  maxDownloadCount: Int!

  validUntil: DateTime

  valid: Boolean!

  url: String!

  file: File
}

type File {
  id: ID!

  filename: String!

  onlyPaidDownload: Boolean!

  product: Product!
}

input NewsletterStatusInput {
  email: String!

  confirmCode: String!
}

input NewsletterStatusUnsubscribeInput {
  email: String!
}

input NewsletterStatusSubscribeInput {
  firstName: String = null

  lastName: String = null

  salutation: String = null

  email: String = null
}

input BasketInput {
  title: String!

  public: Boolean = false
}

input ContactRequestInput {
  email: String = ""

  firstName: String = ""

  lastName: String = ""

  salutation: String = ""

  subject: String = ""

  message: String = ""
}

"""
A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.
"""
type __Schema {
  """
  A list of all types supported by this server.
  """
  types: [__Type!]!

  """
  The type that query operations will be rooted at.
  """
  queryType: __Type!

  """
  If this server supports mutation, the type that mutation operations will be rooted at.
  """
  mutationType: __Type

  """
  If this server support subscription, the type that subscription operations will be rooted at.
  """
  subscriptionType: __Type

  """
  A list of all directives supported by this server.
  """
  directives: [__Directive!]!
}

"""
The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.

Depending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name and description, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.
"""
type __Type {
  kind: __TypeKind!

  name: String

  description: String

  fields(includeDeprecated: Boolean = false): [__Field!]

  interfaces: [__Type!]

  possibleTypes: [__Type!]

  enumValues(includeDeprecated: Boolean = false): [__EnumValue!]

  inputFields: [__InputValue!]

  ofType: __Type
}

"""
An enum describing what kind of type a given `__Type` is.
"""
enum __TypeKind {
  """
  Indicates this type is a scalar.
  """
  SCALAR

  """
  Indicates this type is an object. `fields` and `interfaces` are valid fields.
  """
  OBJECT

  """
  Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.
  """
  INTERFACE

  """
  Indicates this type is a union. `possibleTypes` is a valid field.
  """
  UNION

  """
  Indicates this type is an enum. `enumValues` is a valid field.
  """
  ENUM

  """
  Indicates this type is an input object. `inputFields` is a valid field.
  """
  INPUT_OBJECT

  """
  Indicates this type is a list. `ofType` is a valid field.
  """
  LIST

  """
  Indicates this type is a non-null. `ofType` is a valid field.
  """
  NON_NULL
}

"""
Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.
"""
type __Field {
  name: String!

  description: String

  args: [__InputValue!]!

  type: __Type!

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.
"""
type __InputValue {
  name: String!

  description: String

  type: __Type!

  """
  A GraphQL-formatted string representing the default value for this input value.
  """
  defaultValue: String
}

"""
One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.
"""
type __EnumValue {
  name: String!

  description: String

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.

In some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.
"""
type __Directive {
  name: String!

  description: String

  args: [__InputValue!]!

  isRepeatable: Boolean!

  locations: [__DirectiveLocation!]!
}

"""
A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.
"""
enum __DirectiveLocation {
  """
  Location adjacent to a query operation.
  """
  QUERY

  """
  Location adjacent to a mutation operation.
  """
  MUTATION

  """
  Location adjacent to a subscription operation.
  """
  SUBSCRIPTION

  """
  Location adjacent to a field.
  """
  FIELD

  """
  Location adjacent to a fragment definition.
  """
  FRAGMENT_DEFINITION

  """
  Location adjacent to a fragment spread.
  """
  FRAGMENT_SPREAD

  """
  Location adjacent to an inline fragment.
  """
  INLINE_FRAGMENT

  """
  Location adjacent to a variable definition.
  """
  VARIABLE_DEFINITION

  """
  Location adjacent to a schema definition.
  """
  SCHEMA

  """
  Location adjacent to a scalar definition.
  """
  SCALAR

  """
  Location adjacent to an object type definition.
  """
  OBJECT

  """
  Location adjacent to a field definition.
  """
  FIELD_DEFINITION

  """
  Location adjacent to an argument definition.
  """
  ARGUMENT_DEFINITION

  """
  Location adjacent to an interface definition.
  """
  INTERFACE

  """
  Location adjacent to a union definition.
  """
  UNION

  """
  Location adjacent to an enum definition.
  """
  ENUM

  """
  Location adjacent to an enum value definition.
  """
  ENUM_VALUE

  """
  Location adjacent to an input object type definition.
  """
  INPUT_OBJECT

  """
  Location adjacent to an input object field definition.
  """
  INPUT_FIELD_DEFINITION
}

type User {
  id: ID!

  email: String!
}

type NewsletterStatusUnsubscribe {
  email: String!
}

type Subscriber {
  userName: String!
}

type NewsletterStatusSubscribe {
  salutation: String!

  firstName: String!

  lastName: String!

  email: String!
}

type BasketDeliveryMethod implements DeliveryMethodInterface {
  id: ID!

  title: String!

  paymentTypes: [BasketPayment!]!

  position: Int!

  cost: Price!
}

type DeliveryMethod implements DeliveryMethodInterface {
  id: ID!

  title: String!

  paymentTypes: [BasketPayment!]!

  position: Int!
}

type Payment implements PaymentInterface {
  id: ID!

  active: Boolean!

  title: String!

  description: String!

  updated: DateTime
}

"""
Directs the executor to include this field or fragment only when the `if` argument is true.
"""
directive @include ("Included when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Directs the executor to skip this field or fragment when the `if` argument is true.
"""
directive @skip ("Skipped when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Marks an element of a GraphQL schema as no longer supported.
"""
directive @deprecated ("Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax (as specified by [CommonMark](https://commonmark.org/)." reason: String = "No longer supported") on FIELD_DEFINITION|ENUM_VALUE

schema {
  query: Query
  mutation: Mutation
}
