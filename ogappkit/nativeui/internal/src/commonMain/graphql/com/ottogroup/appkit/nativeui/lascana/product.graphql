query ProductDetail($productId: ID!, $shopTheLook: Boolean!) {
    product(productId: $productId) {
        id
        sku
        title
        parentId
        flags {
            type
            priority
        }
        manufacturer {
            title
            shortdesc
        }
        distributingCompany {
            companies {
                companyName
                city
                street
                postalCode
                countryCode
                url
                email
                phone
            }
        }
        seo {
            url
        }
        ...availabilityFields
        variantLabels
        variantValues
        variants {
            ...variantInfo
        }
        shortDescription
        ...completePrice
        paybackPoints
        imageGallery {
            thumb
            images {
                image
                icon
            }
        }
        positionedPicture
        ...ratingData
        detailPageAttributes: attributes(isForDetailPage: true) {
            attribute {
                title
                groupName
            }
            value
        }
        nonDetailPageAttributes: attributes(isForDetailPage: false) {
            attribute {
                title
                groupName
            }
            value
        }
        shopTheLook @include(if: $shopTheLook){
            mainImagePath
            articles {
                ...minimalProduct
            }
        }
        moreFromSeries {
            articles {
                article {
                    ...minimalProduct
                }
            }
        }
        flyouts {
            sizeGuide
        }
        breadcrumb
    }
}

fragment availabilityFields on Product {
    deliveryInformation
    stock {
        stock
        stockStatus
    }
    flyouts {
        reminder
    }
}

fragment completePrice on Product {
    price {
        ...priceFields
    }
    listPrice {
        ...priceFields
    }
}

fragment priceFields on Price {
    price
    currency {
        name
    }
}

fragment variantInfo on Product {
    id
    sku
    variantValues
    ...availabilityFields
    imageGallery {
        thumb
    }
    positionedPicture
    ...completePrice
}

fragment ratingData on Product {
    rating {
        rating
        count
    }
    reviews {
        title
        text
        rating
        createAt
        reviewer {
            firstName
        }
    }
    flyouts {
        reviews
    }
}
