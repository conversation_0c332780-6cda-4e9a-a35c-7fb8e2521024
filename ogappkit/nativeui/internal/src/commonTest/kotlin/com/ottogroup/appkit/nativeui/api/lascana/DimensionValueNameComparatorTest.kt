package com.ottogroup.appkit.nativeui.api.lascana

import kotlin.test.Test
import kotlin.test.assertEquals

class DimensionValueNameComparatorTest {

    @Test
    fun `sorts US sizes correctly`() {
        assertEquals(
            listOf("XS", "S", "M", "L", "XL", "XXL"),
            listOf("XL", "S", "M", "XXL", "XS", "L").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts sizes with US size prefixes correctly`() {
        assertEquals(
            listOf("XS (32/34)", "S (36/38)", "M (40/42)", "L (44/46)", "XL (48/50)", "XXL (52/54)"),
            listOf("XL (48/50)", "S (36/38)", "M (40/42)", "XXL (52/54)", "XS (32/34)", "L (44/46)").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts numeric sizes correctly`() {
        assertEquals(
            listOf("32", "34", "46", "100"),
            listOf("100", "46", "32", "34").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts combined numeric sizes correctly`() {
        assertEquals(
            listOf("32/34", "46/48", "100-102"),
            listOf("100-102", "32/34", "46/48").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts cup sizes correctly`() {
        assertEquals(
            listOf("AA", "A", "C", "F"),
            listOf("F", "A", "AA", "C").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts cup sizes with prefix correctly`() {
        assertEquals(
            listOf("Cup AA", "Cup A", "Cup C", "Cup F"),
            listOf("Cup C", "Cup A", "Cup AA", "Cup F").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts plain text correctly`() {
        assertEquals(
            listOf("black", "creme", "purple", "white"),
            listOf("white", "creme", "black", "purple").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts EU-style bra sizes correctly`() {
        assertEquals(
            listOf("70AA", "75AA", "70A", "80A", "90C", "120F"),
            listOf("70A", "70AA", "120F", "90C", "75AA", "80A").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts bra sizes with cup prefix cup first correctly`() {
        assertEquals(
            listOf("Cup AA 70", "Cup AA 75", "Cup A 70", "Cup A 80", "Cup C 90", "Cup F 120"),
            listOf("Cup A 70", "Cup AA 70", "Cup F 120", "Cup C 90", "Cup AA 75", "Cup A 80").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts bra sizes with cup prefix width first correctly`() {
        assertEquals(
            listOf("70 Cup AA", "75 Cup AA", "70 Cup A", "80 Cup A", "90 Cup C", "120 Cup F"),
            listOf("70 Cup A", "70 Cup AA", "120 Cup F", "90 Cup C", "75 Cup AA", "80 Cup A").sortedWith(DimensionValueNameComparator)
        )
    }

    @Test
    fun `sorts voucher prices correctly`() {
        assertEquals(
            listOf("10 €", "25 €", "50 €", "75 €", "100 €"),
            listOf("100 €", "50 €", "10 €", "75 €", "25 €").sortedWith(DimensionValueNameComparator)
        )
    }
}
