package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.plugin
import io.ktor.utils.io.ByteReadChannel
import kotlin.test.Test

class LascanaHttpClientProviderTest {

    private val configProvider = OGNativeConfigProvider()
    private val httpClientProvider = LascanaHttpClientProviderImpl(
        configProvider,
        customEngine = MockEngine { respond(content = ByteReadChannel("")) }
    )

    @Test
    fun `lascana HTTP client uses cookies plugin`() {
        httpClientProvider.httpClient.plugin(cookiesPlugin(configProvider))
    }
}
