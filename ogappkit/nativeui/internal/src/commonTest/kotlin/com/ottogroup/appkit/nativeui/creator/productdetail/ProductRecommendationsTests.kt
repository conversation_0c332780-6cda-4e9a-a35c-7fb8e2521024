package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.toRecommendedProduct
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.MoreFromTheSeriesRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductRecommendationsTests {

    @Test
    fun `static recommendations displays successfully loaded products`() = runProductDetailScreenCreatorTest { testObjects ->
        val ids = listOf("123456", "789012")
        testObjects.fakeRecommendationsRepository.staticProducts = ids.map {
            testProduct.copy(id = it, title = it).toRecommendedProduct(isWishlisted = false)
        }
        val config = StaticProductRecommendations.Config(
            productIds = ids,
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateStaticViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    StaticProductRecommendations(
                        state = LoadingComponent.State.Done(
                            content = ProductRecommendations.Content(
                                products = listOf(
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "123456",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "123456",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "123456",
                                    ),
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "789012",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "789012",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "789012",
                                    ),
                                ),
                                trackingId = StaticProductRecommendations.TRACKING_ID,
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `static recommendations is omitted when products cannot be gotten`() = runProductDetailScreenCreatorTest { testObjects ->
        testObjects.fakeRecommendationsRepository.staticProducts = null
        val ids = listOf("123456", "789012")
        val config = StaticProductRecommendations.Config(
            productIds = ids,
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateStaticViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `static recommendations is omitted when recommended products list is empty`() = runProductDetailScreenCreatorTest { testObjects ->
        testObjects.fakeRecommendationsRepository.staticProducts = emptyList()
        val ids = listOf("123456", "789012")
        val config = StaticProductRecommendations.Config(
            productIds = ids,
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateStaticViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `recently viewed recommendations displays successfully loaded products`() = runProductDetailScreenCreatorTest { testObjects ->
        val ids = listOf("123456", "789012")
        testObjects.fakeRecommendationsRepository.recentlyViewedProducts = ids.map {
            testProduct.copy(id = it, title = it).toRecommendedProduct(isWishlisted = false)
        }
        val config = RecentlyViewedRecommendations.Config()

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateRecentlyViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    RecentlyViewedRecommendations(
                        state = LoadingComponent.State.Done(
                            content = ProductRecommendations.Content(
                                products = listOf(
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "123456",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "123456",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "123456",
                                    ),
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "789012",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "789012",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "789012",
                                    ),
                                ),
                                trackingId = RecentlyViewedRecommendations.TRACKING_ID,
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `recently viewed recommendations is omitted when products cannot be gotten`() = runProductDetailScreenCreatorTest { testObjects ->
        testObjects.fakeRecommendationsRepository.recentlyViewedProducts = null
        val config = RecentlyViewedRecommendations.Config()

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateRecentlyViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `recently viewed recommendations is omitted when recommended products list is empty`() = runProductDetailScreenCreatorTest { testObjects ->
        testObjects.fakeRecommendationsRepository.recentlyViewedProducts = emptyList()
        val config = RecentlyViewedRecommendations.Config()

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateRecentlyViewed(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `shop the look recommendations displays successfully loaded products`() = runProductDetailScreenCreatorTest { testObjects ->
        val ids = listOf("123456", "789012")
        testObjects.fakeRecommendationsRepository.shopTheLookRecos =
            ProductRecommendations.Content(
                products = ids.map {
                    testProduct.copy(id = it, title = it).toRecommendedProduct(isWishlisted = false)
                },
                image = Image("https://example.com/image.jpg", null),
                trackingId = ShopTheLookRecommendations.TRACKING_ID,
            )
        val config = ShopTheLookRecommendations.Config()

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateShopTheLook(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ShopTheLookRecommendations(
                        state = LoadingComponent.State.Done(
                            content = ProductRecommendations.Content(
                                products = listOf(
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "123456",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "123456",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "123456",
                                    ),
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "789012",
                                        secondaryId = null,
                                        brandName = testProduct.brand?.name,
                                        title = "789012",
                                        price = testProduct.price,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "789012",
                                    ),
                                ),
                                image = Image("https://example.com/image.jpg", null),
                                trackingId = ShopTheLookRecommendations.TRACKING_ID,
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `more from the series recommendations displays successfully loaded products`() = runProductDetailScreenCreatorTest { testObjects ->
        val ids = listOf("123456", "789012")
        testObjects.fakeRecommendationsRepository.moreFromTheSeriesRecos =
            ProductRecommendations.Content(
                products = ids.map {
                    testProduct.copy(id = it, title = it, brand = null, price = Price.None)
                        .toRecommendedProduct(isWishlisted = false)
                },
                trackingId = MoreFromTheSeriesRecommendations.TRACKING_ID,
            )
        val config = MoreFromTheSeriesRecommendations.Config()

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            ComponentConfigs(listOf(config)),
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateMoreFromTheSeries(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    MoreFromTheSeriesRecommendations(
                        state = LoadingComponent.State.Done(
                            content = ProductRecommendations.Content(
                                products = listOf(
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "123456",
                                        secondaryId = null,
                                        brandName = null,
                                        title = "123456",
                                        price = null,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "123456",
                                    ),
                                    ProductRecommendations.RecommendedProduct(
                                        productId = "789012",
                                        secondaryId = null,
                                        brandName = null,
                                        title = "789012",
                                        price = null,
                                        image = testProduct.images.first(),
                                        isWishlisted = false,
                                        productIdForWishlisting = "789012",
                                    ),
                                ),
                                trackingId = MoreFromTheSeriesRecommendations.TRACKING_ID,
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
            cancelAndIgnoreRemainingEvents()
        }
    }

    private fun loadingStateStaticViewed(config: StaticProductRecommendations.Config) = successfulDetailScreenOf(
        StaticProductRecommendations(
            state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
            config = config,
        )
    )

    private fun loadingStateRecentlyViewed(config: RecentlyViewedRecommendations.Config) = successfulDetailScreenOf(
        RecentlyViewedRecommendations(
            state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
            config = config,
        )
    )

    private fun loadingStateShopTheLook(config: ShopTheLookRecommendations.Config) = successfulDetailScreenOf(
        ShopTheLookRecommendations(
            state = LoadingComponent.State.Loading(Placeholders.shopTheLookRecommendations),
            config = config,
        )
    )

    private fun loadingStateMoreFromTheSeries(config: MoreFromTheSeriesRecommendations.Config) = successfulDetailScreenOf(
        MoreFromTheSeriesRecommendations(
            state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
            config = config,
        )
    )
}
