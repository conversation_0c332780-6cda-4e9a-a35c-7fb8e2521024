package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.RecommendationsRepository
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.MoreFromTheSeriesRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendationsConfig
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

internal class FakeRecommendationsRepository : RecommendationsRepository {
    var recentlyViewedProducts: List<ProductRecommendations.RecommendedProduct>? = emptyList()
    override fun getRecentlyViewedRecommendations(
        productParentId: String,
        config: ProductRecommendationsConfig
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flowOf(
            recentlyViewedProducts?.let {
                Result.Success(
                    ProductRecommendations.Content(
                        products = it,
                        trackingId = RecentlyViewedRecommendations.TRACKING_ID,
                    )
                )
            } ?: Result.Failure(ComparableError("No products"))
        ).asOperation()
    }

    var staticProducts: List<ProductRecommendations.RecommendedProduct>? = emptyList()
    override fun getStaticRecommendations(
        config: StaticProductRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flowOf(
            staticProducts?.let {
                Result.Success(
                    ProductRecommendations.Content(
                        products = it,
                        trackingId = StaticProductRecommendations.TRACKING_ID,
                    )
                )
            } ?: Result.Failure(ComparableError("No products"))
        ).asOperation()
    }

    var dyProducts: List<ProductRecommendations.RecommendedProduct>? = emptyList()
    override fun getDynamicYieldRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flowOf(
            dyProducts?.let {
                Result.Success(
                    ProductRecommendations.Content(
                        products = it,
                        trackingId = "",
                    )
                )
            } ?: Result.Failure(ComparableError("No products"))
        ).asOperation()
    }

    var shopTheLookRecos: ProductRecommendations.Content? = null
    override fun getShopTheLookRecommendations(
        shopTheLook: ShopTheLook?,
        config: ShopTheLookRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flowOf(
            shopTheLookRecos?.let {
                Result.Success(it)
            } ?: Result.Failure(ComparableError("No products"))
        ).asOperation()
    }

    var moreFromTheSeriesRecos: ProductRecommendations.Content? = null
    override fun getMoreFromTheSeriesRecommendations(
        moreFromTheSeries: MoreFromTheSeries?,
        config: MoreFromTheSeriesRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flowOf(
            moreFromTheSeriesRecos?.let {
                Result.Success(it)
            } ?: Result.Failure(ComparableError("No products"))
        ).asOperation()
    }
}
