package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductInformation
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductInformationTests {

    @Test
    fun `ProductInformation component is returned correctly`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(ProductInformation.Config)
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    ProductInformation(
                        state = LoadingComponent.State.Loading(Placeholders.productInformation)
                    )
                ),
                awaitItem()
            )
            assertEquals(
                successfulDetailScreenOf(
                    ProductInformation(
                        state = LoadingComponent.State.Done(
                            ProductInformation.Content(
                                description = ProductInformation.Description(
                                    articleNumber = "1234556",
                                    bulletPoints = listOf("one", "two"),
                                    text = "Long text",
                                    documents = listOf(Link("Manual", "https://www.example.com/files/manual.pdf")),
                                ),
                                details = ProductInformation.Details(
                                    Information.AttributesTable(listOf())
                                ),
                                brand = ProductInformation.Brand(name = "Lascana", description = "This is a cool brand"),
                                importantInformation = ProductInformation.ImportantInformation(
                                    distributingCompanies = listOf(
                                        ProductInformation.ImportantInformation.DistributingCompany(
                                            name = "ACME Corp",
                                            data = "Fakestreet 123\n12345 Townsville, XY\n+1234567890\nhttps://www.example.com"
                                        ),
                                        ProductInformation.ImportantInformation.DistributingCompany(
                                            name = "Second Inc.",
                                            data = "Streetlane 1\n54321 Secondtown, XY\n+1234567890\nhttps://www.example.com"
                                        )
                                    )
                                ),
                                articleStandards = ArticleStandards(listOf(ArticleStandards.Seal.CottonMadeInAfrica)),
                            )
                        )
                    )
                ),
                awaitItem()
            )
        }
    }
}
