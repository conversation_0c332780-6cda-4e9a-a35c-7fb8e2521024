package com.ottogroup.appkit.nativeui.creator.addtobasketsuccess

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.api.toRecommendedProduct
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.AddedProduct
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ContinueShoppingButton
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ShowBasketButton
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import com.ottogroup.appkit.test.createTestProduct
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest

class AddToBasketSuccessScreenCreatorTest {

    @Test
    fun `screen returns failure if product fails to be loaded`() = runTest {
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(AddedProduct.Config)
        )

        val error = Exception()

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            MutableStateFlow(Result.Failure<Product>(error)).asOperation(),
            configs
        ).test {
            // first we display loading
            assertEquals(
                successfulBasketSuccessScreenOf(
                    AddedProduct(
                        state = LoadingComponent.State.Loading(
                            Placeholders.addedProduct
                        )
                    )
                ),
                awaitItem()
            )

            assertEquals(
                Result.Failure(error),
                awaitItem()
            )
        }
    }

    @Test
    fun `AddedProduct is displayed`() = runTest {
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(AddedProduct.Config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        )

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            AddToBasketSuccessScreenCreatorTestObjects.productFlow(product),
            configs
        ).test {
            // skip loading state
            awaitItem()

            assertEquals(
                successfulBasketSuccessScreenOf(
                    AddedProduct(
                        state = LoadingComponent.State.Done(
                            AddedProduct.Content(
                                productId = product.id,
                                brandName = product.brand!!.name,
                                title = product.shortTitle,
                                selectedDimensionValues = listOf(
                                    AddedProduct.Content.SelectedDimensionValue(
                                        ModelMapping.COLOR_VARIANT_LABEL,
                                        "Gold"
                                    ),
                                ),
                                price = product.price,
                                image = product.images.first(),
                            ),
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `AddedProduct for voucher is displayed with different dimensions`() = runTest {
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(AddedProduct.Config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Value"),
            variantData = listOf(VariantData(listOf("Standard", "10 €")))
        ).copy(
            voucherSpec = VoucherSpec(false)
        )

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            AddToBasketSuccessScreenCreatorTestObjects.productFlow(product),
            configs
        ).test {
            // skip loading state
            awaitItem()

            assertEquals(
                successfulBasketSuccessScreenOf(
                    AddedProduct(
                        state = LoadingComponent.State.Done(
                            AddedProduct.Content(
                                productId = product.id,
                                brandName = product.brand!!.name,
                                title = product.shortTitle,
                                selectedDimensionValues = listOf(
                                    AddedProduct.Content.SelectedDimensionValue("Value", "10 €"),
                                ),
                                price = product.price,
                                image = product.images.first(),
                            ),
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ContinueShoppingButton is displayed`() = runTest {
        val config = ContinueShoppingButton.Config(continueShoppingUrl = "https://example.com")
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            AddToBasketSuccessScreenCreatorTestObjects.productFlow(testProduct),
            configs
        ).test {
            // skip loading state
            awaitItem()

            assertEquals(
                successfulBasketSuccessScreenOf(
                    ContinueShoppingButton(
                        state = LoadingComponent.State.Done(
                            ContinueShoppingButton.Content(
                                continueShoppingUrl = "https://example.com"
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ShowBasketButton is displayed`() = runTest {
        val config = ShowBasketButton.Config(basketUrl = "app://basket")
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            AddToBasketSuccessScreenCreatorTestObjects.productFlow(testProduct),
            configs
        ).test {
            // skip loading state
            awaitItem()

            assertEquals(
                successfulBasketSuccessScreenOf(
                    ShowBasketButton(
                        state = LoadingComponent.State.Done(
                            ShowBasketButton.Content(
                                basketUrl = "app://basket"
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    /** Scenarios are more fully tested in ProductRecommendationsTests */
    @Test
    fun `recommendations are displayed`() = runTest {
        val configs: ComponentConfigs<AddToBasketSuccessComponentConfig> = ComponentConfigs(
            listOf(
                StaticProductRecommendations.Config(),
                RecentlyViewedRecommendations.Config(),
                DynamicYieldRecommendations.Config(),
            )
        )

        val staticRecos = listOf(testProduct.copy(id = "1", title = "1").toRecommendedProduct(false))
        val recentRecos = listOf(testProduct.copy(id = "2", title = "2").toRecommendedProduct(false))
        val dyRecos = listOf(testProduct.copy(id = "2", title = "2").toRecommendedProduct(true))
        AddToBasketSuccessScreenCreatorTestObjects.fakeRecommendationsRepository.apply {
            staticProducts = staticRecos
            recentlyViewedProducts = recentRecos
            dyProducts = dyRecos
        }

        AddToBasketSuccessScreenCreatorTestObjects.creator.createScreen(
            AddToBasketSuccessScreenCreatorTestObjects.productFlow(testProduct),
            configs
        ).test {
            assertEquals(
                successfulBasketSuccessScreenOf(
                    StaticProductRecommendations(
                        state = LoadingComponent.State.Done(
                            ProductRecommendations.Content(
                                products = staticRecos,
                                trackingId = StaticProductRecommendations.TRACKING_ID,
                            )
                        ),
                        config = StaticProductRecommendations.Config()
                    ),
                    RecentlyViewedRecommendations(
                        state = LoadingComponent.State.Done(
                            ProductRecommendations.Content(
                                products = recentRecos,
                                trackingId = RecentlyViewedRecommendations.TRACKING_ID,
                            )
                        ),
                        config = RecentlyViewedRecommendations.Config()
                    ),
                    DynamicYieldRecommendations(
                        state = LoadingComponent.State.Done(
                            ProductRecommendations.Content(
                                products = dyRecos,
                                trackingId = "",
                            )
                        ),
                        config = DynamicYieldRecommendations.Config()
                    ),
                ),
                expectMostRecentItem()
            )
        }
    }
}
