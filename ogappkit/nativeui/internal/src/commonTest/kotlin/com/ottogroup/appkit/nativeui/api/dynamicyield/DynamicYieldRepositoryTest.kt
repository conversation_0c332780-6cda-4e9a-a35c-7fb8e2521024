package com.ottogroup.appkit.nativeui.api.dynamicyield

import app.cash.turbine.test
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.nativeui.ProductIdsFromUrlParser
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.test.FakeNativeTrackingProvider
import com.ottogroup.appkit.test.FakeWishlistRepository
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldEvent
import com.ottogroup.appkit.tracking.services.dynamicyield.api.DynamicYieldNetworkDataSource
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Choice
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.ProductData
import com.ottogroup.appkit.tracking.services.dynamicyield.api.response.Variation
import com.ottogroup.appkit.tracking.services.dynamicyield.from
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.jsonObject

class DynamicYieldRepositoryTest {

    private val dataSource = FakeDataSource()
    private val wishlistRepository = FakeWishlistRepository()
    private val nativeTrackingProvider = FakeNativeTrackingProvider()
    private val configProvider = OGNativeConfigProvider().apply {
        update(
            OGNativeConfig(
                graphQLBackend = OGNativeConfig.Backend(""),
                tenant = OGNativeConfig.NativeApiTenant.NONE,
                productIdRegex = "^https:\\/\\/(?:www\\.lascana\\.de|stage-shop\\.lsc1\\.ber\\.basefarm\\.net)\\/.+-(?<productId>[0-9]+)\\.html?(?:\\?variantId=(?<variantId>[0-9_]+))?$",
            )
        )
    }
    private val productIdsFromUrlParser = ProductIdsFromUrlParser(configProvider = configProvider)
    private val dynamicYieldRepository = DynamicYieldRepositoryImpl(
        dataSource,
        wishlistRepository,
        nativeTrackingProvider,
        productIdsFromUrlParser
    )

    @Test
    fun `getRecommendations requests recos from Dynamic Yield and updates wishlist state`() = runTest {
        val expectedProduct = ProductRecommendations.RecommendedProduct(
            "**********",
            "**********",
            "LASCANA",
            "Strickkleid",
            Price("EUR", 8399),
            Image(
                url = "https://bilder.lascana.de/styles/479x684/lascana-strickkleid-beige-meliert-560233140.jpg",
                thumbnailUrl = "https://bilder.lascana.de/styles/479x684/lascana-strickkleid-beige-meliert-560233140.jpg",
            ),
            isWishlisted = false,
            productIdForWishlisting = "**********"
        )

        dynamicYieldRepository.getRecommendations(
            "sku",
            DynamicYieldRecommendations.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            listOf(expectedProduct),
                            trackingId = "group_selector",
                        )
                    )
                ),
                awaitItem()
            )

            wishlistRepository.addProductToWishlist(expectedProduct.productIdForWishlisting)
            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            listOf(expectedProduct.copy(isWishlisted = true)),
                            trackingId = "group_selector",
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `getRecommendations with empty response from DY results in empty recos`() = runTest {
        dataSource.response = Result.Success(emptyList())

        dynamicYieldRepository.getRecommendations(
            "sku",
            DynamicYieldRecommendations.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            emptyList(),
                            trackingId = "group_selector",
                        )
                    )
                ),
                awaitItem()
            )

            awaitComplete()
        }
    }

    @Test
    fun `getRecommendations with error response from DY results in error recos`() = runTest {
        val error = RuntimeException("Something went wrong")
        dataSource.response = Result.Failure(error)

        dynamicYieldRepository.getRecommendations(
            "sku",
            DynamicYieldRecommendations.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(Result.Failure<ProductRecommendations.Content>(error)),
                awaitItem()
            )

            awaitComplete()
        }
    }

    @Test
    fun `getBanner with valid response from DY results in successful banner`() = runTest {
        dataSource.response = Result.Success(
            listOf(
                Choice.CustomChoice(
                    name = "Banner",
                    variations = listOf(
                        Variation(
                            id = "bannerId",
                            payload = Variation.Payload.CustomJsonPayload(
                                data = lenientJson.parseToJsonElement(
                                    """
                                    {
                                      "headline": "Miami Summer Days",
                                      "text": "20% on everything",
                                      "discountText": "Only valid 13.04. – 16.04.2025 and only in the app.",
                                      "discountValue": "MIAMI20",
                                      "linkText": "20% on everything",
                                      "linkUrl": "https://www.lascana.de/bekleidung",
                                      "bgcolor": "#5B1F67"
                                    }
                                    """.trimIndent()
                                ).jsonObject
                            )
                        )
                    )
                )
            )
        )

        val eCommerceItem = ECommerceItem("product", "123123")
        dynamicYieldRepository.getBanner(
            "sku",
            eCommerceItem = eCommerceItem,
            DynamicYieldBanner.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
                slotId = "app_pdp_top",
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        DynamicYieldBanner.Content(
                            text = "Miami Summer Days: 20% on everything",
                            infoText = "Only valid 13.04. – 16.04.2025 and only in the app.",
                            promoCode = "MIAMI20",
                            trackingEvents = DynamicYieldBanner.TrackingEvents(
                                view = View.ProductDetailPromotion(
                                    item = eCommerceItem.copy(coupon = "MIAMI20"),
                                    creativeSlot = "app_pdp_top",
                                    promotionId = "bannerId",
                                    promotionName = "Miami Summer Days",
                                ),
                                click = Interaction.ProductDetailSelectPromotion(
                                    item = eCommerceItem.copy(coupon = "MIAMI20"),
                                    creativeSlot = "app_pdp_top",
                                    promotionId = "bannerId",
                                    promotionName = "Miami Summer Days",
                                )
                            )
                        )
                    )
                ),
                awaitItem()
            )
            awaitComplete()
        }
    }

    @Test
    fun `getBanner with empty response from DY results in error banner`() = runTest {
        dataSource.response = Result.Success(emptyList())

        dynamicYieldRepository.getBanner(
            "sku",
            ECommerceItem("product", "123123"),
            DynamicYieldBanner.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
                slotId = "app_pdp_top",
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            val actual = awaitItem()
            assertIs<Result.Failure<DynamicYieldBanner.Content>>((actual as Operation.Complete).result)

            awaitComplete()
        }
    }

    @Test
    fun `getBanner with error response from DY results in error banner`() = runTest {
        val error = RuntimeException("Something went wrong")
        dataSource.response = Result.Failure(error)

        dynamicYieldRepository.getBanner(
            "sku",
            ECommerceItem("product", "123123"),
            DynamicYieldBanner.Config(
                selectorNames = listOf("selector"),
                selectorGroups = listOf("group"),
                slotId = "app_pdp_top",
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(Result.Failure<DynamicYieldBanner.Content>(error)),
                awaitItem()
            )

            awaitComplete()
        }
    }
}

private class FakeDataSource : DynamicYieldNetworkDataSource {
    var response: Result<List<Choice>> = Result.Success(
        listOf(
            Choice.RecommendationsChoice(
                name = "app zuletzt gesehen",
                variations = listOf(
                    Variation<Variation.Payload.RecommendationsPayload>(
                        id = "102632354",
                        payload = Variation.Payload.RecommendationsPayload(
                            data = Variation.Payload.SlotData(
                                slots = listOf(
                                    Variation.Payload.SlotData.Slot(
                                        sku = "67286322_48",
                                        slotId = "mIOkdHlw",
                                        productData = ProductData(
                                            brand = "LASCANA",
                                            name = "Strickkleid",
                                            url = "https://www.lascana.de/badezehentrenner-venice-beach-**********.html?variantId=**********",
                                            imageUrl = "https://bilder.lascana.de/styles/479x684/lascana-strickkleid-beige-meliert-560233140.jpg",
                                            price = 83.99f,
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )
    )

    override suspend fun chooseVariations(
        event: DynamicYieldEvent.PageView,
        selectorNames: List<String>,
        selectorGroups: List<String>
    ): Result<List<Choice>> {
        if (event != DynamicYieldEvent.from(View.ProductDetailViewItem(ECommerceItem(name = "", id = "sku")))) {
            return Result.Failure(Exception("Invalid event"))
        }

        if (selectorNames != listOf("selector")) {
            return Result.Failure(Exception("Invalid selectorNames"))
        }

        if (selectorGroups != listOf("group")) {
            return Result.Failure(Exception("Invalid selectorGroups"))
        }

        return response
    }
}
