package com.ottogroup.appkit.test

import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner.TrackingEvents
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View

internal val bannerTrackingEvents = TrackingEvents(
    view = View.ProductDetailPromotion(
        itemId = "123",
        itemName = "Item 1",
        coupon = "C0D3",
    ),
    click = Interaction.ProductDetailSelectPromotion(
        itemId = "123",
        itemName = "Item 1",
        coupon = "C0D3",
    )
)

internal val testBannerContent = DynamicYieldBanner.Content(
    "20% off",
    "Conditions apply",
    "C0D3",
    bannerTrackingEvents,
)
