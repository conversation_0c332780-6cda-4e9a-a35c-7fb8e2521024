package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.Contextual
import com.ottogroup.appkit.nativeui.api.FromNothing
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductHeader
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.map

class ReactiveProductDetailScreenCreatorTest {

    @Test
    fun `only relevant product data updates trigger component recreation`() =
        runProductDetailScreenCreatorTest { testObjects ->
            // this test is of course only exemplary and not covering all components

            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(ProductHeader.Config)
            )

            val productFlow = MutableSharedFlow<Result<Product>>()
            testObjects.creator.createScreen(
                productFlow.asOperation().map { Contextual(FromNothing, it) },
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(
                        ProductHeader(
                            state = LoadingComponent.State.Loading(
                                Placeholders.productHeader
                            ),
                        )
                    ),
                    awaitItem()
                )
                expectNoEvents()

                productFlow.emit(Result.Success(testProduct))
                assertEquals(
                    successfulDetailScreenOf(
                        ProductHeader(
                            state = LoadingComponent.State.Done(
                                ProductHeader.Content(
                                    title = testProduct.shortTitle,
                                    brandName = testProduct.brand!!.name,
                                    sharingData = ProductHeader.SharingData(url = testProduct.webShopUrl!!),
                                    isWishlisted = false,
                                    productIdForWishlisting = testProduct.id,
                                    productId = testProduct.id,
                                )
                            )
                        )
                    ),
                    awaitItem()
                )

                // changing product attributes not relevant to the header should not trigger an update
                productFlow.emit(
                    Result.Success(
                        testProduct.copy(
                            information = Information(
                                articleNumber = "123123",
                                description = "Changed description",
                                bulletPoints = listOf(),
                                attributesTable = Information.AttributesTable(emptyList()),
                                distributingCompanies = emptyList(),
                                documents = emptyList(),
                                articleStandards = null,
                            )
                        )
                    )
                )
                expectNoEvents()

                // changing product attributes relevant to the header should trigger an update
                productFlow.emit(
                    Result.Success(
                        testProduct.copy(
                            shortTitle = "Changed title",
                        )
                    )
                )
                assertEquals(
                    successfulDetailScreenOf(
                        ProductHeader(
                            state = LoadingComponent.State.Done(
                                ProductHeader.Content(
                                    title = "Changed title",
                                    brandName = testProduct.brand!!.name,
                                    sharingData = ProductHeader.SharingData(url = testProduct.webShopUrl!!),
                                    isWishlisted = false,
                                    productIdForWishlisting = testProduct.id,
                                    productId = testProduct.id,
                                )
                            )
                        )
                    ),
                    awaitItem()
                )
            }
        }
}
