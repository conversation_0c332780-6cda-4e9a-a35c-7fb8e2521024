package com.ottogroup.appkit.nativeui.creator.reviews

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.creator.ProductReviewsScreenCreator
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsScreen
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow

internal object ProductReviewsScreenCreatorTestObjects {
    val creator = ProductReviewsScreenCreator()

    fun reviewsFlow(productReviews: ProductReviews): Flow<Operation<ProductReviews>> =
        MutableStateFlow(Result.Success(productReviews)).asOperation()
}

internal fun successfulDetailScreenOf(vararg components: ProductReviewsComponent): Result<ProductReviewsScreen> {
    return Result.Success(ProductReviewsScreen(components.asList()))
}
