package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.api.lascana.data.complexTestQueryProduct
import com.ottogroup.appkit.nativeui.api.lascana.data.createQueryProduct
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Dimension.DimensionType
import com.ottogroup.appkit.nativeui.model.domain.SizeDimensionMatrix
import com.ottogroup.appkit.test.createDimensionValue
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class VariantConstructionTest {

    @Test
    fun `correctly constructs empty dimensions without fusing from product with no variants`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
            variantData = emptyList()
        )

        val domainProduct = mapping.toProduct(queryProduct)
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = emptyList(),
            ),
            Dimension(
                name = "Size",
                type = DimensionType.DEFAULT,
                values = emptyList(),
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs empty dimensions with fusing from product with no variants`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = "Size")

        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
            variantData = emptyList()
        )

        val domainProduct = mapping.toProduct(queryProduct)
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = emptyList(),
            ),
            Dimension(
                name = "Size",
                type = DimensionType.DEFAULT,
                values = emptyList(),
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs variants from two dimensions`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
            variantData = listOf(
                listOf("Red", "M"),
                listOf("Red", "S"),
                listOf("Blue", "S"),
                listOf("Blue", "M"),
                listOf("Black", "L"),
            ).map(::VariantData)
        )

        val domainProduct = mapping.toProduct(queryProduct)
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "Black",
                        productId = "Black_L",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 100,
                        )
                    ),
                    createDimensionValue(
                        text = "Blue",
                        productId = "Blue_M",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 200,
                        )
                    ),
                    createDimensionValue(
                        text = "Red",
                        productId = "Red_M",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 200,
                        )
                    ),
                )
            ),
            Dimension(
                name = "Size",
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "S",
                        productId = "Red_S",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "M",
                        productId = "Red_M",
                        hasThumb = false,
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs variants from three dimensions`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Unterbrustweite")
        val colors = listOf("blau", "rot", "schwarz")
        val cups = listOf("A", "B", "C")
        val sizes = listOf("70", "80", "90")
        val variantValues = buildList {
            for (color in colors) {
                for (cup in cups) {
                    for (size in sizes) {
                        add(listOf(color, cup, size))
                    }
                }
            }
        }

        val queryProduct = createQueryProduct(
            variantLabels = variantLabels,
            variantData = variantValues.map(::VariantData)
        )
        val domainProduct = mapping.toProduct(queryProduct)
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "Blau",
                        productId = "Blau_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                    createDimensionValue(
                        text = "Rot",
                        productId = "Rot_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                    createDimensionValue(
                        text = "Schwarz",
                        productId = "Schwarz_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                )
            ),
            Dimension(
                name = "Cup",
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "A",
                        productId = "Blau_A_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "B",
                        productId = "Blau_B_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "C",
                        productId = "Blau_C_70",
                        hasThumb = false,
                    ),
                )
            ),
            Dimension(
                name = "Unterbrustweite",
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "70",
                        productId = "Blau_A_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "80",
                        productId = "Blau_A_80",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "90",
                        productId = "Blau_A_90",
                        hasThumb = false,
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs variants from three dimensions and fuses them to two dimensions`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = "Size")

        val domainProduct = mapping.toProduct(complexTestQueryProduct)
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "Blau",
                        productId = "Blau_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                    createDimensionValue(
                        text = "Rot",
                        productId = "Rot_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                    createDimensionValue(
                        text = "Schwarz",
                        productId = "Schwarz_A_70",
                        hasThumb = true,
                    ).copy(
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 900,
                        )
                    ),
                )
            ),
            Dimension(
                name = "Size",
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "70A",
                        productId = "Blau_A_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "80A",
                        productId = "Blau_A_80",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "90A",
                        productId = "Blau_A_90",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "70B",
                        productId = "Blau_B_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "80B",
                        productId = "Blau_B_80",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "90B",
                        productId = "Blau_B_90",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "70C",
                        productId = "Blau_C_70",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "80C",
                        productId = "Blau_C_80",
                        hasThumb = false,
                    ),
                    createDimensionValue(
                        text = "90C",
                        productId = "Blau_C_90",
                        hasThumb = false,
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.fusedDimensions
        )
        // ensure we also keep individual dimensions
        assertNotEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs variants when attempting to fuse product with single dimension`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = "Size")

        val domainProduct = mapping.toProduct(
            createQueryProduct(
                listOf(ModelMapping.COLOR_VARIANT_LABEL),
                listOf(
                    VariantData(listOf("Gold"))
                )
            )
        )
        val expectedDimensions = listOf(
            Dimension(
                name = ModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "Gold",
                        productId = "Gold",
                        hasThumb = true,
                        availabilityMessage = null // color dimension values don't have a message
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.fusedDimensions
        )
        // in this case, nothing was fused and both dimensions should be identical
        assertEquals(
            domainProduct.fusedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `correctly constructs a full matrix of possible size variants from three dimensions`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Unterbrustweite")
        val colors = listOf("blau", "rot", "schwarz")
        val cups = listOf("Cup A", "Cup B", "Cup C")
        val sizes = listOf("70", "80", "90")
        val variantValues = buildList {
            for (color in colors) {
                for (cup in cups) {
                    for (size in sizes) {
                        add(
                            VariantData(
                                variantValues = listOf(color, cup, size),
                                stockStatus = if (size == "70" && cup == "Cup A" && color == "blau") {
                                    ModelMapping.STOCK_STATUS_DELIVERABLE_FEW_LEFT
                                } else {
                                    ModelMapping.STOCK_STATUS_DELIVERABLE
                                }
                            )
                        )
                    }
                }
            }
        }
        val testQueryProduct = createQueryProduct(variantLabels, variantValues)

        val domainProduct = mapping.toProduct(testQueryProduct)
        val expectedMatrix = SizeDimensionMatrix(
            parentDimensionName = "Cup",
            entries = listOf(
                SizeDimensionMatrix.Entry(
                    name = "A",
                    child = Dimension(
                        name = "Unterbrustweite",
                        type = DimensionType.DEFAULT,
                        values = listOf(
                            createDimensionValue(
                                text = "70",
                                productId = "Blau_A_70",
                                hasThumb = false,
                                availabilityState = Availability.State.LOW_STOCK,
                            ),
                            createDimensionValue(
                                text = "80",
                                productId = "Blau_A_80",
                                hasThumb = false,
                            ),
                            createDimensionValue(
                                text = "90",
                                productId = "Blau_A_90",
                                hasThumb = false,
                            )
                        )
                    )
                ),
                SizeDimensionMatrix.Entry(
                    name = "B",
                    child = Dimension(
                        name = "Unterbrustweite",
                        type = DimensionType.DEFAULT,
                        values = listOf(
                            createDimensionValue(
                                text = "70",
                                productId = "Blau_B_70",
                                hasThumb = false,
                            ),
                            createDimensionValue(
                                text = "80",
                                productId = "Blau_B_80",
                                hasThumb = false,
                            ),
                            createDimensionValue(
                                text = "90",
                                productId = "Blau_B_90",
                                hasThumb = false,
                            )
                        )
                    )
                ),
                SizeDimensionMatrix.Entry(
                    name = "C",
                    child = Dimension(
                        name = "Unterbrustweite",
                        type = DimensionType.DEFAULT,
                        values = listOf(
                            createDimensionValue(
                                text = "70",
                                productId = "Blau_C_70",
                                hasThumb = false,
                            ),
                            createDimensionValue(
                                text = "80",
                                productId = "Blau_C_80",
                                hasThumb = false,
                            ),
                            createDimensionValue(
                                text = "90",
                                productId = "Blau_C_90",
                                hasThumb = false,
                            )
                        )
                    )
                ),
            )
        )
        assertEquals(
            expectedMatrix,
            domainProduct.sizeMatrix,
        )
    }

    @Test
    fun `handles cases where there are more variant labels than variant values`() {
        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size1", "Size2"),
            variantData = listOf(
                VariantData(listOf("Blue", "38")),
                VariantData(listOf("Blue", "40")),
            ),
        )
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val domainProduct = mapping.toProduct(queryProduct)
        assertEquals(
            listOf(
                Dimension(
                    name = ModelMapping.COLOR_VARIANT_LABEL,
                    type = DimensionType.COLOR,
                    values = listOf(
                        createDimensionValue(
                            text = "Blue",
                            productId = "Blue_38",
                            hasThumb = true,
                        ).copy(
                            availability = Availability(
                                state = Availability.State.IN_STOCK,
                                quantity = 200,
                            )
                        ),
                    ),
                ),
                Dimension(
                    name = "Size1",
                    type = DimensionType.DEFAULT,
                    values = listOf(
                        createDimensionValue(
                            text = "38",
                            productId = "Blue_38",
                            hasThumb = false,
                        ),
                        createDimensionValue(
                            text = "40",
                            productId = "Blue_40",
                            hasThumb = false,
                        ),
                    ),
                ),
            ),
            domainProduct.individualDimensions
        )
    }
}
