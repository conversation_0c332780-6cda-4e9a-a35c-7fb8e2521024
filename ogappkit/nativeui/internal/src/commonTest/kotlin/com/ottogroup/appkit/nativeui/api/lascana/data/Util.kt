package com.ottogroup.appkit.nativeui.api.lascana.data

import com.apollographql.apollo.api.DefaultFakeResolver
import com.apollographql.apollo.api.FakeResolverContext
import com.ottogroup.appkit.nativeui.lascana.MasterProductVariantsQuery
import com.ottogroup.appkit.nativeui.lascana.MinimalProductQuery
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.lascana.schema.__Schema
import com.ottogroup.appkit.nativeui.lascana.type.QueryBuilder

internal fun buildProductDetailQueryData(
    block: QueryBuilder.() -> Unit = {}
): ProductDetailQuery.Data = ProductDetailQuery.Data(
    resolver = LascanaFakeResolver(),
    block = block,
)

internal fun buildMasterProductDetailQueryData(
    block: QueryBuilder.() -> Unit = {}
): MasterProductVariantsQuery.Data = MasterProductVariantsQuery.Data(
    resolver = LascanaFakeResolver(),
    block = block,
)

internal fun buildMinimalProductDetailQueryData(
    block: QueryBuilder.() -> Unit = {}
): MinimalProductQuery.Data = MinimalProductQuery.Data(
    resolver = LascanaFakeResolver(),
    block = block,
)

internal fun buildProductReviewsQueryData(
    block: QueryBuilder.() -> Unit = {}
): ProductReviewsQuery.Data = ProductReviewsQuery.Data(
    resolver = LascanaFakeResolver(),
    block = block,
)

internal class LascanaFakeResolver(
    val typeResolution: Map<String, Any?> = mapOf(
        "DateTime" to null,
    )
) : DefaultFakeResolver(__Schema.all) {

    private fun name(context: FakeResolverContext): String {
        return context.mergedField.type.rawType().name
    }

    override fun resolveLeaf(context: FakeResolverContext): Any {
        val name = name(context)
        return typeResolution[name] ?: super.resolveLeaf(context)
    }

    override fun resolveMaybeNull(context: FakeResolverContext): Boolean {
        val name = name(context)
        if (!typeResolution.containsKey(name)) return super.resolveMaybeNull(context)
        return typeResolution[name] == null
    }
}
