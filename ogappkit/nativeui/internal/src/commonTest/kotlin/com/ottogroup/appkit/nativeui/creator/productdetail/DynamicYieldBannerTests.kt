package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.Event
import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.test.testBannerContent
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.advanceUntilIdle

@OptIn(ExperimentalCoroutinesApi::class)
class DynamicYieldBannerTests {

    @Test
    fun `DynamicYieldBanner is returned for successful banner content from repository`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                // banner does not show up in loading state
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakeDynamicYieldRepository.banner.emit(
                    Result.Success(testBannerContent)
                )

                assertEquals(
                    successfulDetailScreenOf(
                        DynamicYieldBanner(
                            state = LoadingComponent.State.Done(
                                content = testBannerContent,
                            ),
                            config = config,
                        )
                    ),
                    awaitItem()
                )
                cancelAndIgnoreRemainingEvents()
            }
        }

    @Test
    fun `DynamicYieldBanner is omitted for unsuccessful banner content from repository`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                // banner does not show up in loading state
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakeDynamicYieldRepository.banner.emit(
                    Result.Failure(IllegalStateException("Something went wrong"))
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `DynamicYieldBanner is omitted for optimistic fake products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                // banner does not show up in loading state
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakeDynamicYieldRepository.banner.emit(
                    Result.Success(testBannerContent)
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `DynamicYieldBanner is debounced`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                // banner does not show up in loading state
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakeDynamicYieldRepository.banner.emit(
                    Result.Success(testBannerContent)
                )
                testObjects.updateProductFlow(testProduct.copy(id = "123123"))
                val newBannerContent = testBannerContent.copy(text = "new text")
                testObjects.fakeDynamicYieldRepository.banner.emit(
                    Result.Success(newBannerContent)
                )
                advanceUntilIdle()

                // expect only a single event with the latest banner content
                val events = cancelAndConsumeRemainingEvents()
                assertEquals(
                    listOf(
                        Event.Item(
                            successfulDetailScreenOf(
                                DynamicYieldBanner(
                                    state = LoadingComponent.State.Done(
                                        content = newBannerContent,
                                    ),
                                    config = config,
                                )
                            )
                        )
                    ),
                    events
                )
            }
        }
}
