package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.dynamicyield.DynamicYieldRepository
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.tracking.event.ECommerceItem
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow

internal class FakeDynamicYieldRepository : DynamicYieldRepository {
    val recommendations: MutableSharedFlow<Result<ProductRecommendations.Content>> = MutableSharedFlow()
    override fun getRecommendations(
        productSku: String,
        config: DynamicYieldRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return recommendations.asOperation()
    }

    val banner: MutableSharedFlow<Result<DynamicYieldBanner.Content>> = MutableSharedFlow(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)
    override fun getBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldBanner.Config
    ): Flow<Operation<DynamicYieldBanner.Content>> {
        return banner.asOperation()
    }
}
