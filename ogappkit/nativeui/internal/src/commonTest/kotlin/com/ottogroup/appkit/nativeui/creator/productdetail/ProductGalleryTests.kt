package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.ProductDetailScreenRequestContext
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductGallery
import com.ottogroup.appkit.test.THUMB_URL
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductGalleryTests {

    @Test
    fun `ProductGallery component is returned correctly`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(ProductGallery.Config)
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    ProductGallery(
                        state = LoadingComponent.State.Loading(Placeholders.productGallery)
                    )
                ),
                awaitItem()
            )
            assertEquals(
                successfulDetailScreenOf(
                    ProductGallery(
                        state = LoadingComponent.State.Done(
                            ProductGallery.Content(
                                images = listOf(Image(url = "https://img.url/product", thumbnailUrl = THUMB_URL)),
                                flags = emptyList(),
                                isWishlisted = false,
                                productIdForWishlisting = testProduct.id,
                            )
                        )
                    )
                ),
                awaitItem()
            )

            testObjects.fakeWishlistRepository.addProductToWishlist(testProduct.id)
            assertEquals(
                successfulDetailScreenOf(
                    ProductGallery(
                        state = LoadingComponent.State.Done(
                            ProductGallery.Content(
                                images = listOf(Image(url = "https://img.url/product", thumbnailUrl = THUMB_URL)),
                                flags = emptyList(),
                                isWishlisted = true,
                                productIdForWishlisting = testProduct.id,
                            )
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductGallery component set to loading state during color change`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(ProductGallery.Config)
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(
                        ProductGallery(
                            state = LoadingComponent.State.Done(
                                ProductGallery.Content(
                                    images = listOf(Image(url = "https://img.url/product", thumbnailUrl = THUMB_URL)),
                                    flags = emptyList(),
                                    isWishlisted = false,
                                    productIdForWishlisting = testProduct.id,
                                )
                            )
                        )
                    ),
                    expectMostRecentItem()
                )

                testObjects.updateProductFlow(
                    product = testProduct.copy(
                        isOptimisticFake = true
                    ),
                    context = ProductDetailScreenRequestContext.FromColorSelection,
                )
                assertEquals(
                    successfulDetailScreenOf(
                        ProductGallery(
                            state = LoadingComponent.State.Loading(Placeholders.productGallery)
                        )
                    ),
                    awaitItem()
                )
            }
        }
}
