package com.ottogroup.appkit.nativeui.api

import app.cash.turbine.test
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.ProductDetailScreenRequestContext
import com.ottogroup.appkit.nativeui.api.NativeApi.CachePolicy
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.test.FakeNativeApiProvider
import com.ottogroup.appkit.test.testProduct
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class ProductScreensRepositoryTest {

    private val nativeApiProvider = FakeNativeApiProvider()
    private val repository =
        ProductScreensRepository(ProductLoaderImpl(nativeApiProvider, LocalRecentlyViewedRepository()))

    private val testProduct1 = testProduct.copy(id = "1", title = "1")
    private val testProduct2 = testProduct.copy(id = "2", title = "2")

    @BeforeTest
    fun setup() {
        nativeApiProvider.products = mapOf(
            "1" to testProduct1,
            "2" to testProduct2,
        )
    }

    @Test
    fun `screen state is updated in place`() = runTest {
        val state = repository.loadNewProduct(
            id = "1",
            secondaryId = null,
            includeShopTheLook = true,
        )
        state.flow.test {
            // screen is loading, then displaying the first requested product
            assertEquals<Contextual<Operation<Product>>>(
                Contextual(
                    context = FromNothing,
                    value = Operation.InProgress,
                ),
                awaitItem()
            )
            assertEquals<Contextual<Operation<Product>>>(
                Contextual(
                    context = FromNothing,
                    value = Operation.Complete(
                        Result.Success(testProduct1),
                    ),
                ),
                awaitItem()
            )
            // assert we have only done a single request for initial loading
            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheAndNetwork),
            )

            // after update, screen is loading, then displaying a replica of the first product from cache, then the real second product
            repository.updateScreen(state.screenId, "2", ProductDetailScreenRequestContext.FromVariantSelection)
            assertEquals<Contextual<Operation<Product>>>(
                Contextual(
                    context = ProductDetailScreenRequestContext.FromVariantSelection,
                    value = Operation.InProgress,
                ),
                awaitItem()
            )
            assertEquals<Contextual<Operation<Product>>>(
                Contextual(
                    context = ProductDetailScreenRequestContext.FromVariantSelection,
                    value = Operation.Complete(
                        Result.Success(testProduct1.copy(id = "2", isOptimisticFake = true)),
                    ),
                ),
                awaitItem()
            )
            assertEquals<Contextual<Operation<Product>>>(
                Contextual(
                    context = ProductDetailScreenRequestContext.FromVariantSelection,
                    value = Operation.Complete(
                        Result.Success(testProduct2),
                    ),
                ),
                awaitItem()
            )
            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheAndNetwork),
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheOnly),
                NativeApi::getProduct.name to listOf("2", CachePolicy.CacheAndNetwork),
            )

            advanceUntilIdle()
            expectNoEvents()
        }

        // stopping screen flow observation cleans up internally and updates are no longer applied
        state.flow.test {
            advanceUntilIdle()
            expectMostRecentItem()

            repository.updateScreen(state.screenId, "1", ProductDetailScreenRequestContext.FromColorSelection)
            expectNoEvents()
        }
    }
}
