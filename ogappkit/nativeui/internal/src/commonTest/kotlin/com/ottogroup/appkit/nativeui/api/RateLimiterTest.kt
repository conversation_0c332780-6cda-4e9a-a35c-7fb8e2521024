package com.ottogroup.appkit.nativeui.api

import co.touchlab.stately.concurrency.AtomicInt
import com.ottogroup.appkit.test.TestClock
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalCoroutinesApi::class)
class RateLimiterTest {

    private val testClock = TestClock()
    private val limiter = RateLimiter<String>(clock = testClock)
    private val executions = AtomicInt(0)

    @Test
    fun `limit limits sequential execution within the specified duration`() = runTest {
        limiter.limit("MyOperation") {
            executions.incrementAndGet()
        }

        // second call, well within the limit duration
        limiter.limit("MyOperation") {
            executions.incrementAndGet()
        }

        assertEquals(1, executions.get())
    }

    @Test
    fun `limit limits concurrent execution within the specified duration`() {
        runBlocking {
            repeat(10) {
                launch {
                    limiter.limit("MyOperation") {
                        executions.incrementAndGet()
                    }
                }
            }
        }

        assertEquals(1, executions.get())
    }

    @Test
    fun `limit allows execution after the specified duration`() = runTest {
        limiter.limit("MyOperation") {
            executions.incrementAndGet()
        }

        testClock.advance(1.seconds.inWholeMilliseconds)

        limiter.limit("MyOperation") {
            executions.incrementAndGet()
        }

        assertEquals(2, executions.get())
    }

    @Test
    fun `limit allows immediate execution with different key`() = runTest {
        limiter.limit("MyOperation") {
            executions.incrementAndGet()
        }

        limiter.limit("MyOtherOperation") {
            executions.incrementAndGet()
        }

        assertEquals(2, executions.get())
    }
}
