package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions.SortingOption
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant

class ProductReviewsRepositoryTests {

    private val productReviewsRepository = ProductReviewsRepository(reviewsPerPage = 4)

    @BeforeTest
    fun setUp() = runBlocking {
        val dummyReviews = listOf(
            Review(
                text = "Awesome product",
                rating = 5,
                reviewerName = "Lucy",
                dateTime = Instant.fromEpochSeconds(1732273804),
            ),
            Review(
                text = "Good product",
                rating = 4,
                reviewerName = "John",
                dateTime = Instant.fromEpochSeconds(1732192729),
            ),
            Review(
                text = "Awesome product",
                rating = 5,
                reviewerName = "Valerie",
                dateTime = Instant.fromEpochSeconds(1732106329),
            ),
            Review(
                text = "Bad product",
                rating = 1,
                reviewerName = "Anna",
                dateTime = Instant.fromEpochSeconds(1732024073),
            )
        )
        productReviewsRepository.setProductReviews(dummyReviews)
    }

    @Test
    fun `setProductReviews stores the provided reviews`() = runTest {
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `filterProductReviews filters the reviews with the provided value`() = runTest {
        productReviewsRepository.filterProductReviews(5)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `filterProductReviews filters are removed if the filterRating is null`() = runTest {
        productReviewsRepository.filterProductReviews(null)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `reviews can be sorted by most recent`() = runTest {
        productReviewsRepository.sortProductReviews(SortingOption.RECENT)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `reviews can be sorted by oldest first`() = runTest {
        productReviewsRepository.sortProductReviews(SortingOption.OLDEST)
        assertEquals(
            listOf(
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `reviews can be sorted by highest rating`() = runTest {
        productReviewsRepository.sortProductReviews(SortingOption.HIGHEST)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `reviews can be sorted by lowest rating`() = runTest {
        productReviewsRepository.sortProductReviews(SortingOption.LOWEST)
        assertEquals(
            listOf(
                Review(
                    text = "Bad product",
                    rating = 1,
                    reviewerName = "Anna",
                    dateTime = Instant.fromEpochSeconds(1732024073),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `filtering a sorted list keeps the selected sorting`() = runTest {
        productReviewsRepository.sortProductReviews(SortingOption.OLDEST)
        productReviewsRepository.filterProductReviews(5)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Valerie",
                    dateTime = Instant.fromEpochSeconds(1732106329),
                ),
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                )
            ),
            productReviewsRepository.displayedProductReviews.value
        )
    }

    @Test
    fun `reviewsPerPage reviews are returned`() = runTest {
        val repository = ProductReviewsRepository(reviewsPerPage = 1)
        val dummyReviews = listOf(
            Review(
                text = "Awesome product",
                rating = 5,
                reviewerName = "Lucy",
                dateTime = Instant.fromEpochSeconds(1732273804),
            ),
            Review(
                text = "Good product",
                rating = 4,
                reviewerName = "John",
                dateTime = Instant.fromEpochSeconds(1732192729),
            )
        )
        repository.setProductReviews(dummyReviews)
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                )
            ),
            repository.displayedProductReviews.value
        )
    }

    @Test
    fun `showMoreReviews loads the next page of reviews`() = runTest {
        val repository = ProductReviewsRepository(reviewsPerPage = 1)
        val dummyReviews = listOf(
            Review(
                text = "Awesome product",
                rating = 5,
                reviewerName = "Lucy",
                dateTime = Instant.fromEpochSeconds(1732273804),
            ),
            Review(
                text = "Good product",
                rating = 4,
                reviewerName = "John",
                dateTime = Instant.fromEpochSeconds(1732192729),
            )
        )
        repository.setProductReviews(dummyReviews)
        repository.showMoreReviews()
        assertEquals(
            listOf(
                Review(
                    text = "Awesome product",
                    rating = 5,
                    reviewerName = "Lucy",
                    dateTime = Instant.fromEpochSeconds(1732273804),
                ),
                Review(
                    text = "Good product",
                    rating = 4,
                    reviewerName = "John",
                    dateTime = Instant.fromEpochSeconds(1732192729),
                )
            ),
            repository.displayedProductReviews.value
        )
    }
}
