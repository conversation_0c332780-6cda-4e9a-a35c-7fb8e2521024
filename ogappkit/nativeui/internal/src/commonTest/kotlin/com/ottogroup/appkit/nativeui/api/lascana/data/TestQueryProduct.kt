package com.ottogroup.appkit.nativeui.api.lascana.data

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_DELIVERABLE
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_DELIVERABLE_FEW_LEFT
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_NOT_IN_STOCK
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.type.buildAttribute
import com.ottogroup.appkit.nativeui.lascana.type.buildCurrency
import com.ottogroup.appkit.nativeui.lascana.type.buildDistributingCompanies
import com.ottogroup.appkit.nativeui.lascana.type.buildDistributingCompany
import com.ottogroup.appkit.nativeui.lascana.type.buildFlyout
import com.ottogroup.appkit.nativeui.lascana.type.buildPrice
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildProductAttribute
import com.ottogroup.appkit.nativeui.lascana.type.buildProductImageGallery
import com.ottogroup.appkit.nativeui.lascana.type.buildProductStock
import com.ottogroup.appkit.test.POSITIONED_URL
import com.ottogroup.appkit.test.THUMB_URL

internal val complexTestQueryProduct: ProductDetailQuery.Product
    get() {
        val variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Unterbrustweite")
        val colors = listOf("blau", "rot", "schwarz")
        val cups = listOf("Cup A", "Cup B", "Cup C")
        val sizes = listOf("70", "80", "90")
        val variantValues = buildList {
            for (color in colors) {
                for (cup in cups) {
                    for (size in sizes) {
                        add(VariantData(variantValues = listOf(color, cup, size)))
                    }
                }
            }
        }

        return createQueryProduct(variantLabels, variantValues)
    }

internal fun createQueryProduct(
    variantLabels: List<String>,
    variantData: List<VariantData>,
): ProductDetailQuery.Product {
    fun List<String>.toId() = joinToString("_") { it.replace("Cup ", "").replaceFirstChar { it.titlecase() } }

    val thisVariantData = variantData.firstOrNull()
    val data = buildProductDetailQueryData {
        product = buildProduct {
            id = thisVariantData?.variantValues?.toId() ?: "parent"
            this.variantLabels = variantLabels
            this.variantValues = thisVariantData?.variantValues ?: emptyList()
            this.stock = buildProductStock {
                stockStatus = thisVariantData?.stockStatus ?: -99
                stock = when (thisVariantData?.stockStatus) {
                    STOCK_STATUS_DELIVERABLE -> 100
                    STOCK_STATUS_DELIVERABLE_FEW_LEFT -> 5
                    STOCK_STATUS_NOT_IN_STOCK -> 0
                    else -> -1
                }.toDouble()
            }
            thisVariantData?.deliveryInformation?.let { deliveryInformation = it }
            variants = variantData.map { data ->
                buildProduct {
                    id = data.variantValues.toId()
                    variantValues = data.variantValues
                    stock = buildProductStock {
                        stockStatus = data.stockStatus
                        stock = when (data.stockStatus) {
                            STOCK_STATUS_DELIVERABLE -> 100
                            STOCK_STATUS_DELIVERABLE_FEW_LEFT -> 5
                            STOCK_STATUS_NOT_IN_STOCK -> 0
                            else -> -1
                        }.toDouble()
                    }
                    data.deliveryInformation?.let { deliveryInformation = it }
                    imageGallery = buildProductImageGallery {
                        thumb = THUMB_URL
                    }
                    positionedPicture = POSITIONED_URL.takeIf { data.hasPositionedImage }
                    price = buildPrice {
                        currency = buildCurrency {
                            name = "EUR"
                        }
                        price = 0.0
                    }
                    listPrice = null
                    flyouts = buildFlyout {
                        reminder = if (data.stockStatus == STOCK_STATUS_NOT_IN_STOCK) {
                            "/notify-me"
                        } else null
                    }
                }
            }.shuffled() // Order of variants from the API should not matter. They are sorted by our model mapping.
            shortDescription = "This product is <b>awesome</b> &amp; great!<br /> " +
                "Winner of an <a href=\"https://www.award.com\">award</a>.<br> <br>" +
                "<a href=\"https://example.com/files/manual.pdf\">Manual</a>" +
                "<a HREF=\"https://example.com/files/LEGAL.PDF\">Legal doc</a>" +
                "<img src=\"logo.gif\">"
            this["detailPageAttributes"] = listOf(
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Look"
                        groupName = "Style"
                    }
                    value = "single-color"
                },
                // Note the identical title AND groupName as above. Both values to be displayed in a single table cell.
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Look"
                        groupName = "Style"
                    }
                    value = "stitched"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Color"
                        groupName = "Color"
                    }
                    value = "rainbow"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Style"
                        groupName = "Style"
                    }
                    value = "casual &amp; relaxed"
                },
            )
            this["nonDetailPageAttributes"] = listOf(
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Selling Point 3"
                        groupName = "Selling Points"
                    }
                    value = "Cool zipper"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Selling Point 1"
                        groupName = "Selling Points"
                    }
                    value = "Stitched logo"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Technical Artnum"
                        groupName = "internal"
                    }
                    value = "123456_xx"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Nachhaltigkeitssiegel"
                        groupName = "Nachhaltigkeit"
                    }
                    value = "Recyceltes Material"
                },
                buildProductAttribute {
                    attribute = buildAttribute {
                        title = "Selling Point 2"
                        groupName = "Selling Points"
                    }
                    value = "Machine washable"
                },
            )
            distributingCompany = buildDistributingCompanies {
                companies = listOf(
                    buildDistributingCompany {
                        companyName = "ACME Corp. &amp; Sons"
                        street = "F&auml;kestreet 123"
                        postalCode = "12345"
                        city = "To&omega;nsville"
                        countryCode = "XY"
                        phone = "+1234567890"
                        email = "<EMAIL>"
                        url = "https://www.example.com"
                    },
                    buildDistributingCompany {
                        companyName = "Second Inc."
                        street = "Streetlane 1"
                        postalCode = "54321"
                        city = "Secondtown"
                        countryCode = "XY"
                        phone = "+1234567890"
                        email = "<EMAIL>"
                        url = "https://www.example.com"
                    }
                )
            }
            flyouts = buildFlyout {
                sizeGuide = null
                reminder = if (thisVariantData?.stockStatus == STOCK_STATUS_NOT_IN_STOCK) {
                    "/notify-me"
                } else null
            }
        }
    }

    return data.product
}

internal data class VariantData(
    val variantValues: List<String>,
    val stockStatus: Int = 0,
    val deliveryInformation: String? = null,
    val hasPositionedImage: Boolean = true,
)
