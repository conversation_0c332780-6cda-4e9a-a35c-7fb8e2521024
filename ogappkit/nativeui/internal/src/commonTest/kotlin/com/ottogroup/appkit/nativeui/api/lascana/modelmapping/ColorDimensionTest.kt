package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.api.lascana.data.createQueryProduct
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.singleColorDimension
import com.ottogroup.appkit.test.THUMB_URL
import com.ottogroup.appkit.test.createDimensionValue
import kotlin.test.Test
import kotlin.test.assertEquals

class ColorDimensionTest {

    @Test
    fun `color dimension value's availability matches best case of all variants of that color`() {
        /* Red: one size is deliverable -> color is available
         * Green: no size is in stock -> color is not available
         * Blue: one size has few left, the other is not in stock -> color is "low stock"
         */
        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
            variantData = listOf(
                VariantData(
                    variantValues = listOf("red", "S"),
                    stockStatus = ModelMapping.STOCK_STATUS_NOT_IN_STOCK,
                ),
                VariantData(
                    variantValues = listOf("red", "M"),
                    stockStatus = ModelMapping.STOCK_STATUS_DELIVERABLE,
                ),
                VariantData(
                    variantValues = listOf("green", "M"),
                    stockStatus = ModelMapping.STOCK_STATUS_NOT_IN_STOCK,
                ),
                VariantData(
                    variantValues = listOf("green", "L"),
                    stockStatus = ModelMapping.STOCK_STATUS_NOT_IN_STOCK,
                ),
                VariantData(
                    variantValues = listOf("blue", "S"),
                    stockStatus = ModelMapping.STOCK_STATUS_NOT_IN_STOCK,
                ),
                VariantData(
                    variantValues = listOf("blue", "M"),
                    stockStatus = ModelMapping.STOCK_STATUS_DELIVERABLE_FEW_LEFT,
                ),
            )
        )

        val modelMapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val domainProduct = modelMapping.toProduct(queryProduct)
        val colorDimension = domainProduct.singleColorDimension()
        val expectedColorDimension = Dimension(
            name = ModelMapping.COLOR_VARIANT_LABEL,
            type = Dimension.DimensionType.COLOR,
            values = listOf(
                createDimensionValue(
                    text = "Blue",
                    productId = "Blue_S",
                    hasThumb = true,
                    availabilityState = Availability.State.LOW_STOCK,
                    availabilityMessage = null,
                ),
                createDimensionValue(
                    text = "Green",
                    productId = "Green_M",
                    hasThumb = true,
                    availabilityState = Availability.State.TEMPORARILY_OUT_OF_STOCK,
                    availabilityMessage = null,
                ),
                createDimensionValue(
                    text = "Red",
                    productId = "Red_S",
                    hasThumb = true,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                ),
            )
        )

        assertEquals(
            expectedColorDimension,
            colorDimension
        )
    }

    @Test
    fun `color dimension image falls back to first regular image if positioned image is null`() {
        val queryProduct = createQueryProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(
                VariantData(
                    variantValues = listOf("red"),
                    stockStatus = ModelMapping.STOCK_STATUS_DELIVERABLE,
                ),
                VariantData(
                    variantValues = listOf("blue"),
                    stockStatus = ModelMapping.STOCK_STATUS_DELIVERABLE,
                    hasPositionedImage = false,
                ),
            )
        )

        val modelMapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val domainProduct = modelMapping.toProduct(queryProduct)
        val colorDimension = domainProduct.singleColorDimension()
        val expectedColorDimension = Dimension(
            name = ModelMapping.COLOR_VARIANT_LABEL,
            type = Dimension.DimensionType.COLOR,
            values = listOf(
                createDimensionValue(
                    text = "Blue",
                    productId = "Blue",
                    hasThumb = true,
                    thumbnailUrl = THUMB_URL,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                ),
                createDimensionValue(
                    text = "Red",
                    productId = "Red",
                    hasThumb = true,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                ),
            )
        )

        assertEquals(
            expectedColorDimension,
            colorDimension
        )
    }
}
