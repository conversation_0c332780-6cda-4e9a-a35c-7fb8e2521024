package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.http.CookiesBridge

internal class TestCookiesBridge : CookiesBridge {
    override suspend fun getCookies(url: String): Map<String, String> = mapOf(
        "cookie" to "value"
    )

    val setCookies = mutableListOf<Pair<String, String>>()
    override suspend fun setCookie(url: String, cookie: String) {
        setCookies += url to cookie
    }
}
