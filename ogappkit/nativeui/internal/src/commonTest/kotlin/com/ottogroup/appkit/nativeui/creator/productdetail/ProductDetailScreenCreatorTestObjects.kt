package com.ottogroup.appkit.nativeui.creator.productdetail

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.ProductDetailScreenRequestContext
import com.ottogroup.appkit.nativeui.api.Contextual
import com.ottogroup.appkit.nativeui.api.FromNothing
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailScreen
import com.ottogroup.appkit.test.FakeDynamicYieldRepository
import com.ottogroup.appkit.test.FakeNativeTrackingProvider
import com.ottogroup.appkit.test.FakeRecommendationsRepository
import com.ottogroup.appkit.test.FakeWishlistRepository
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest

internal class ProductDetailScreenCreatorTestObjects {
    val fakeRecommendationsRepository = FakeRecommendationsRepository()
    val fakeDynamicYieldRepository = FakeDynamicYieldRepository()
    val fakeTracking = FakeNativeTrackingProvider()
    val fakeWishlistRepository = FakeWishlistRepository()

    val creator = ProductDetailScreenCreator(
        fakeWishlistRepository,
        fakeRecommendationsRepository,
        fakeDynamicYieldRepository,
        fakeTracking,
    )

    val screenId = SCREEN_ID

    private val productResultFlow = MutableSharedFlow<Result<Product>>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)
    private var context: ProductDetailScreenRequestContext = FromNothing
    fun productFlow(product: Product): Flow<Contextual<Operation<Product>>> {
        productResultFlow.tryEmit(Result.Success(product))
        return productResultFlow.asOperation().map { Contextual(context, it) }
    }
    fun updateProductFlow(
        product: Product,
        context: ProductDetailScreenRequestContext = FromNothing
    ) {
        this.context = context
        productResultFlow.tryEmit(Result.Success(product))
    }
}

internal fun successfulDetailScreenOf(vararg components: ProductDetailComponent<*>): Result<ProductDetailScreen> {
    return Result.Success(
        ProductDetailScreen(
            screenId = SCREEN_ID,
            components = components.asList(),
        )
    )
}

internal fun runProductDetailScreenCreatorTest(
    testBody: suspend TestScope.(ProductDetailScreenCreatorTestObjects) -> Unit
) {
    runTest { testBody(ProductDetailScreenCreatorTestObjects()) }
}

private const val SCREEN_ID = "1234"
