package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.test.TestCookiesBridge
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json

class LascanaWishlistApiTest {

    private val cookiesBridge = TestCookiesBridge()
    private val configProvider = OGNativeConfigProvider().apply {
        update(
            OGNativeConfig(
                restBackend = OGNativeConfig.Backend(
                    url = "https://example.com/api",
                    headers = mapOf(
                        "Authorization" to "Basic dXNlcjpwYXNz"
                    ),
                ),
                cookiesBridge = cookiesBridge,
            )
        )
    }

    private fun createApi(mockEngine: MockEngine): LascanaWishlistApi {
        return LascanaWishlistApiImpl(
            ogNativeConfigProvider = configProvider,
            httpClientProvider = object : LascanaHttpClientProvider {
                override val httpClient = lascanaHttpClient(
                    plugins = listOf(cookiesPlugin(configProvider)),
                    customEngine = mockEngine
                )
            }
        )
    }

    @Test
    fun `getWishlist sends well-formed request`() = testApi(
        method = HttpMethod.Get,
        url = configProvider.configState.value.restBackend!!.url + "/graphql/getWishlist/",
        headers = configProvider.configState.value.restBackend!!.headers,
        payload = "",
    ) { api ->
        assertEquals(
            Result.Success(Wishlist(listOf(Wishlist.Item(id = "**********")))),
            api.getWishlist()
        )
    }

    @Test
    fun `addToWishlist sends well-formed request`() = testApi(
        method = HttpMethod.Post,
        url = configProvider.configState.value.restBackend!!.url + "/graphql/addToWishlist/",
        headers = configProvider.configState.value.restBackend!!.headers,
        payload = goldenPayload("12345678"),
    ) { api ->
        assertEquals(
            Result.Success(Wishlist(listOf(Wishlist.Item(id = "**********")))),
            api.addToWishlist("12345678")
        )
    }

    @Test
    fun `removeFromWishlist sends well-formed request`() = testApi(
        method = HttpMethod.Delete,
        url = configProvider.configState.value.restBackend!!.url + "/graphql/addToWishlist/",
        headers = configProvider.configState.value.restBackend!!.headers,
        payload = goldenPayload("12345678"),
    ) { api ->
        assertEquals(
            Result.Success(Wishlist(listOf(Wishlist.Item(id = "**********")))),
            api.removeFromWishlist("12345678")
        )
    }

    @Test
    fun `alternate response format is handled correctly`() = testApi(
        method = HttpMethod.Get,
        url = configProvider.configState.value.restBackend!!.url + "/graphql/getWishlist/",
        headers = configProvider.configState.value.restBackend!!.headers,
        payload = "",
        response = alternateResponse,
    ) { api ->
        assertEquals(
            Result.Success(Wishlist(listOf(Wishlist.Item(id = "**********")))),
            api.getWishlist()
        )
    }

    private fun testApi(
        method: HttpMethod,
        url: String,
        headers: Map<String, String>,
        payload: String,
        response: String = defaultResponse,
        block: suspend (LascanaWishlistApi) -> Unit,
    ) = runTest {
        var error: Throwable? = null
        val api = createApi(assertingMockEngine(method, url, headers, payload, response) { error = it })
        block(api)

        assertEquals(
            listOf(
                url to "cookie1=value1",
                url to "cookie2=value2; expires=Fri, 14-Mar-2025 14:42:36 GMT",
            ),
            cookiesBridge.setCookies
        )

        error?.let { throw it }
    }
}

internal fun assertingMockEngine(
    method: HttpMethod,
    url: String,
    headers: Map<String, String>,
    payload: String,
    response: String,
    onError: (Throwable) -> Unit,
) = MockEngine { request ->
    try {
        assertEquals(
            method,
            request.method
        )

        assertEquals(
            url,
            request.url.toString(),
        )

        headers.forEach { (k, v) ->
            assertTrue("Expected header $k:$v to be present") {
                request.headers.contains(k, v)
            }
        }

        assertEquals(
            "cookie=value",
            request.headers[HttpHeaders.Cookie]
        )
        // LAS API firewall disallows Accept-Charset header
        assertNull(request.headers[HttpHeaders.AcceptCharset])

        if (payload.isNotBlank()) {
            assertEquals(
                Json.parseToJsonElement(payload),
                Json.parseToJsonElement(request.body.toByteArray().decodeToString())
            )
        }
    } catch (e: Throwable) {
        // need to propagate the error onto the main thread
        onError(e)
    }
    respond(
        content = ByteReadChannel(response),
        headers = headersOf(
            HttpHeaders.ContentType to listOf("application/json"),
            HttpHeaders.SetCookie to listOf("cookie1=value1", "cookie2=value2; expires=Fri, 14-Mar-2025 14:42:36 GMT"),
        )
    )
}

private val defaultResponse = """
    {
      "success": true,
      "itemCount": 1,
      "wishlistItems": [
        {
          "articleId": "**********",
          "parentId": "**********",
          "articleName": "Leggings"
        }
      ]
    }
""".trimIndent()

private val alternateResponse = """
    {
      "success": true,
      "itemCount": 1,
      "wishlistItems": [
        {
          "articleId": "**********",
          "parentId": "",
          "articleName": "Leggings"
        }
      ]
    }
""".trimIndent()

private fun goldenPayload(id: String) = "{\"articleId\":\"$id\"}"
