package com.ottogroup.appkit.test

import com.ottogroup.appkit.tracking.ContextParameter
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.OGTrackingConfig
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceConfig
import com.ottogroup.appkit.tracking.services.adjust.AdjustAnalytics
import com.ottogroup.appkit.tracking.services.airship.AirshipAnalytics
import com.ottogroup.appkit.tracking.services.firebase.FirebaseAnalytics
import com.ottogroup.appkit.tracking.services.snowplow.SnowplowAnalytics
import com.ottogroup.appkit.tracking.userproperty.UserProperty

internal class FakeOGTracking : OGTracking, TestCallVerifiable by TestCallVerifier() {
    override fun configure(config: OGTrackingConfig) = Unit

    override fun onDidLoad(url: String) = Unit

    override fun track(event: OGEvent) {
        recordCall(::track.name, event)
    }

    override fun setConsent(
        serviceId: OGTrackingServiceId,
        consent: Boolean
    ) = Unit

    override fun setGlobalConsent(globalConsent: Boolean) = Unit

    override fun configureAirshipAnalytics(airshipAnalytics: AirshipAnalytics) = Unit

    override fun configureAdjustAnalytics(adjustAnalytics: AdjustAnalytics) = Unit

    override fun configureFirebaseAnalytics(firebaseAnalytics: FirebaseAnalytics) = Unit

    override fun configureSnowplow(snowplowAnalytics: SnowplowAnalytics) = Unit

    override fun onUpdateConfig(
        id: OGTrackingServiceId,
        config: ServiceConfig
    ) = Unit

    override fun updateGlobalContext(
        id: OGTrackingServiceId,
        contextParameters: ContextParameter
    ) = Unit

    override fun setUserProperty(userProperty: UserProperty) = Unit
}
