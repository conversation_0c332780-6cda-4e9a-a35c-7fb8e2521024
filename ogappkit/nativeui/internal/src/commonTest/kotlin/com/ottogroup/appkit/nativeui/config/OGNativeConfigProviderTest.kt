package com.ottogroup.appkit.nativeui.config

import app.cash.turbine.test
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import kotlin.test.Test
import kotlin.test.assertEquals

@OptIn(ExperimentalCoroutinesApi::class)
class OGNativeConfigProviderTest {

    @Test
    fun `provides current config state`() = runTest {
        val provider = OGNativeConfigProvider()

        provider.configState.test {
            assertEquals(
                OGNativeConfig(),
                awaitItem()
            )

            val newConfig = OGNativeConfig(
                graphQLBackend = OGNativeConfig.Backend("https://www.lascana.de/graphql"),
                tenant = OGNativeConfig.NativeApiTenant.LASCANA,
            )
            provider.update(newConfig)

            assertEquals(
                newConfig,
                awaitItem()
            )
        }
    }
}
