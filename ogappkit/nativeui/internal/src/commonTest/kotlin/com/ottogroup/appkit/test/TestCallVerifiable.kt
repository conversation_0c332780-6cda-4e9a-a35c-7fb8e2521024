package com.ottogroup.appkit.test

import com.ottogroup.appkit.test.TestCallVerifiable.Call
import kotlin.test.assertContains
import kotlin.test.assertEquals

/**
 * Allows verifying that certain methods were called with expected
 * parameters.
 *
 * Intended usage: implement this interface in your test object by
 * delegating to an instance of [TestCallVerifier]. Then, in your methods,
 * call [recordCall] with the parameters that are of interest for
 * verification. In the unit test, these calls can be checked by using
 * [verify].
 */
interface TestCallVerifiable {
    val calls: List<Call>

    fun recordCall(name: String, vararg args: Any?)
    fun verify(expectedCalls: List<Call>, exactMatch: Boolean = true)
    fun verify(vararg expectedCalls: Pair<String, List<Any?>>) = verify(expectedCalls.map { Call(it.first, it.second) })

    fun verify(name: String, vararg callArgs: List<Any?>, exactMatch: Boolean = false) =
        verify(callArgs.map { Call(name, it) }, exactMatch = exactMatch)

    fun verifyNone(name: String)

    data class Call(val name: String, val args: List<Any?>) {
        constructor(name: String, vararg args: Any?) : this(name, args.toList())
    }
}

class TestCallVerifier : TestCallVerifiable {
    override val calls = mutableListOf<Call>()

    override fun recordCall(name: String, vararg args: Any?) {
        calls.add(Call(name, args.toList()))
    }

    override fun verify(expectedCalls: List<Call>, exactMatch: Boolean) {
        when {
            expectedCalls.isEmpty() -> {
                assertEquals(
                    expected = emptyList<Call>(),
                    actual = this.calls,
                    message = "Expected no calls, but found: $calls"
                )
            }

            exactMatch -> {
                assertEquals(
                    expected = expectedCalls,
                    actual = this.calls,
                    message = "Expected calls do not match actual calls. Expected: $expectedCalls, Actual: $calls"
                )
            }

            else -> {
                expectedCalls.forEach { expectedCall ->
                    assertContains(
                        iterable = this.calls,
                        element = expectedCall,
                        message = "Expected call ${expectedCall.name} with args ${expectedCall.args} not found in actual calls"
                    )
                }
            }
        }
    }

    override fun verifyNone(name: String) {
        val callsWithName = calls.filter { it.name == name }
        assertEquals(
            expected = emptyList(),
            actual = callsWithName,
            message = "Expected no calls to $name, but found: $callsWithName"
        )
    }
}
