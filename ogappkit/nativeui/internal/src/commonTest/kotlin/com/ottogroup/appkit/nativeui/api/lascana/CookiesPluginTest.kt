package com.ottogroup.appkit.nativeui.api.lascana

import kotlin.test.Test
import kotlin.test.assertEquals

class CookiesPluginTest {

    @Test
    fun `splitCookieString splits comma-delimited Set-Cookie strings correctly`() {
        val testCases = mapOf(
            "__Secure-lascanaWishlistInformation=1440512043_1440510471_1_2025-02-12%2016%3A21%3A15; expires=Fri, 14-Mar-2025 15:21:15 GMT; Max-Age=2592000; path=/; secure" to listOf("__Secure-lascanaWishlistInformation=1440512043_1440510471_1_2025-02-12%2016%3A21%3A15; expires=Fri, 14-Mar-2025 15:21:15 GMT; Max-Age=2592000; path=/; secure"),
            "language=0; path=/; secure; HttpOnly" to listOf("language=0; path=/; secure; HttpOnly"),
            "cookie1=value1; expires=Mon, 01-Jan-2024 00:00:00 GMT, cookie2=value2,cookie3=value3; expires=Tue, 02-Jan-2024 00:00:00 GMT" to listOf(
                "cookie1=value1; expires=Mon, 01-Jan-2024 00:00:00 GMT",
                "cookie2=value2",
                "cookie3=value3; expires=Tue, 02-Jan-2024 00:00:00 GMT"
            )
        )

        for (case in testCases) {
            assertEquals(
                case.value,
                splitCookieString(case.key)
            )
        }
    }
}
