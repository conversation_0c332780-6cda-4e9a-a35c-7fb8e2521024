package com.ottogroup.appkit.nativeui.model.ui

import kotlin.test.Test
import kotlin.test.assertEquals

class ComponentConfigTest {

    @Test
    fun `polymorphic deserialization for product detail works as expected`() {
        val json = """
        {
            "components": [
                {
                    "name": "ProductTitle"
                },
                {
                    "name": "ProductGallery"
                },
                {
                    "name": "invalid element",
                    "text": "this will simply be skipped"
                }
            ]
        }
        """.trimIndent()
        val configs: ComponentConfigs<ProductDetailComponentConfig> = parseComponentConfigFromJson(json)

        val l = listOf(
            ProductTitle.Config,
            ProductGallery.Config,
        )

        assertEquals(
            ComponentConfigs<ProductDetailComponentConfig>(l),
            configs
        )
    }
}
