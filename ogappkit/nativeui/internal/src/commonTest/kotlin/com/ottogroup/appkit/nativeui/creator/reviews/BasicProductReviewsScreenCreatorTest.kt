package com.ottogroup.appkit.nativeui.creator.reviews

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.ProductReviewsRepository
import com.ottogroup.appkit.nativeui.creator.reviews.ProductReviewsScreenCreatorTestObjects.creator
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest

class BasicProductReviewsScreenCreatorTest {

    @Test
    fun `screen returns failure if product reviews fail to be loaded`() = runTest {
        val configs: ComponentConfigs<ProductReviewsComponentConfig> = ComponentConfigs(
            listOf(ReviewsSortingOptions.Config)
        )

        val error = Exception()

        creator.createScreen(
            MutableStateFlow(Result.Failure<ProductReviews>(error)).asOperation(),
            configs,
            ProductReviewsRepository(reviewsPerPage = 15),
            "12345"
        ).test {
            assertEquals(
                Result.Failure(error),
                awaitItem()
            )
        }
    }
}
