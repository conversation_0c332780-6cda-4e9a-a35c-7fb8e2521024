package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.buildMinimalProductDetailQueryData
import com.ottogroup.appkit.nativeui.lascana.MinimalProductQuery
import com.ottogroup.appkit.nativeui.lascana.type.buildCurrency
import com.ottogroup.appkit.nativeui.lascana.type.buildManufacturer
import com.ottogroup.appkit.nativeui.lascana.type.buildPrice
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildProductImage
import com.ottogroup.appkit.nativeui.lascana.type.buildProductImageGallery
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import kotlin.test.Test
import kotlin.test.assertEquals

class MinimalProductParsingTest {

    @Test
    fun `parses minimal product query data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val minimalProductQueryData = createMinimalProductQueryData(parentId = "0")

        val domainProduct = mapping.toProduct(minimalProductQueryData.product.minimalProduct)
        assertEquals(
            createExpectedMinimalProduct(parentId = "0"),
            domainProduct
        )
    }

    @Test
    fun `parses minimal product query data with empty parentId`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val minimalProductQueryData = createMinimalProductQueryData(parentId = "")

        val domainProduct = mapping.toProduct(minimalProductQueryData.product.minimalProduct)
        assertEquals(
            createExpectedMinimalProduct(parentId = null),
            domainProduct
        )
    }
}

private fun createMinimalProductQueryData(
    parentId: String
): MinimalProductQuery.Data = buildMinimalProductDetailQueryData {
    product = buildProduct {
        id = "1"
        sku = "1_XS"
        this.parentId = parentId
        title = "Product"
        manufacturer = buildManufacturer {
            title = "Manufacturer"
        }
        price = buildPrice {
            price = 10.0
            currency = buildCurrency { name = "EUR" }
        }
        listPrice = null
        imageGallery = buildProductImageGallery {
            images = listOf(
                buildProductImage {
                    image = "https://example.com/image.jpg"
                }
            )
        }
    }
}

private fun createExpectedMinimalProduct(parentId: String?): Product = Product(
    id = "1",
    parentId = parentId,
    title = "Product",
    shortTitle = "Product",
    webShopUrl = null,
    images = listOf(
        Image(
            url = "https://example.com/image.jpg",
            thumbnailUrl = "https://example.com/image.jpg",
        )
    ),
    flags = emptyList(),
    brand = Brand(name = "Manufacturer", description = null),
    price = Price(currency = "EUR", value = 1000),
    fusedDimensions = emptyList(),
    individualDimensions = emptyList(),
    sizeMatrix = null,
    availability = Availability(
        state = Availability.State.UNKNOWN,
        quantity = 0,
    ),
    information = Information(
        articleNumber = "",
        description = "",
        bulletPoints = emptyList(),
        attributesTable = Information.AttributesTable(emptyList()),
        distributingCompanies = emptyList(),
        documents = emptyList(),
        articleStandards = null
    ),
    reviews = Reviews(rating = null, reviews = emptyList(), writeReviewUrl = ""),
    sku = "1_XS",
    shopTheLook = null,
    moreFromTheSeries = null,
    breadcrumbs = emptyList(),
)
