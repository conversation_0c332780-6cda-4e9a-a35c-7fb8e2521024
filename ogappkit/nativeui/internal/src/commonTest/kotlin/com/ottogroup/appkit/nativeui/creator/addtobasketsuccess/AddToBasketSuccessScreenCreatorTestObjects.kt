package com.ottogroup.appkit.nativeui.creator.addtobasketsuccess

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.creator.AddToBasketSuccessScreenCreator
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponent
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessScreen
import com.ottogroup.appkit.test.FakeRecommendationsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow

internal object AddToBasketSuccessScreenCreatorTestObjects {
    internal val fakeRecommendationsRepository = FakeRecommendationsRepository()

    val creator = AddToBasketSuccessScreenCreator(
        fakeRecommendationsRepository,
    )

    fun productFlow(product: Product): Flow<Operation<Product>> =
        MutableStateFlow(Result.Success(product)).asOperation()
}

internal fun successfulBasketSuccessScreenOf(vararg components: AddToBasketSuccessComponent<*>): Result<AddToBasketSuccessScreen> {
    return Result.Success(AddToBasketSuccessScreen(components.asList()))
}
