package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductColor
import com.ottogroup.appkit.nativeui.model.ui.ProductColorDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.test.POSITIONED_URL
import com.ottogroup.appkit.test.createTestProduct
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductColorDimensionTests {

    @Test
    fun `color dimension component puts selected color first`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductColorDimension.Config
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(testProduct),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateColorDimension,
                awaitItem()
            )

            val expectedColorDimension = ProductColorDimension(
                state = LoadingComponent.State.Done(
                    ProductColorDimension.Content(
                        colorName = "White",
                        colors = listOf(
                            ProductColorDimension.ColorLink(
                                colorName = "White",
                                productId = "02",
                                preview = ProductColorDimension.ColorLink.Preview.Thumbnail(POSITIONED_URL),
                                availability = Availability(
                                    state = Availability.State.IN_STOCK,
                                    quantity = 100
                                ),
                                isSelected = true,
                            ),
                            ProductColorDimension.ColorLink(
                                colorName = "Black",
                                productId = "01",
                                preview = ProductColorDimension.ColorLink.Preview.Thumbnail(POSITIONED_URL),
                                availability = Availability(
                                    state = Availability.State.IN_STOCK,
                                    quantity = 100
                                ),
                                isSelected = false,
                            ),
                            ProductColorDimension.ColorLink(
                                colorName = "Pink",
                                productId = "03",
                                preview = ProductColorDimension.ColorLink.Preview.Thumbnail(POSITIONED_URL),
                                availability = Availability(
                                    state = Availability.State.IN_STOCK,
                                    quantity = 100
                                ),
                                isSelected = false,
                            ),
                        )
                    )
                )
            )
            assertEquals(
                successfulDetailScreenOf(expectedColorDimension),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductColorDimension is omitted in favor of ProductColor component for single color products`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductColorDimension.Config,
                ProductColor.Config,
            )
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(
                VariantData(listOf("Gold")),
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateColorDimension,
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductColor(
                        state = LoadingComponent.State.Done(
                            ProductColor.Content(
                                "Gold"
                            )
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductColor is omitted in favor of ProductColorDimension component for multi-color products`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductColorDimension.Config,
                ProductColor.Config,
            )
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(
                VariantData(listOf("Gold")),
                VariantData(listOf("Silver")),
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateColorDimension,
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductColorDimension(
                        state = LoadingComponent.State.Done(
                            ProductColorDimension.Content(
                                colorName = "Gold",
                                colors = listOf(
                                    ProductColorDimension.ColorLink(
                                        colorName = "Gold",
                                        productId = "Gold",
                                        preview = ProductColorDimension.ColorLink.Preview.Thumbnail(POSITIONED_URL),
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100
                                        ),
                                        isSelected = true,
                                    ),
                                    ProductColorDimension.ColorLink(
                                        colorName = "Silver",
                                        productId = "Silver",
                                        preview = ProductColorDimension.ColorLink.Preview.Thumbnail(POSITIONED_URL),
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100
                                        ),
                                        isSelected = false,
                                    ),
                                )
                            )
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductColorDimension and ProductColor components are omitted for no-variant products`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductColorDimension.Config,
                ProductColor.Config,
            )
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
            variantData = emptyList()
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateColorDimension,
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
        }
    }

    private val loadingStateColorDimension = successfulDetailScreenOf(
        ProductColorDimension(
            state = LoadingComponent.State.Loading(
                Placeholders.productColorDimension
            )
        )
    )
}
