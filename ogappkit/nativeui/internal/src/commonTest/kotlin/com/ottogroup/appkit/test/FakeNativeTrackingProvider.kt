package com.ottogroup.appkit.test

import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.tracking.NativeTracking
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProvider
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View

internal class FakeNativeTrackingProvider : NativeTrackingProvider {
    override val nativeTracking: FakeNativeTracking = FakeNativeTracking()
}

internal class FakeNativeTracking : NativeTracking, TestCallVerifiable by TestCallVerifier() {
    override fun toECommerceItem(product: Product): ECommerceItem {
        return ECommerceItem(name = product.title, id = product.id)
    }

    override fun viewItem(product: Product) {
        recordCall(::viewItem.name, product)
    }

    override fun addItemToCart(productId: String) {
    }

    override fun addItemToWishlist(productId: String) {
    }

    override fun getViewPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): View.ProductDetailPromotion {
        return View.ProductDetailPromotion(
            item = item,
            creativeName = creativeName,
            creativeSlot = creativeSlot,
            promotionId = promotionId,
            promotionName = promotionName,
        )
    }

    override fun getSelectPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): Interaction.ProductDetailSelectPromotion {
        return Interaction.ProductDetailSelectPromotion(
            item = item,
            creativeName = creativeName,
            creativeSlot = creativeSlot,
            promotionId = promotionId,
            promotionName = promotionName,
        )
    }
}
