package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions.Config.Style
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions.ProductDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductVariant
import com.ottogroup.appkit.test.createTestProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductDimensionsTests {

    @Test
    fun `flat style ProductDimensions returns flat dimension`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductDimensions.Config(
            style = Style.FLAT,
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf("Size"),
            variantData = listOf(
                VariantData(listOf("42")),
                VariantData(listOf("44")),
            )
        ).copy(
            sizeAdvisorUrl = "https://www.example.com/size-advisor",
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingState(config),
                awaitItem()
            )

            val expectedDimensions = ProductDimensions(
                state = LoadingComponent.State.Done(
                    content = ProductDimensions.FlatDimension(
                        dimension = ProductDimension(
                            name = "Size",
                            variants = listOf(
                                ProductDimension.VariantLink(
                                    name = "42",
                                    productId = "42",
                                    availability = Availability(
                                        state = Availability.State.IN_STOCK,
                                        quantity = 100,
                                        message = "deliveryInformation",
                                    ),
                                    price = Price(
                                        currency = "EUR",
                                        value = 0,
                                    ),
                                    isSelected = true,
                                ),
                                ProductDimension.VariantLink(
                                    name = "44",
                                    productId = "44",
                                    availability = Availability(
                                        state = Availability.State.IN_STOCK,
                                        quantity = 100,
                                        message = "deliveryInformation",
                                    ),
                                    price = Price(
                                        currency = "EUR",
                                        value = 0,
                                    ),
                                    isSelected = false,
                                ),
                            ),
                        ),
                        isWishlisted = false,
                        productIdForWishlisting = product.id,
                        sizeAdvisorUrl = "https://www.example.com/size-advisor",
                    ),
                ),
                config = config,
            )
            assertEquals(
                successfulDetailScreenOf(expectedDimensions),
                awaitItem()
            )
        }
    }

    @Test
    fun `nested style ProductDimensions returns flat dimension for single-dimension product`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(
                style = Style.NESTED,
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(config)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("42")),
                    VariantData(listOf("44")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )

                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "42",
                                        productId = "42",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "44",
                                        productId = "44",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = config,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `nested style ProductDimensions returns nested dimension for multi-dimension product`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(
                style = Style.NESTED,
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(
                    config,
                    ProductVariant.Config,
                )
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Width"),
                variantData = listOf(
                    VariantData(listOf("Blue", "A", "70")),
                    VariantData(listOf("Blue", "A", "80")),
                    VariantData(listOf("Blue", "B", "80")),
                    VariantData(listOf("Blue", "B", "90")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )

                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.NestedDimensions(
                            name = "Cup",
                            entries = listOf(
                                ProductDimensions.NestedDimensions.Entry(
                                    name = "A",
                                    dimension = ProductDimension(
                                        name = "Width",
                                        variants = listOf(
                                            ProductDimension.VariantLink(
                                                name = "70",
                                                productId = "Blue_A_70",
                                                availability = Availability(
                                                    state = Availability.State.IN_STOCK,
                                                    quantity = 100,
                                                    message = "deliveryInformation",
                                                ),
                                                price = Price(
                                                    currency = "EUR",
                                                    value = 0,
                                                ),
                                                isSelected = true,
                                            ),
                                            ProductDimension.VariantLink(
                                                name = "80",
                                                productId = "Blue_A_80",
                                                availability = Availability(
                                                    state = Availability.State.IN_STOCK,
                                                    quantity = 100,
                                                    message = "deliveryInformation",
                                                ),
                                                price = Price(
                                                    currency = "EUR",
                                                    value = 0,
                                                ),
                                                isSelected = false,
                                            ),
                                        )
                                    )
                                ),
                                ProductDimensions.NestedDimensions.Entry(
                                    name = "B",
                                    dimension = ProductDimension(
                                        name = "Width",
                                        variants = listOf(
                                            ProductDimension.VariantLink(
                                                name = "80",
                                                productId = "Blue_B_80",
                                                availability = Availability(
                                                    state = Availability.State.IN_STOCK,
                                                    quantity = 100,
                                                    message = "deliveryInformation",
                                                ),
                                                price = Price(
                                                    currency = "EUR",
                                                    value = 0,
                                                ),
                                                isSelected = false,
                                            ),
                                            ProductDimension.VariantLink(
                                                name = "90",
                                                productId = "Blue_B_90",
                                                availability = Availability(
                                                    state = Availability.State.IN_STOCK,
                                                    quantity = 100,
                                                    message = "deliveryInformation",
                                                ),
                                                price = Price(
                                                    currency = "EUR",
                                                    value = 0,
                                                ),
                                                isSelected = false,
                                            ),
                                        )
                                    )
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = config,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions component is omitted for single-size products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(style = Style.NESTED)
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(
                    config,
                    ProductVariant.Config,
                )
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
                variantData = listOf(
                    VariantData(listOf("Gold")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )

                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions component is omitted for no-variant products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(style = Style.FLAT)
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(
                    config,
                    ProductVariant.Config,
                ),
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
                variantData = emptyList()
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions component is omitted in favor of ProductVariant for single-variant products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(style = Style.FLAT)
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(
                    config,
                    ProductVariant.Config,
                )
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Width"),
                variantData = listOf(VariantData(listOf("Gold", "C", "80"))),
                fuseNonColorDimensionsInto = ModelMapping.FUSED_SIZE_DIMENSION_NAME,
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )
                assertEquals(
                    successfulDetailScreenOf(
                        ProductVariant(
                            LoadingComponent.State.Done(
                                ProductVariant.Content(
                                    ModelMapping.FUSED_SIZE_DIMENSION_NAME,
                                    ProductDimension.VariantLink(
                                        name = "80C",
                                        productId = "Gold_C_80",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    )
                                )
                            )
                        )
                    ),
                    expectMostRecentItem()
                )
            }
        }

    @Test
    fun `ProductDimensions component is omitted in favor of ProductVariant for multi-color single-variant products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(style = Style.FLAT)
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(
                    config,
                    ProductVariant.Config,
                )
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Width"),
                variantData = listOf(
                    VariantData(listOf("Gold", "C", "80")),
                    VariantData(listOf("Red", "B", "90")),
                    VariantData(listOf("Blue", "B", "90")),
                ),
                fuseNonColorDimensionsInto = ModelMapping.FUSED_SIZE_DIMENSION_NAME,
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )
                assertEquals(
                    successfulDetailScreenOf(
                        ProductVariant(
                            LoadingComponent.State.Done(
                                ProductVariant.Content(
                                    ModelMapping.FUSED_SIZE_DIMENSION_NAME,
                                    ProductDimension.VariantLink(
                                        name = "80C",
                                        productId = "Gold_C_80",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    )
                                )
                            )
                        )
                    ),
                    expectMostRecentItem()
                )
            }
        }

    @Test
    fun `ProductDimensions returns voucher dimension for voucher`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductDimensions.Config(
            style = Style.NESTED,
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Value"),
            variantData = listOf(
                VariantData(listOf("Standard", "10 €")),
                VariantData(listOf("Standard", "100 €")),
                VariantData(listOf("Standard", "50 €")),
            )
        ).copy(
            voucherSpec = VoucherSpec(
                allowsCustomName = true,
                maxCustomNameLength = 20,
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingState(config),
                awaitItem()
            )

            val expectedDimensions = ProductDimensions(
                state = LoadingComponent.State.Done(
                    content = ProductDimensions.VoucherDimension(
                        dimension = ProductDimension(
                            name = "Value",
                            variants = listOf(
                                ProductDimension.VariantLink(
                                    name = "10 €",
                                    productId = "Standard_10 €",
                                    availability = Availability(
                                        state = Availability.State.IN_STOCK,
                                        quantity = 100,
                                        message = "deliveryInformation",
                                    ),
                                    price = Price(
                                        currency = "EUR",
                                        value = 0,
                                    ),
                                    isSelected = true,
                                ),
                                ProductDimension.VariantLink(
                                    name = "50 €",
                                    productId = "Standard_50 €",
                                    availability = Availability(
                                        state = Availability.State.IN_STOCK,
                                        quantity = 100,
                                        message = "deliveryInformation",
                                    ),
                                    price = Price(
                                        currency = "EUR",
                                        value = 0,
                                    ),
                                    isSelected = false,
                                ),
                                ProductDimension.VariantLink(
                                    name = "100 €",
                                    productId = "Standard_100 €",
                                    availability = Availability(
                                        state = Availability.State.IN_STOCK,
                                        quantity = 100,
                                        message = "deliveryInformation",
                                    ),
                                    price = Price(
                                        currency = "EUR",
                                        value = 0,
                                    ),
                                    isSelected = false,
                                ),
                            ),
                        ),
                        customName = ProductDimensions.VoucherDimension.CustomName(maxLength = 20),
                        isWishlisted = false,
                        productIdForWishlisting = product.id,
                        sizeAdvisorUrl = null,
                    )
                ),
                config = config,
            )
            assertEquals(
                successfulDetailScreenOf(expectedDimensions),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductDimensions updates when wishlist state changes`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductDimensions.Config(style = Style.FLAT)
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf("Size"),
            variantData = listOf(
                VariantData(listOf("42")),
                VariantData(listOf("44")),
            )
        )

        val expectedContent = ProductDimensions.FlatDimension(
            dimension = ProductDimension(
                name = "Size",
                variants = listOf(
                    ProductDimension.VariantLink(
                        name = "42",
                        productId = "42",
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 100,
                            message = "deliveryInformation",
                        ),
                        price = Price(
                            currency = "EUR",
                            value = 0,
                        ),
                        isSelected = true,
                    ),
                    ProductDimension.VariantLink(
                        name = "44",
                        productId = "44",
                        availability = Availability(
                            state = Availability.State.IN_STOCK,
                            quantity = 100,
                            message = "deliveryInformation",
                        ),
                        price = Price(
                            currency = "EUR",
                            value = 0,
                        ),
                        isSelected = false,
                    ),
                ),
            ),
            isWishlisted = false,
            productIdForWishlisting = product.id,
            sizeAdvisorUrl = null,
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingState(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductDimensions(
                        state = LoadingComponent.State.Done(content = expectedContent),
                        config = config,
                    )
                ),
                awaitItem()
            )

            testObjects.fakeWishlistRepository.addProductToWishlist(product.id)
            assertEquals(
                successfulDetailScreenOf(
                    ProductDimensions(
                        state = LoadingComponent.State.Done(content = expectedContent.copy(isWishlisted = true)),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductDimensions uses longTitleStyle when variant text exceeds character limit in individual dimensions`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val originalConfig = ProductDimensions.Config(
                style = Style.FLAT,
                longTitleStyle = ProductDimensions.Config.LongTitleStyle(
                    characterLimit = 5,
                    style = Style.NESTED
                )
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(originalConfig)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("Very Long Size Name")),
                    VariantData(listOf("Small")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(originalConfig),
                    awaitItem()
                )

                val expectedConfig = originalConfig.copy(style = Style.NESTED)
                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "Small",
                                        productId = "Small",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "Very Long Size Name",
                                        productId = "Very Long Size Name",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = expectedConfig,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions uses original style when variant text is within character limit`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(
                style = Style.FLAT,
                longTitleStyle = ProductDimensions.Config.LongTitleStyle(
                    characterLimit = 20,
                    style = Style.NESTED
                )
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(config)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("42")),
                    VariantData(listOf("44")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )

                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "42",
                                        productId = "42",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "44",
                                        productId = "44",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = config,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions uses longTitleStyle when size matrix entries exceed character limit`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val originalConfig = ProductDimensions.Config(
                style = Style.NESTED,
                longTitleStyle = ProductDimensions.Config.LongTitleStyle(
                    characterLimit = 10,
                    style = Style.FLAT
                )
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(originalConfig)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("Very Long Size Name")),
                    VariantData(listOf("Short")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(originalConfig),
                    awaitItem()
                )

                val expectedConfig = originalConfig.copy(style = Style.FLAT)
                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "Short",
                                        productId = "Short",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "Very Long Size Name",
                                        productId = "Very Long Size Name",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = expectedConfig,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions uses longTitleStyle when fused dimensions exceed character limit`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val originalConfig = ProductDimensions.Config(
                style = Style.FLAT,
                longTitleStyle = ProductDimensions.Config.LongTitleStyle(
                    characterLimit = 8,
                    style = Style.FLATCHIPS
                )
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(originalConfig)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("Short")),
                    VariantData(listOf("Very Long Width"))
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(originalConfig),
                    awaitItem()
                )

                val expectedConfig = originalConfig.copy(style = Style.FLATCHIPS)
                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "Short",
                                        productId = "Short",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "Very Long Width",
                                        productId = "Very Long Width",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = expectedConfig,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions with no longTitleStyle config always uses original style`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ProductDimensions.Config(
                style = Style.NESTED
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(config)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("Very Very Very Long Size Name That Definitely Exceeds Any Reasonable Limit")),
                    VariantData(listOf("44")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(config),
                    awaitItem()
                )

                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "44",
                                        productId = "44",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "Very Very Very Long Size Name That Definitely Exceeds Any Reasonable Limit",
                                        productId = "Very Very Very Long Size Name That Definitely Exceeds Any Reasonable Limit",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = config,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    @Test
    fun `ProductDimensions checks individual dimensions when different from fused dimensions`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val originalConfig = ProductDimensions.Config(
                style = Style.FLAT,
                longTitleStyle = ProductDimensions.Config.LongTitleStyle(
                    characterLimit = 5,
                    style = Style.NESTED
                )
            )
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(originalConfig)
            )

            val product = createTestProduct(
                variantLabels = listOf("Size"),
                variantData = listOf(
                    VariantData(listOf("Regular")),
                    VariantData(listOf("XL")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    loadingState(originalConfig),
                    awaitItem()
                )

                val expectedConfig = originalConfig.copy(style = Style.NESTED)
                val expectedDimensions = ProductDimensions(
                    state = LoadingComponent.State.Done(
                        content = ProductDimensions.FlatDimension(
                            dimension = ProductDimension(
                                name = "Size",
                                variants = listOf(
                                    ProductDimension.VariantLink(
                                        name = "Regular",
                                        productId = "Regular",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = true,
                                    ),
                                    ProductDimension.VariantLink(
                                        name = "XL",
                                        productId = "XL",
                                        availability = Availability(
                                            state = Availability.State.IN_STOCK,
                                            quantity = 100,
                                            message = "deliveryInformation",
                                        ),
                                        price = Price(
                                            currency = "EUR",
                                            value = 0,
                                        ),
                                        isSelected = false,
                                    ),
                                ),
                            ),
                            isWishlisted = false,
                            productIdForWishlisting = product.id,
                            sizeAdvisorUrl = null,
                        )
                    ),
                    config = expectedConfig,
                )
                assertEquals(
                    successfulDetailScreenOf(expectedDimensions),
                    awaitItem()
                )
            }
        }

    private fun loadingState(config: ProductDimensions.Config) = successfulDetailScreenOf(
        ProductDimensions(
            state = LoadingComponent.State.Loading(
                Placeholders.productDimensions
            ),
            config
        )
    )
}
