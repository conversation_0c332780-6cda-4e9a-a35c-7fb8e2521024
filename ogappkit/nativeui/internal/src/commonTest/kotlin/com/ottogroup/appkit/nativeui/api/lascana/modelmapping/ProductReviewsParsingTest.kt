package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.buildProductReviewsQueryData
import com.ottogroup.appkit.nativeui.lascana.type.buildFlyout
import com.ottogroup.appkit.nativeui.lascana.type.buildManufacturer
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildProductRating
import com.ottogroup.appkit.nativeui.lascana.type.buildReview
import com.ottogroup.appkit.nativeui.lascana.type.buildReviewer
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.datetime.Clock

class ProductReviewsParsingTest {

    @Test
    fun `parses reviews correctly`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val now = Clock.System.now()

        val productReviewsQueryData = buildProductReviewsQueryData {
            product = buildProduct {
                id = "1"
                parentId = "0"
                title = "Product"
                manufacturer = buildManufacturer {
                    title = "Manufacturer"
                }
                rating = buildProductRating {
                    rating = 3.5
                    count = 2
                }
                reviews = listOf(
                    buildReview {
                        rating = 4
                        title = "Good"
                        text = "Good product<br><br> It fits &gt;perfectly&lt;."
                        reviewer = buildReviewer { firstName = "John" }
                        createAt = now
                    },
                    buildReview {
                        rating = 3
                        title = "Mid"
                        text = "Mid product"
                        reviewer = buildReviewer { firstName = "Anna" }
                        createAt = now
                    },
                )
                flyouts = buildFlyout {
                    reviews = "/write-review"
                }
            }
        }

        val domainProduct = mapping.toProductReviews(productReviewsQueryData.product)
        assertEquals(
            ProductReviews(
                id = "1",
                title = "Product",
                brandName = "Manufacturer",
                Reviews(
                    rating = Rating(
                        averageRating = 3.5f,
                        count = 2,
                        ratingDistribution = mapOf(4 to Pair(first = 1, second = 50.0), 3 to Pair(1, 50.0))
                    ),
                    reviews = listOf(
                        Review(
                            title = "Good",
                            text = "Good product\n\nIt fits >perfectly<.",
                            rating = 4,
                            reviewerName = "John",
                            dateTime = now,
                        ),
                        Review(
                            title = "Mid",
                            text = "Mid product",
                            rating = 3,
                            reviewerName = "Anna",
                            dateTime = now,
                        )
                    ),
                    writeReviewUrl = "/write-review",
                )
            ),
            domainProduct
        )
    }
}
