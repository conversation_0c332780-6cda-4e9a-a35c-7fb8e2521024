package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.apollographql.apollo.api.BuilderScope
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.buildProductDetailQueryData
import com.ottogroup.appkit.nativeui.api.lascana.data.complexTestQueryProduct
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.type.ProductMap
import com.ottogroup.appkit.nativeui.lascana.type.QueryBuilder
import com.ottogroup.appkit.nativeui.lascana.type.buildAttribute
import com.ottogroup.appkit.nativeui.lascana.type.buildCurrency
import com.ottogroup.appkit.nativeui.lascana.type.buildFlyout
import com.ottogroup.appkit.nativeui.lascana.type.buildManufacturer
import com.ottogroup.appkit.nativeui.lascana.type.buildMoreFromSeries
import com.ottogroup.appkit.nativeui.lascana.type.buildPrice
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildProductAttribute
import com.ottogroup.appkit.nativeui.lascana.type.buildProductImage
import com.ottogroup.appkit.nativeui.lascana.type.buildProductImageGallery
import com.ottogroup.appkit.nativeui.lascana.type.buildProductRating
import com.ottogroup.appkit.nativeui.lascana.type.buildReview
import com.ottogroup.appkit.nativeui.lascana.type.buildReviewer
import com.ottogroup.appkit.nativeui.lascana.type.buildSeriesProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildShopTheLook
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.datetime.Clock

class BasicMappingTest {

    @Test
    fun `title equals title from query result`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val testQueryProduct = complexTestQueryProduct.copy(
            title = "Schalen-BH",
        )

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            "Schalen-BH",
            domainProduct.title,
        )
    }

    @Test
    fun `title equals title plus additional product name from query result`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        // required because Apollo's data builder DSL functions are only available when creating full query data
        val additionalProductDataHolder = buildProductDetailQueryData {
            product = buildProduct {
                this["nonDetailPageAttributes"] = listOf(
                    buildProductAttribute {
                        attribute = buildAttribute {
                            title = "Produkt-Name"
                            groupName = "Artikelbezeichnung"
                        }
                        value = "VICTORIA SWAROVSKI X LASCANA LIMITED COLLECTION"
                    },
                )
            }
        }.product

        val testQueryProduct = complexTestQueryProduct.copy(
            title = "Schalen-BH",
            nonDetailPageAttributes = complexTestQueryProduct.nonDetailPageAttributes + additionalProductDataHolder.nonDetailPageAttributes
        )

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            "Schalen-BH \"VICTORIA SWAROVSKI X LASCANA LIMITED COLLECTION\"",
            domainProduct.title,
        )
    }

    @Test
    fun `brand data does not contain HTML`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val testQueryProduct = buildProductDetailQueryData {
            product = buildProduct {
                manufacturer = buildManufacturer {
                    title = "Gut &amp; G&uuml;nstig"
                    shortdesc = "Gut &amp; G&uuml;nstig: <b>Lecker!</b>"
                }
            }
        }.product

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Brand(
                name = "Gut & Günstig",
                description = "Gut & Günstig: Lecker!",
            ),
            domainProduct.brand,
        )
    }

    @Test
    fun `reviews contains only URL when there are no ratings and reviews`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val testQueryProduct = buildProductDetailQueryData {
            product = buildProduct {
                rating = buildProductRating {
                    rating = 0.0
                    count = 0
                }
                reviews = emptyList()
                flyouts = buildFlyout {
                    reviews = "/write-review"
                }
            }
        }.product

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "/write-review",
            ),
            domainProduct.reviews
        )
    }

    @Test
    fun `rating is calculated from review if there is no rating but there are reviews`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val now = Clock.System.now()

        val testQueryProduct = buildProductDetailQueryData {
            product = buildProduct {
                rating = buildProductRating {
                    rating = 0.0
                    count = 0
                }
                reviews = listOf(
                    buildReview {
                        rating = 4
                        title = "Good"
                        text = "Good product"
                        reviewer = buildReviewer { firstName = "John" }
                        createAt = now
                    },
                    buildReview {
                        rating = 3
                        title = "Mid"
                        text = "Mid product"
                        reviewer = buildReviewer { firstName = "Anna" }
                        createAt = now
                    },
                )
                flyouts = buildFlyout {
                    reviews = "/write-review"
                }
            }
        }.product

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Reviews(
                rating = Rating(
                    averageRating = 3.5f,
                    count = 2,
                    ratingDistribution = mapOf(4 to Pair(first = 1, second = 50.0), 3 to Pair(1, 50.0))
                ),
                reviews = listOf(
                    Review(
                        title = "Good",
                        text = "Good product",
                        rating = 4,
                        reviewerName = "John",
                        dateTime = now,
                    ),
                    Review(
                        title = "Mid",
                        text = "Mid product",
                        rating = 3,
                        reviewerName = "Anna",
                        dateTime = now,
                    )
                ),
                writeReviewUrl = "/write-review",
            ),
            domainProduct.reviews
        )
    }

    @Test
    fun `deliveryTime is always null`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val domainProduct = mapping.toProduct(complexTestQueryProduct)
        assertNull(
            domainProduct.availability.deliveryTime,
        )
    }

    @Test
    fun `availability is set based on variant data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val message = "Delivery in 2-3 days"
        val testQueryProduct = complexTestQueryProduct.run {
            val modifiedVariants = this.variants.toMutableList().apply {
                val i = indexOfFirst { it.id == id }
                val thisVariant = removeAt(i)
                add(
                    thisVariant.copy(
                        variantInfo = thisVariant.variantInfo.copy(
                            availabilityFields = thisVariant.variantInfo.availabilityFields.copy(
                                deliveryInformation = message,
                                stock = thisVariant.variantInfo.availabilityFields.stock.copy(
                                    stock = 123.0
                                )
                            ),
                        )
                    )
                )
            }
            this.copy(variants = modifiedVariants)
        }
        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Availability(
                state = Availability.State.IN_STOCK,
                quantity = 123,
                message = message
            ),
            domainProduct.availability
        )
    }

    @Test
    fun `availability falls back to direct data when there are no variants`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val message = "Delivery in 2-3 days"
        val testQueryProduct = complexTestQueryProduct.copy(
            availabilityFields = complexTestQueryProduct.availabilityFields.copy(
                deliveryInformation = message
            ),
            variants = emptyList()
        )

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Availability(
                state = Availability.State.IN_STOCK,
                quantity = 100,
                message = message
            ),
            domainProduct.availability
        )
    }

    @Test
    fun `price is set based on variant data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val newPrice = buildProductDetailQueryData {
            product = buildProduct {
                variants = listOf(
                    buildProduct {
                        price = buildPrice {
                            price = 19.99
                            currency = buildCurrency {
                                name = "EUR"
                            }
                        }
                        listPrice = buildPrice {
                            price = 25.00
                            currency = buildCurrency {
                                name = "EUR"
                            }
                        }
                    }
                )
            }
        }.product.variants.first().variantInfo.completePrice

        val testQueryProduct = complexTestQueryProduct.run {
            val modifiedVariants = this.variants.toMutableList().apply {
                val i = indexOfFirst { it.id == id }
                val thisVariant = removeAt(i)
                add(
                    thisVariant.copy(
                        variantInfo = thisVariant.variantInfo.copy(completePrice = newPrice),
                    )
                )
            }
            this.copy(variants = modifiedVariants)
        }
        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Price(
                currency = "EUR",
                value = 1999,
                oldValue = 2500
            ),
            domainProduct.price
        )
    }

    @Test
    fun `price falls back to direct data when there are no variants`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val newPrice = buildProductDetailQueryData {
            product = buildProduct {
                variants = listOf(
                    buildProduct {
                        price = buildPrice {
                            price = 19.99
                            currency = buildCurrency {
                                name = "EUR"
                            }
                        }
                        listPrice = buildPrice {
                            price = 25.00
                            currency = buildCurrency {
                                name = "EUR"
                            }
                        }
                    }
                )
            }
        }.product.variants.first().variantInfo.completePrice

        val testQueryProduct = complexTestQueryProduct.copy(
            completePrice = complexTestQueryProduct.completePrice.copy(
                price = newPrice.price,
                listPrice = newPrice.listPrice,
            ),
            variants = emptyList()
        )

        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            Price(
                currency = "EUR",
                value = 1999,
                oldValue = 2500
            ),
            domainProduct.price
        )
    }

    @Test
    fun `payback points are set`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val testQueryProduct = complexTestQueryProduct.copy(
            paybackPoints = 18.0,
        )
        val domainProduct = mapping.toProduct(testQueryProduct)
        assertEquals(
            18,
            domainProduct.paybackPoints
        )
    }

    @Test
    fun `flags are correctly mapped`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val testQueryProduct = complexTestQueryProduct.copy(
            flags = listOf(
                ProductDetailQuery.Flag(
                    __typename = "ProductFlag",
                    type = "SALE",
                    priority = "1"
                ),
                ProductDetailQuery.Flag(
                    __typename = "ProductFlag",
                    type = "NEW",
                    priority = "2"
                ),
                ProductDetailQuery.Flag(
                    __typename = "ProductFlag",
                    type = "UNEXPECTED",
                    priority = "0"
                )
            )
        )
        val domainProduct = mapping.toProduct(testQueryProduct)
        val expectedFlags = listOf(
            Flag(type = Flag.FlagType.SALE),
            Flag(type = Flag.FlagType.NEW),
        )
        assertEquals(
            expectedFlags,
            domainProduct.flags,
        )
    }

    @Test
    fun `shopTheLook is null if not contained in query data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)
        val domainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {
                    shopTheLook = null
                }
            }.product
        )
        assertNull(domainProduct.shopTheLook)
    }

    @Test
    fun `shopTheLook is parsed correctly`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        fun QueryBuilder.buildSTLProduct(i: Int): ProductMap = buildProduct {
            id = "$i"
            sku = "${i}_XS"
            parentId = "0"
            title = "Product"
            manufacturer = buildManufacturer {
                title = "Manufacturer"
                shortdesc = "<b>Cool &amp; sustainable</b>"
            }
            price = buildPrice {
                price = i * 10.0
                currency = buildCurrency { name = "EUR" }
            }
            listPrice = null
            imageGallery = buildProductImageGallery {
                images = listOf(
                    buildProductImage {
                        image = "https://example.com/image$i.jpg"
                    }
                )
            }
        }

        val testQueryProduct = buildProductDetailQueryData {
            product = buildProduct {
                shopTheLook = buildShopTheLook {
                    mainImagePath = "https://example.com/mainImage.jpg"
                    articles = listOf(
                        buildSTLProduct(1),
                        buildSTLProduct(2),
                    )
                }
            }
        }.product

        val domainProduct = mapping.toProduct(testQueryProduct)

        val expectedDomainProduct = Product(
            id = "1",
            parentId = "0",
            title = "Product",
            shortTitle = "Product",
            webShopUrl = null,
            images = listOf(
                Image(
                    url = "https://example.com/image1.jpg",
                    thumbnailUrl = "https://example.com/image1.jpg",
                )
            ),
            flags = emptyList(),
            brand = Brand(name = "Manufacturer", description = null),
            price = Price(currency = "EUR", value = 1000),
            fusedDimensions = emptyList(),
            individualDimensions = emptyList(),
            sizeMatrix = null,
            availability = Availability(
                state = Availability.State.UNKNOWN,
                quantity = 0,
            ),
            information = Information(
                articleNumber = "",
                description = "",
                bulletPoints = emptyList(),
                attributesTable = Information.AttributesTable(emptyList()),
                distributingCompanies = emptyList(),
                documents = emptyList(),
                articleStandards = null,
            ),
            reviews = Reviews(rating = null, reviews = emptyList(), writeReviewUrl = ""),
            sku = "1_XS",
            shopTheLook = null,
            moreFromTheSeries = null,
            breadcrumbs = emptyList(),
        )

        assertEquals(
            ShopTheLook(
                image = Image("https://example.com/mainImage.jpg", null),
                articles = listOf(
                    expectedDomainProduct,
                    expectedDomainProduct.copy(
                        id = "2",
                        sku = "2_XS",
                        price = Price(currency = "EUR", value = 2000),
                        images = listOf(
                            Image(
                                url = "https://example.com/image2.jpg",
                                thumbnailUrl = "https://example.com/image2.jpg"
                            )
                        )
                    )
                )
            ),
            domainProduct.shopTheLook,
        )
    }

    @Test
    fun `moreFromTheSeries is null if not contained in query data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)
        val domainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {
                    moreFromSeries = null
                }
            }.product
        )
        assertNull(domainProduct.moreFromTheSeries)
    }

    @Test
    fun `moreFromTheSeries is parsed correctly`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        fun BuilderScope.buildMFSArticle(i: Int) = buildProduct {
            id = "$i"
            sku = "$i"
            parentId = "0"
            title = "Product"
            manufacturer = buildManufacturer {
                title = "Manufacturer"
                shortdesc = "<b>Cool &amp; sustainable</b>"
            }
            price = buildPrice {
                price = i * 10.0
                currency = buildCurrency { name = "EUR" }
            }
            listPrice = null
            imageGallery = buildProductImageGallery {
                images = listOf(
                    buildProductImage {
                        image = "https://example.com/image$i.jpg"
                    }
                )
            }
        }

        val testQueryProduct = buildProductDetailQueryData {
            product = buildProduct {
                moreFromSeries = buildMoreFromSeries {
                    articles = (1..2).map {
                        buildSeriesProduct {
                            article = buildMFSArticle(it)
                        }
                    }
                }
            }
        }.product

        val domainProduct = mapping.toProduct(testQueryProduct)

        val expectedDomainProduct = Product(
            id = "1",
            parentId = "0",
            title = "Product",
            shortTitle = "Product",
            webShopUrl = null,
            images = listOf(
                Image(
                    url = "https://example.com/image1.jpg",
                    thumbnailUrl = "https://example.com/image1.jpg",
                )
            ),
            flags = emptyList(),
            brand = Brand(name = "Manufacturer", description = null),
            price = Price(
                currency = "EUR",
                value = 1000,
            ),
            fusedDimensions = emptyList(),
            individualDimensions = emptyList(),
            sizeMatrix = null,
            availability = Availability(
                state = Availability.State.UNKNOWN,
                quantity = 0,
            ),
            information = Information(
                articleNumber = "",
                description = "",
                bulletPoints = emptyList(),
                attributesTable = Information.AttributesTable(emptyList()),
                distributingCompanies = emptyList(),
                documents = emptyList(),
                articleStandards = null,
            ),
            reviews = Reviews(rating = null, reviews = emptyList(), writeReviewUrl = ""),
            shopTheLook = null,
            moreFromTheSeries = null,
            breadcrumbs = emptyList(),
        )

        assertEquals(
            MoreFromTheSeries(
                articles = listOf(
                    expectedDomainProduct,
                    expectedDomainProduct.copy(
                        id = "2",
                        sku = "2",
                        price = Price(currency = "EUR", value = 2000),
                        images = listOf(
                            Image(
                                url = "https://example.com/image2.jpg",
                                thumbnailUrl = "https://example.com/image2.jpg"
                            )
                        )
                    )
                )
            ),
            domainProduct.moreFromTheSeries,
        )
    }

    @Test
    fun `voucher state is correctly determined`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)
        val voucherDomainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {
                    parentId = ModelMapping.VOUCHER_PARENT_ID
                }
            }.product
        )
        assertEquals(
            VoucherSpec(
                allowsCustomName = true,
                maxCustomNameLength = ModelMapping.VOUCHER_MAX_NAME_LENGTH
            ),
            voucherDomainProduct.voucherSpec
        )

        val nonVoucherDomainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {}
            }.product
        )
        assertNull(nonVoucherDomainProduct.voucherSpec)
    }

    @Test
    fun `size advisor URL is null if not contained in query data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)
        val domainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {
                    flyouts = buildFlyout {
                        sizeGuide = null
                    }
                }
            }.product
        )
        assertNull(domainProduct.sizeAdvisorUrl)
    }

    @Test
    fun `size advisor URL is set if contained in query data`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)
        val domainProduct = mapping.toProduct(
            buildProductDetailQueryData {
                product = buildProduct {
                    flyouts = buildFlyout {
                        sizeGuide = "https://www.example.com/size-advisor"
                    }
                }
            }.product
        )
        assertEquals(
            "https://www.example.com/size-advisor",
            domainProduct.sizeAdvisorUrl
        )
    }
}
