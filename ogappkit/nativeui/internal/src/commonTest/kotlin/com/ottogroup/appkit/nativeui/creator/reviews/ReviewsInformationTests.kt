package com.ottogroup.appkit.nativeui.creator.reviews

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.ProductReviewsRepository
import com.ottogroup.appkit.nativeui.creator.reviews.ProductReviewsScreenCreatorTestObjects.creator
import com.ottogroup.appkit.nativeui.creator.reviews.ProductReviewsScreenCreatorTestObjects.reviewsFlow
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ReviewsInformation
import com.ottogroup.appkit.test.testProductReviews
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest

class ReviewsInformationTests {

    @Test
    fun `ReviewsInformation returns information content`() = runTest {
        val config = ReviewsInformation.Config()
        val configs: ComponentConfigs<ProductReviewsComponentConfig> = ComponentConfigs(
            listOf(config)
        )
        creator.createScreen(
            reviewsFlow(testProductReviews),
            configs,
            ProductReviewsRepository(reviewsPerPage = 15),
            "12345"
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    ReviewsInformation(
                        brandName = "Manufacturer",
                        title = "Product",
                        rating = Rating(
                            averageRating = 3.5f,
                            count = 2,
                            ratingDistribution = mapOf(4 to Pair(first = 1, second = 50.0), 3 to Pair(1, 50.0))
                        ),
                        screenId = "12345",
                        config = config
                    )
                ),
                awaitItem()
            )
        }
    }
}
