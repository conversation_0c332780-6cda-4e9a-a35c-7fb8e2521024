package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.Contextual
import com.ottogroup.appkit.nativeui.api.FromNothing
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketButton
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductAvailability
import com.ottogroup.appkit.nativeui.model.ui.ProductColor
import com.ottogroup.appkit.nativeui.model.ui.ProductColorDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductHeader
import com.ottogroup.appkit.nativeui.model.ui.ProductInformation
import com.ottogroup.appkit.nativeui.tracking.NativeTracking
import com.ottogroup.appkit.test.createTestProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

class BasicProductDetailScreenCreatorTest {

    @Test
    fun `screen returns failure if product fails to be loaded`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(ProductAvailability.Config)
        )

        val error = Exception()

        testObjects.creator.createScreen(
            MutableStateFlow(Result.Failure<Product>(error)).asOperation().map { Contextual(FromNothing, it) },
            configs,
            screenId = testObjects.screenId
        ).test {
            // first we display loading
            assertEquals(
                successfulDetailScreenOf(
                    ProductAvailability(
                        state = LoadingComponent.State.Loading(
                            Placeholders.productAvailability
                        )
                    )
                ),
                awaitItem()
            )

            assertEquals(
                Result.Failure(error),
                awaitItem()
            )
        }
    }

    @Test
    fun `screen is tracked if product is loaded`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductHeader.Config,
                ProductAvailability.Config,
            )
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold"))),
        )
        val differentProduct = product.copy(sku = "new sku")

        val productFlow = MutableSharedFlow<Result<Product>>()
        testObjects.creator.createScreen(
            productFlow.asOperation().map { Contextual(FromNothing, it) },
            configs,
            screenId = testObjects.screenId
        ).test {
            // first we display loading and don't track yet
            testObjects.fakeTracking.nativeTracking.verifyNone(NativeTracking::viewItem.name)

            productFlow.emit(Result.Success(product))
            // next is the successful screen, which we track
            awaitItem()
            testObjects.fakeTracking.nativeTracking.verify(NativeTracking::viewItem.name, listOf(product))

            productFlow.emit(Result.Success(product.copy(shortTitle = "New Title")))
            // the product updated but has the same SKU -> we don't track another time
            awaitItem()
            testObjects.fakeTracking.nativeTracking.verify(NativeTracking::viewItem.name, listOf(product))

            productFlow.emit(Result.Success(differentProduct.copy(isOptimisticFake = true)))
            // the product updated and has another SKU but is an optimistic fake from cache -> we don't track
            awaitItem()
            testObjects.fakeTracking.nativeTracking.verify(NativeTracking::viewItem.name, listOf(product))

            productFlow.emit(Result.Success(differentProduct))
            // the product updated and is no longer a fake -> we track another time
            awaitItem()
            testObjects.fakeTracking.nativeTracking.verify(
                NativeTracking::viewItem.name,
                listOf(product),
                listOf(differentProduct)
            )
            cancelAndIgnoreRemainingEvents()
        }

        // no further tracking has happened
        testObjects.fakeTracking.nativeTracking.verify(
            NativeTracking::viewItem.name,
            listOf(product),
            listOf(differentProduct)
        )
    }

    @Test
    fun `AddToBasketButton component is omitted for no-variant products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(AddToBasketButton.Config)
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Size"),
                variantData = emptyList()
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId
            ).test {
                assertEquals(
                    successfulDetailScreenOf(
                        AddToBasketButton(
                            state = LoadingComponent.State.Loading(
                                AddToBasketButton.Content.AddToBasket(Placeholders.productId)
                            ),
                        )
                    ),
                    awaitItem()
                )

                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )
            }
        }

    @Test
    fun `AddToBasketButton component becomes notify me when product is unavailable`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
                listOf(AddToBasketButton.Config)
            )

            val product = createTestProduct(
                variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
                variantData = listOf(
                    VariantData(variantValues = listOf("Black"), stockStatus = ModelMapping.STOCK_STATUS_NOT_IN_STOCK),
                    VariantData(variantValues = listOf("White")),
                )
            )

            testObjects.creator.createScreen(
                testObjects.productFlow(product),
                configs,
                screenId = testObjects.screenId
            ).test {
                assertEquals(
                    successfulDetailScreenOf(
                        AddToBasketButton(
                            state = LoadingComponent.State.Loading(
                                AddToBasketButton.Content.AddToBasket(Placeholders.productId)
                            ),
                        )
                    ),
                    awaitItem()
                )

                assertEquals(
                    successfulDetailScreenOf(
                        AddToBasketButton(
                            state = LoadingComponent.State.Done(
                                AddToBasketButton.Content.NotifyMe("/notify-me")
                            ),
                        )
                    ),
                    awaitItem()
                )
            }
        }

    @Test
    fun `applicable components are omitted for voucher products`() = runProductDetailScreenCreatorTest { testObjects ->
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(
                ProductColor.Config,
                ProductColorDimension.Config,
                ProductInformation.Config,
                AddToBasketButton.Config,
            )
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Value"),
            variantData = listOf(
                VariantData(listOf("Standard", "10 €")),
                VariantData(listOf("Standard", "50 €")),
                VariantData(listOf("Standard", "100 €")),
            )
        ).copy(
            voucherSpec = VoucherSpec(allowsCustomName = true)
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId
        ).test {
            // Loading state
            awaitItem()

            assertEquals(
                successfulDetailScreenOf(
                    // color and information components are not displayed for vouchers
                    AddToBasketButton(
                        state = LoadingComponent.State.Done(
                            AddToBasketButton.Content.AddToBasket(product.id)
                        ),
                    )
                ),
                awaitItem()
            )
        }
    }
}
