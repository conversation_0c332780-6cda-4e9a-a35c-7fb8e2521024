plugins {
    embeddedKotlin("jvm")
    `kotlin-dsl`
}

dependencies {
    implementation(files(libs.javaClass.superclass.protectionDomain.codeSource.location))

    implementation(libs.plugins.kotlinMultiplatform)
    implementation(libs.plugins.kotlinxSerialization)
    implementation(libs.kotlinx.serialization)
    implementation(libs.plugins.androidLibrary)
    implementation(libs.plugins.kmmbridge)
    implementation(libs.plugins.skie)
    implementation(libs.plugins.ktlint)
    implementation(libs.plugins.detekt)
}

// This is a future Gradle feature to make declaring dependencies to plugins simpler
// See and upvote https://docs.google.com/document/d/1P7aTeeVNhkhwxcS5sQNFrSsmqJOhDo3G8kUdhtp_vyM
fun DependencyHandler.implementation(pluginDependency: Provider<PluginDependency>): Dependency? =
    add(
        "implementation",
        pluginDependency.map {
            "${it.pluginId}:${it.pluginId}.gradle.plugin:${it.version.requiredVersion}"
        }.get()
    )

fun DependencyHandler.implementation(
    pluginDependency: Provider<PluginDependency>,
    dependencyConfiguration: ExternalModuleDependency.() -> Unit
): Dependency = add(
    "implementation",
    pluginDependency.map {
        "${it.pluginId}:${it.pluginId}.gradle.plugin:${it.version.requiredVersion}"
    }.get(),
    dependencyConfiguration = dependencyConfiguration
)
