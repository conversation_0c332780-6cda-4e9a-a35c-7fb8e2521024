import com.ottogroup.appkit.resources.ResourceConfiguration
import com.ottogroup.appkit.resources.ResourceExtension
import com.ottogroup.appkit.resources.ResourceSourceBundledFilesGeneratorTask
import com.ottogroup.appkit.resources.ResourceSourceWrapperGeneratorTask
import org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension
import org.jetbrains.kotlin.gradle.plugin.sources.android.findAndroidSourceSet

plugins {
    id("ogAppKit.libraryModule")
}

private val extension = project.extensions.create<ResourceExtension>("OGResources")

kotlin {
    configureResourceBundling(extension)
}

private fun String.capitalize(): String {
    return this.replaceFirstChar { it.uppercase() }
}

private fun KotlinMultiplatformExtension.configureResourceBundling(extension: ResourceExtension) {
    val bundleTask = getOrRegisterIOSBundleTask(extension)
    extension.resources.all {
        configureAndroidResourceBundling(name, from, destPath)
        configureIosResourceBundling(bundleTask, this)
    }
}

private fun KotlinMultiplatformExtension.configureAndroidResourceBundling(
    collectionName: String,
    resources: ConfigurableFileCollection,
    destinationDir: Property<String>
) {
    sourceSets {
        // Configure all android source sets to copy the common resources into the intermediate assets folder
        // the intermediate assetsfolder will be merged into the final assets folder by the android plugin
        // duplicate files will be replaced, first common, then specific
        all {
            val kotlinSourceSetName = name
            val androidSourceSet = findAndroidSourceSet(this)
            androidSourceSet?.let {
                val buildDir =
                    layout.buildDirectory.dir("intermediates/og_assets/${kotlinSourceSetName}")
                val destination = destinationDir.flatMap { dest -> buildDir.map { it.dir(dest) } }
                it.assets {
                    val copyTask =
                        tasks.register<Copy>("copy${collectionName}OGResourcesTo${kotlinSourceSetName.capitalize()}Assets") {
                            group = "ogAppKit"
                            description = "Copy resources to Android assets"
                            duplicatesStrategy = DuplicatesStrategy.WARN
                            from(resources)
                            into(destination)
                        }
                    srcDirs(buildDir)

                    // register copy task as task dependency for the generateAssets task
                    tasks.named {
                        it == "generateReleaseAssets" || it == "generateDebugAssets"
                    }
                        .configureEach {

                            dependsOn(copyTask)
                        }

                    tasks.named {
                        it == "generateDebugLintModel"
                            || it == "generateReleaseLintModel"
                            || it == "lintAnalyzeDebug"
                            || it == "lintAnalyzeRelease"
                            || it == "generateDebugLintReportModel"
                            || it == "generateReleaseLintReportModel"
                    }.configureEach {
                        mustRunAfter(copyTask)
                    }
                }
            }
        }
    }
}

private fun KotlinMultiplatformExtension.configureIosResourceBundling(
    bundleTask: TaskProvider<ResourceSourceBundledFilesGeneratorTask>,
    resourceConfiguration: ResourceConfiguration
) {
    // For ios we will generate some wrapper files that bundle all resources
    // this extension is later used to copy the resources into the framework
    // as ios does not have a resource merging mechanism like android

    sourceSets.named("iosMain").configure {
        val filesTask =
            tasks.register<ResourceSourceWrapperGeneratorTask>("generate${resourceConfiguration.name}IosResourcesWrapperFiles") {
                group = "ogAppKit"
                description = "Generates wrapper that bundles all resources for ios"
                resourceFiles.set(resourceConfiguration)
                outputFile.set(
                    layout.buildDirectory.dir("generated/source/ogresources/iosMain")
                )
            }
        bundleTask.configure {
            fileWrapperSources.srcDirs(filesTask)
        }
        kotlin.srcDirs(bundleTask)
    }
}

private fun KotlinMultiplatformExtension.getOrRegisterIOSBundleTask(extension: ResourceExtension): TaskProvider<ResourceSourceBundledFilesGeneratorTask> {
    val taskName = "generateIosResourcesBundles"

    // Try to find existing task or register a new one
    return try {
        tasks.named<ResourceSourceBundledFilesGeneratorTask>(taskName)
    } catch (e: Exception) {
        // Task doesn't exist yet, register a new one
        tasks.register<ResourceSourceBundledFilesGeneratorTask>(taskName) {
            group = "ogAppKit"
            description = "Generates wrapper class that bundles all resources for ios"
            outputFile.set(
                layout.buildDirectory.dir("generated/source/ogresources/iosMain")
            )
        }
    }
}
