plugins {
    id("ogAppKit.libraryModule")
    id("co.touchlab.kmmbridge")
    id("co.touchlab.skie")
}

skie {
    analytics {
        disableUpload.set(true)
        enabled.set(false)
    }
    build {
        produceDistributableFramework()
    }
}

@Suppress("PropertyName")
val FRAMEWORK_NAME: String by properties
kmmbridge {
    frameworkName.set(FRAMEWORK_NAME)
    mavenPublishArtifacts()
//    githubReleaseVersions()
//    Android version is not automatically incremented in KMMBridge, so if you need it to be aligned with the iOS version
//    use manualVersions instead of githubReleaseVersions to set version manually
//    manualVersions()
    spm(swiftToolVersion = "5.8")
}
