import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction
import org.gradle.kotlin.dsl.property

abstract class BuildConfigTask : DefaultTask() {
    init {
        group = "ogappkit"
        description = "Creates a BuildConfig file with the current version"
    }

    @Input
    open val version: Property<String> = project.objects.property<String>().convention(
        project.version.toString()
    )

    @OutputDirectory
    open val outputDirectory: DirectoryProperty = project.objects.directoryProperty().convention(
        project.layout.buildDirectory.dir("generated/src/buildConfig")
    )

    @TaskAction
    fun run() {
        val file = outputDirectory.file("BuildConfig.kt").get().asFile
        file.writeText(
            """
            package com.ottogroup.appkit

            public object BuildConfig {
                public const val VERSION: String = "${version.get()}"
            }
            """.trimIndent()
        )
    }
}
