package com.ottogroup.appkit.resources

import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.SourceDirectorySet
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFiles
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction

/**
 * Task to generate a Kotlin file with a map of all bundled files.
 * Bundles files can be added via the `resourceBundles` source set.
 * This is a workaround to be able to access bundled files in a multiplatform project ony ios target.
 */
abstract class ResourceSourceBundledFilesGeneratorTask : DefaultTask() {
    @get:InputFiles
    val fileWrapperSources: SourceDirectorySet = project.objects.sourceDirectorySet("resourceBundles", "resourceBundles")

    @get:OutputDirectory
    abstract val outputFile: DirectoryProperty

    private val excludedFiles = listOf("FileWrapper.kt", "BundledFiles.kt")

    @TaskAction
    fun execute() {
        outputFile.get().file("BundledFiles.kt").asFile.writeText(
            """
            package com.ottogroup.appkit.resources

            public object BundledFiles {
                private val files by lazy {
                    listOf(${
                fileWrapperSources.files.filterNot { excludedFiles.contains(it.name) }
                    .joinToString { it.nameWithoutExtension }
            }).associateBy({ it.path })
                }

                public fun getFiles(): Map<String, FileWrapper> {
                    return files
                }

                public fun getFiles(path: String): Map<String, FileWrapper> {
                    return files.filterKeys { it.startsWith(path) }
                        .mapKeys { it.key.removePrefix(path).removePrefix("/") }
                }

                public fun getFile(path: String): FileWrapper? {
                    return files[path]
                }

                public fun getFileContent(path: String): ByteArray? {
                   return files[path]?.content
                }
            }
        """.trimIndent()
        )
    }
}
