package com.ottogroup.appkit.resources

import javax.inject.Inject
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.file.ConfigurableFileCollection
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFiles
import org.gradle.api.tasks.SkipWhenEmpty

public abstract class ResourceExtension{
    abstract val resources: NamedDomainObjectContainer<ResourceConfiguration>
}

public abstract class ResourceConfiguration
@Inject constructor(
    @get:Input
    val name: String
) {

    @get:InputFiles
    @get:SkipWhenEmpty
    abstract val from: ConfigurableFileCollection
    @get:Input
    abstract val destPath: Property<String>
}
