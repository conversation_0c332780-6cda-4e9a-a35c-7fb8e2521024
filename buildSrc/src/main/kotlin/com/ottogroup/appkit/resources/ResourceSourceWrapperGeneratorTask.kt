package com.ottogroup.appkit.resources

import java.io.File
import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.api.provider.Provider
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Internal
import org.gradle.api.tasks.Nested
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction

/**
 * Task to generate file wrappers.
 * These wrappers are used to access the content of the files in the resources and will be included via the ResourceSourceBundledFilesGeneratorTask.
 */
abstract class ResourceSourceWrapperGeneratorTask : DefaultTask() {

    @get:Nested
    abstract val resourceFiles: Property<ResourceConfiguration>

    @get:OutputDirectory
    abstract val outputFile: DirectoryProperty

    private fun File.nestedFilePath(parent: String): List<Pair<String, ByteArray>> {
        val path = "${if (parent.isEmpty()) "" else "$parent/"}$name"
        return if (isDirectory) {
            listFiles()?.flatMap {
                it.nestedFilePath(path)
            } ?: emptyList()
        } else {
            listOf(path to readBytes())
        }
    }

    @TaskAction
    fun execute() {
        val files = resourceFiles.get().let { resource ->
            resource.from.filter { it.exists() }
                .flatMap { it.nestedFilePath(resource.destPath.get()) }
        }

        outputFile.get().file("FileWrapper.kt").asFile.writeText(
            """
            package com.ottogroup.appkit.resources

            public data class FileWrapper(
                val path: String,
                val content: ByteArray
            )

        """.trimIndent()
        )

        val filesMap = files.associate { (path, content) ->
            val name = path.toPropertyName()
            val bytesString = content.joinToString(", ") { it.toString() }
            name to "internal val $name = FileWrapper(\"${path}\", byteArrayOf($bytesString))"
        }

        filesMap.forEach {
            outputFile.get().file("${it.key}.kt").asFile.writeText(
                """
                package com.ottogroup.appkit.resources

                ${it.value}
            """.trimIndent()
            )
        }

        println(
            "Generated files: ${
                outputFile.get().asFile.listFiles()?.joinToString(separator = "\n\t")
            }"
        )
    }

    private fun String.toPropertyName(): String {
        return this.replace(".", "_").replace("-", "_").replace("/", "_").replace(" ", "_")
    }
}
