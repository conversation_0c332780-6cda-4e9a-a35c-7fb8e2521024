import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget

plugins {
    kotlin("multiplatform")
    kotlin("plugin.serialization")
    id("com.android.library")
    id("ogAppKit.checks")
    `maven-publish`
}

addGithubPackagesRepository()

val fullGroup = run {
    val groupElements = listOf("com.ottogroup") +
        project.path.split(":", "-")
            .drop(1) // drop empty first element
            .dropLast(1) // this will be the artifactId, not the group
    groupElements.joinToString(".")
}

group = fullGroup

kotlin {
    jvmToolchain(21)
    explicitApi()

    targets.withType<KotlinNativeTarget> {
        compilerOptions{
            freeCompilerArgs.add("-Xexport-kdoc")
        }
    }

    androidTarget {
        publishAllLibraryVariants()
    }

    iosX64()
    iosArm64()
    iosSimulatorArm64()

    sourceSets {
        commonMain {
            dependencies {
                if (":ogappkit:base" !in project.path) {
                    implementation(project(":ogappkit:base:internal"))
                }
                implementation(libs.kotlinx.serialization)
                implementation(libs.kotlinx.coroutines)
                implementation(libs.koin.core)
                implementation(libs.skie.annotations)
                implementation(libs.kermit.koin)
                implementation(libs.kermit)
            }
        }
        commonTest {
            dependencies {
                implementation(libs.turbine)
                implementation(libs.kotlinx.coroutines.test)
                implementation(kotlin("test-common"))
                implementation(kotlin("test-annotations-common"))
            }
        }
        androidMain {
            dependencies {
                implementation(libs.koin.android)
            }
        }
        val androidUnitTest by getting
        androidUnitTest.dependencies {
            implementation(kotlin("test-junit"))
        }
        iosMain {
            dependencies {
            }
        }
    }
}

android {
    namespace = "$fullGroup.${project.name}"
    compileSdk = libs.versions.android.compileSdk.get().toInt()
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    defaultConfig {
        minSdk = libs.versions.android.minSdk.get().toInt()
        val consumerProguardFile = project.file("../proguard-consumer-rules.pro")
        if (consumerProguardFile.exists()) {
            consumerProguardFiles(consumerProguardFile)
        }
    }
}

tasks.withType<Test> {
    testLogging {
        exceptionFormat = TestExceptionFormat.FULL
        showStackTraces = true
        showStandardStreams = true
    }
}
